<?php
/**
 * Admin Session Analysis Tool
 * Scans all admin pages to identify session handling patterns
 * NO SESSION REQUIRED - DEBUG TOOL
 */

// No session or authentication required for this debug tool
error_reporting(E_ALL);
ini_set('display_errors', 1);

$page_title = 'Admin Session Analysis';

// Scan admin directory for PHP files
function scanAdminFiles($dir = '.') {
    $files = [];
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $relativePath = str_replace('\\', '/', $iterator->getSubPathName());
            $files[] = $relativePath;
        }
    }
    
    return $files;
}

// Analyze session patterns in a file
function analyzeSessionPattern($filePath) {
    if (!file_exists($filePath)) {
        return ['error' => 'File not found'];
    }
    
    $content = file_get_contents($filePath);
    $analysis = [
        'file' => $filePath,
        'has_require_admin' => strpos($content, 'requireAdmin()') !== false,
        'has_session_start' => strpos($content, 'session_start()') !== false,
        'has_config_include' => strpos($content, "require_once '../config/config.php'") !== false || 
                               strpos($content, "require_once '../../config/config.php'") !== false,
        'has_header_include' => strpos($content, 'admin-header.php') !== false,
        'has_debug_logging' => strpos($content, 'error_log') !== false,
        'session_pattern' => 'unknown'
    ];
    
    // Determine session pattern
    if ($analysis['has_require_admin'] && $analysis['has_config_include']) {
        $analysis['session_pattern'] = 'standard';
    } elseif ($analysis['has_session_start'] && !$analysis['has_require_admin']) {
        $analysis['session_pattern'] = 'custom';
    } elseif (!$analysis['has_require_admin'] && !$analysis['has_session_start']) {
        $analysis['session_pattern'] = 'none';
    } else {
        $analysis['session_pattern'] = 'mixed';
    }
    
    return $analysis;
}

// Get all admin files
$adminFiles = scanAdminFiles('.');

// Analyze each file
$analysis = [];
foreach ($adminFiles as $file) {
    $analysis[] = analyzeSessionPattern($file);
}

// Group by session pattern
$patterns = [
    'standard' => [],
    'custom' => [],
    'none' => [],
    'mixed' => []
];

foreach ($analysis as $fileAnalysis) {
    $patterns[$fileAnalysis['session_pattern']][] = $fileAnalysis;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .pattern-standard { background-color: #d4edda; }
        .pattern-custom { background-color: #fff3cd; }
        .pattern-none { background-color: #f8d7da; }
        .pattern-mixed { background-color: #d1ecf1; }
        .file-path { font-family: monospace; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <h1><i class="fas fa-search me-2"></i><?php echo $page_title; ?></h1>
        <p class="text-muted">Analysis of session handling patterns across all admin PHP files</p>

        <!-- Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card pattern-standard">
                    <div class="card-body text-center">
                        <h5 class="card-title">Standard Pattern</h5>
                        <h2 class="text-success"><?php echo count($patterns['standard']); ?></h2>
                        <small>requireAdmin() + config.php</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card pattern-custom">
                    <div class="card-body text-center">
                        <h5 class="card-title">Custom Pattern</h5>
                        <h2 class="text-warning"><?php echo count($patterns['custom']); ?></h2>
                        <small>session_start() only</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card pattern-none">
                    <div class="card-body text-center">
                        <h5 class="card-title">No Session</h5>
                        <h2 class="text-danger"><?php echo count($patterns['none']); ?></h2>
                        <small>No session handling</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card pattern-mixed">
                    <div class="card-body text-center">
                        <h5 class="card-title">Mixed Pattern</h5>
                        <h2 class="text-info"><?php echo count($patterns['mixed']); ?></h2>
                        <small>Inconsistent</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Analysis -->
        <?php foreach ($patterns as $patternName => $files): ?>
        <div class="card mb-4">
            <div class="card-header pattern-<?php echo $patternName; ?>">
                <h4 class="mb-0">
                    <i class="fas fa-<?php echo $patternName === 'standard' ? 'check' : ($patternName === 'none' ? 'times' : 'exclamation'); ?> me-2"></i>
                    <?php echo ucfirst($patternName); ?> Pattern (<?php echo count($files); ?> files)
                </h4>
            </div>
            <div class="card-body">
                <?php if (empty($files)): ?>
                    <p class="text-muted">No files found with this pattern.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>File</th>
                                    <th>requireAdmin()</th>
                                    <th>session_start()</th>
                                    <th>Config Include</th>
                                    <th>Header Include</th>
                                    <th>Debug Logging</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($files as $file): ?>
                                <tr>
                                    <td class="file-path"><?php echo htmlspecialchars($file['file']); ?></td>
                                    <td><?php echo $file['has_require_admin'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'; ?></td>
                                    <td><?php echo $file['has_session_start'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'; ?></td>
                                    <td><?php echo $file['has_config_include'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'; ?></td>
                                    <td><?php echo $file['has_header_include'] ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'; ?></td>
                                    <td><?php echo $file['has_debug_logging'] ? '<i class="fas fa-check text-warning"></i>' : '<i class="fas fa-times text-muted"></i>'; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endforeach; ?>

        <!-- Recommendations -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Recommendations</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>✅ Good Practices Found:</h5>
                        <ul>
                            <li><strong><?php echo count($patterns['standard']); ?> files</strong> use standard requireAdmin() pattern</li>
                            <li>Most files include proper config.php</li>
                            <li>Header includes are consistent</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>⚠️ Issues to Fix:</h5>
                        <ul>
                            <?php if (count($patterns['custom']) > 0): ?>
                            <li><strong><?php echo count($patterns['custom']); ?> files</strong> use custom session handling</li>
                            <?php endif; ?>
                            <?php if (count($patterns['none']) > 0): ?>
                            <li><strong><?php echo count($patterns['none']); ?> files</strong> have no session protection</li>
                            <?php endif; ?>
                            <?php if (count($patterns['mixed']) > 0): ?>
                            <li><strong><?php echo count($patterns['mixed']); ?> files</strong> have inconsistent patterns</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
