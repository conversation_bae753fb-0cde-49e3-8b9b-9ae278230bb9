<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid deposit ID']);
    exit;
}

$deposit_id = intval($_GET['id']);

try {
    $db = getDB();
    
    // Get deposit details with user information and wallet details
    $query = "SELECT cd.*, 
                     a.first_name, a.last_name, a.username, a.email, 
                     a.account_number, a.phone,
                     admin.first_name as admin_first_name, 
                     admin.last_name as admin_last_name,
                     cw.wallet_address as user_wallet_address
              FROM crypto_deposits cd 
              LEFT JOIN accounts a ON cd.account_id = a.id 
              LEFT JOIN admin_users admin ON cd.reviewed_by = admin.id
              LEFT JOIN crypto_wallets cw ON cd.wallet_id = cw.id
              WHERE cd.id = ?";
    
    $stmt = $db->prepare($query);
    $stmt->bind_param("i", $deposit_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if (!($deposit = $result->fetch_assoc())) {
        http_response_code(404);
        echo json_encode(['error' => 'Deposit not found']);
        exit;
    }
    
    // Format the response
    $response = [
        'success' => true,
        'data' => [
            'user' => [
                'name' => trim($deposit['first_name'] . ' ' . $deposit['last_name']),
                'username' => $deposit['username'],
                'email' => $deposit['email'],
                'account_number' => $deposit['account_number'],
                'phone' => $deposit['phone']
            ],
            'deposit' => [
                'id' => $deposit['id'],
                'deposit_number' => $deposit['deposit_number'],
                'cryptocurrency' => strtoupper($deposit['cryptocurrency']),
                'deposit_amount' => $deposit['deposit_amount'],
                'usd_equivalent' => $deposit['usd_equivalent'],
                'exchange_rate' => $deposit['exchange_rate'],
                'status' => $deposit['status'],
                'admin_wallet_address' => $deposit['admin_wallet_address'],
                'user_wallet_address' => $deposit['user_wallet_address'],
                'transaction_hash' => $deposit['user_transaction_hash'],
                'receipt_file' => $deposit['receipt_file_path']
            ],
            'transaction' => [
                'created_at' => $deposit['created_at'],
                'updated_at' => $deposit['updated_at'],
                'reviewed_at' => $deposit['reviewed_at'],
                'credited_at' => $deposit['credited_at'],
                'reviewed_by' => $deposit['admin_first_name'] ? 
                    trim($deposit['admin_first_name'] . ' ' . $deposit['admin_last_name']) : null
            ],
            'admin_notes' => $deposit['admin_notes']
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch deposit details: ' . $e->getMessage()]);
}
?>