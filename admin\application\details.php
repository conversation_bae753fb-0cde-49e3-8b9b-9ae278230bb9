<?php
/**
 * IRS Application Details Page
 * Display detailed information about a specific IRS application
 */

// Set page variables
$page_title = 'Application Details';
$current_page = 'applications';

// Include database connection and authentication
require_once __DIR__ . '/../../config/config.php';

// Check admin authentication using proper function
requireAdmin();
require_once __DIR__ . '/../../config/dynamic-css.php';

$db = getDB();

// Get application ID from URL
$application_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$application_id) {
    header('Location: ../applications.php');
    exit();
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');
    
    if ($action === 'approve' || $action === 'decline') {
        $new_status = ($action === 'approve') ? 'approved' : 'declined';
        
        // Update application status
        $update_query = "UPDATE irs_applications SET status = ?, admin_notes = ?, reviewed_at = NOW(), reviewed_by = ? WHERE id = ?";
        $db->query($update_query, [$new_status, $admin_notes, $_SESSION['admin_id'], $application_id]);
        
        // Send email notification (include email functionality here)
        $success = "Application has been " . ($action === 'approve' ? 'approved' : 'declined') . " successfully.";
    }
}

// Get application details
$query = "SELECT a.*, acc.first_name as account_first_name, acc.last_name as account_last_name, acc.email as account_email 
          FROM irs_applications a 
          LEFT JOIN accounts acc ON a.account_id = acc.id 
          WHERE a.id = ?";
$result = $db->query($query, [$application_id]);
$application = $result->fetch_assoc();

if (!$application) {
    header('Location: ../applications.php');
    exit();
}

// Include encryption utilities
require_once __DIR__ . '/../../includes/encryption.php';

// Decrypt ID.me password for admin view
$decrypted_password = '';
if (!empty($application['idme_password'])) {
    // Check if password uses simple encryption
    if (isIdmePasswordEncrypted($application['idme_password'])) {
        // Decrypt using simple method
        $decrypted_password = decryptIdmePassword($application['idme_password']);
    } elseif (PasswordEncryption::isEncrypted($application['idme_password'])) {
        // Old complex encryption - try to decrypt
        try {
            $decrypted_password = PasswordEncryption::decrypt($application['idme_password']);
        } catch (Exception $e) {
            $decrypted_password = '[Complex Encryption - Cannot Decrypt]';
        }
    } elseif (PasswordEncryption::isBcryptHash($application['idme_password'])) {
        // Legacy hashed password - cannot be decrypted
        $decrypted_password = '[Legacy Hash - Cannot Decrypt]';
    } else {
        // Plain text password (shouldn't happen in production)
        $decrypted_password = $application['idme_password'];
    }
} else {
    $decrypted_password = 'Not provided';
}

// Include header
require_once '../includes/admin-header.php';
?>

<!-- Include Applications CSS -->
<link rel="stylesheet" href="../applications.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
    
    .application-details {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .detail-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .detail-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }
    
    .detail-row {
        display: flex;
        margin-bottom: 0.75rem;
        padding: 0.75rem;
        border-radius: 6px;
        transition: background-color 0.2s ease;
    }
    
    .detail-row:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .detail-row:nth-child(odd) {
        background-color: #ffffff;
    }
    
    .detail-row:hover {
        background-color: #e9ecef;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
        min-width: 200px;
        flex-shrink: 0;
    }
    
    .detail-value {
        color: #212529;
        flex: 1;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.875rem;
    }
    
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-approved {
        background-color: #d1edff;
        color: #0c5460;
    }
    
    .status-declined {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }
    
    .password-field {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .password-toggle {
        background: none;
        border: none;
        color: #007bff;
        cursor: pointer;
        padding: 0.25rem;
    }
</style>

        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1><i class="fas fa-file-alt me-3"></i>Application Details</h1>
                <p class="text-muted mb-0">Application #<?php echo htmlspecialchars($application['application_number']); ?></p>
            </div>
            <div>
                <a href="../applications.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Applications
                </a>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_GET['submitted']) && $_GET['submitted'] == '1'): ?>
        <div class="alert alert-success" role="alert">
            <div class="d-flex">
                <div><i class="fas fa-check-circle me-2"></i></div>
                <div>
                    <h4 class="alert-title">Application Submitted Successfully!</h4>
                    <div class="text-muted">The application has been submitted and is now pending review. Application Number: <?php echo htmlspecialchars($application['application_number']); ?></div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (isset($success)): ?>
        <div class="alert alert-success" role="alert">
            <div class="d-flex">
                <div><i class="fas fa-check-circle me-2"></i></div>
                <div>
                    <h4 class="alert-title">Success!</h4>
                    <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Application Status Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-info-circle me-2"></i>Application Status
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Status:</div>
                        <div class="detail-value">
                            <span class="status-badge status-<?php echo $application['status']; ?>">
                                <?php echo ucfirst($application['status']); ?>
                            </span>
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Application Number:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['application_number']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Applied Date:</div>
                        <div class="detail-value"><?php echo date('M d, Y g:i A', strtotime($application['created_at'])); ?></div>
                    </div>
                    <?php if ($application['reviewed_at']): ?>
                    <div class="detail-row">
                        <div class="detail-label">Reviewed Date:</div>
                        <div class="detail-value"><?php echo date('M d, Y g:i A', strtotime($application['reviewed_at'])); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Personal Information Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-user me-2"></i>Personal Information
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Full Name:</div>
                        <div class="detail-value">
                            <?php echo htmlspecialchars($application['first_name'] . ' ' . 
                                      ($application['middle_name'] ? $application['middle_name'] . ' ' : '') . 
                                      $application['last_name']); ?>
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">SSN:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['ssn']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Date of Birth:</div>
                        <div class="detail-value"><?php echo date('M d, Y', strtotime($application['date_of_birth'])); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Phone Number:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['phone_number']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Email:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['email']); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Address Information Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-map-marker-alt me-2"></i>Address Information
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Street Address:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['street_address']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">City:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['city']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">State:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['state']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">ZIP Code:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['zip_code']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Country:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['country'] ?? 'United States'); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tax Information Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-calculator me-2"></i>Tax Information
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Tax Year:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['tax_year']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Filing Status:</div>
                        <div class="detail-value"><?php echo ucwords(str_replace('_', ' ', $application['filing_status'])); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Annual Income:</div>
                        <div class="detail-value">$<?php echo number_format($application['annual_income'], 2); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Employment Type:</div>
                        <div class="detail-value"><?php echo ucwords(str_replace('_', ' ', $application['employment_type'])); ?></div>
                    </div>
                    <?php if ($application['employer_name']): ?>
                    <div class="detail-row">
                        <div class="detail-label">Employer Name:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['employer_name']); ?></div>
                    </div>
                    <?php endif; ?>
                    <?php if ($application['employer_ein']): ?>
                    <div class="detail-row">
                        <div class="detail-label">Employer EIN:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['employer_ein']); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Banking Information Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-university me-2"></i>Banking Information
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Bank Account Number:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['bank_account_number']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Routing Number:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['routing_number']); ?></div>
                    </div>
                    <?php if ($application['previous_year_agi']): ?>
                    <div class="detail-row">
                        <div class="detail-label">Previous Year AGI:</div>
                        <div class="detail-value">$<?php echo number_format($application['previous_year_agi'], 2); ?></div>
                    </div>
                    <?php endif; ?>
                    <?php if ($application['estimated_refund']): ?>
                    <div class="detail-row">
                        <div class="detail-label">Estimated Refund:</div>
                        <div class="detail-value">$<?php echo number_format($application['estimated_refund'], 2); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- ID.me Authentication Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-shield-alt me-2"></i>ID.me Authentication
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">ID.me Email:</div>
                        <div class="detail-value"><?php echo htmlspecialchars($application['idme_email']); ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">ID.me Password:</div>
                        <div class="detail-value">
                            <div class="password-field">
                                <span id="passwordDisplay" style="display: none;"><?php echo htmlspecialchars($decrypted_password); ?></span>
                                <span id="passwordHidden">••••••••••••</span>
                                <button type="button" class="password-toggle" onclick="togglePassword()">
                                    <i id="passwordIcon" class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Verification Status:</div>
                        <div class="detail-value"><?php echo ucwords(str_replace('_', ' ', $application['idme_verification_status'])); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dependents Information Card -->
        <?php if ($application['number_of_dependents'] > 0): ?>
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-users me-2"></i>Dependents Information
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Number of Dependents:</div>
                        <div class="detail-value"><?php echo $application['number_of_dependents']; ?></div>
                    </div>
                    <?php if ($application['dependents_info']): ?>
                        <?php $dependents = json_decode($application['dependents_info'], true); ?>
                        <?php foreach ($dependents as $index => $dependent): ?>
                        <div class="detail-row">
                            <div class="detail-label">Dependent <?php echo $index + 1; ?>:</div>
                            <div class="detail-value">
                                <?php echo htmlspecialchars($dependent['name']); ?>
                                <?php if ($dependent['relationship']): ?>
                                    (<?php echo htmlspecialchars($dependent['relationship']); ?>)
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Additional Information Card -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-info-circle me-2"></i>Additional Information
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Foreign Income:</div>
                        <div class="detail-value"><?php echo $application['has_foreign_income'] ? 'Yes' : 'No'; ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Business Income:</div>
                        <div class="detail-value"><?php echo $application['has_business_income'] ? 'Yes' : 'No'; ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Rental Income:</div>
                        <div class="detail-value"><?php echo $application['has_rental_income'] ? 'Yes' : 'No'; ?></div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-label">Investment Income:</div>
                        <div class="detail-value"><?php echo $application['has_investment_income'] ? 'Yes' : 'No'; ?></div>
                    </div>
                    <?php if ($application['special_circumstances']): ?>
                    <div class="detail-row">
                        <div class="detail-label">Special Circumstances:</div>
                        <div class="detail-value"><?php echo nl2br(htmlspecialchars($application['special_circumstances'])); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Admin Notes Card -->
        <?php if ($application['admin_notes']): ?>
        <div class="card mb-4">
            <div class="card-body">
                <div class="detail-section">
                    <div class="section-title">
                        <i class="fas fa-sticky-note me-2"></i>Admin Notes
                    </div>
                    <div class="detail-row">
                        <div class="detail-value"><?php echo nl2br(htmlspecialchars($application['admin_notes'])); ?></div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Action Buttons Card -->
        <?php if ($application['status'] === 'pending'): ?>
        <div class="card mb-4">
            <div class="card-body">
                <div class="action-buttons">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="approve">
                        <div class="mb-3">
                            <label for="admin_notes_approve" class="form-label">Admin Notes (Optional)</label>
                            <textarea class="form-control" id="admin_notes_approve" name="admin_notes" rows="3" 
                                      placeholder="Add any notes for the applicant..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>Approve Application
                        </button>
                    </form>
                    
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="decline">
                        <div class="mb-3">
                            <label for="admin_notes_decline" class="form-label">Reason for Decline</label>
                            <textarea class="form-control" id="admin_notes_decline" name="admin_notes" rows="3" 
                                      placeholder="Please provide a reason for declining..." required></textarea>
                        </div>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times me-2"></i>Decline Application
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <?php endif; ?>

<script>
function togglePassword() {
    const passwordDisplay = document.getElementById('passwordDisplay');
    const passwordHidden = document.getElementById('passwordHidden');
    const passwordIcon = document.getElementById('passwordIcon');
    
    if (passwordDisplay.style.display === 'none') {
        passwordDisplay.style.display = 'inline';
        passwordHidden.style.display = 'none';
        passwordIcon.className = 'fas fa-eye-slash';
    } else {
        passwordDisplay.style.display = 'none';
        passwordHidden.style.display = 'inline';
        passwordIcon.className = 'fas fa-eye';
    }
}
</script>

<?php include '../includes/admin-footer.php'; ?>