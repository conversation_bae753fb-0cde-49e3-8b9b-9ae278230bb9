<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'IRS Applications';

// Handle application approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        $application_id = intval($_POST['application_id']);
        $action = $_POST['action'];
        $admin_notes = trim($_POST['admin_notes'] ?? '');
        
        if (!in_array($action, ['approve', 'decline'])) {
            throw new Exception("Invalid action.");
        }
        
        // Get application details
        $app_query = "SELECT ia.*, a.first_name, a.last_name, a.email 
                      FROM irs_applications ia 
                      LEFT JOIN accounts a ON ia.account_id = a.id 
                      WHERE ia.id = ? AND ia.status = 'pending'";
        $app_result = $db->query($app_query, [$application_id]);
        $application = $app_result->fetch_assoc();
        
        if (!$application) {
            throw new Exception("Application not found or already processed.");
        }
        
        $new_status = $action === 'approve' ? 'approved' : 'declined';
        
        // Update application status
        $update_query = "UPDATE irs_applications SET 
                        status = ?, admin_notes = ?, reviewed_by = ?, reviewed_at = NOW() 
                        WHERE id = ?";
        $db->query($update_query, [$new_status, $admin_notes, $_SESSION['user_id'], $application_id]);
        
        // Send email notification using template system
        require_once '../config/email_templates.php';
        require_once '../config/email.php';

        $user_data = [
            'first_name' => $application['first_name'],
            'last_name' => $application['last_name'],
            'email' => $application['email']
        ];

        $application_data = [
            'application_number' => $application['application_number'],
            'tax_year' => $application['tax_year'],
            'filing_status' => $application['filing_status']
        ];

        if ($action === 'approve') {
            $email_subject = "IRS Application Approved - Application #{$application['application_number']}";
            $email_body = generateIRSApprovalEmailTemplate($user_data, $application_data);
        } else {
            $email_subject = "IRS Application Update - Application #{$application['application_number']}";
            $email_body = generateIRSDeclineEmailTemplate($user_data, $application_data, $admin_notes);
        }

        // Send the email
        $email_sent = sendEmail($application['email'], $email_subject, $email_body, true);

        // Log email notification
        $email_insert = "INSERT INTO email_notifications (
            recipient_id, email_address, subject, message_body, notification_type,
            related_record_id, related_record_type, status
        ) VALUES (?, ?, ?, ?, ?, ?, 'irs_application', ?)";

        $db->query($email_insert, [
            $application['account_id'],
            $application['email'],
            $email_subject,
            $email_body,
            $action === 'approve' ? 'irs_approval' : 'irs_decline',
            $application_id,
            $email_sent ? 'sent' : 'failed'
        ]);
        
        $success = "Application " . ($action === 'approve' ? 'approved' : 'declined') . " successfully!";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get applications with pagination
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

$filter_status = $_GET['status'] ?? '';
$where_conditions = [];
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "ia.status = ?";
    $params[] = $filter_status;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count
    $count_query = "SELECT COUNT(*) as total FROM irs_applications ia $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get applications
    $apps_query = "SELECT ia.*, 
                   a.first_name, a.last_name, a.username, a.account_number,
                   admin.first_name as admin_first_name, admin.last_name as admin_last_name
                   FROM irs_applications ia 
                   LEFT JOIN accounts a ON ia.account_id = a.id 
                   LEFT JOIN accounts admin ON ia.reviewed_by = admin.id 
                   $where_clause
                   ORDER BY ia.created_at DESC 
                   LIMIT $records_per_page OFFSET $offset";
    
    $apps_result = $db->query($apps_query, $params);
    $applications = [];
    while ($row = $apps_result->fetch_assoc()) {
        $applications[] = $row;
    }
    
    // Get statistics
    $stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = 'declined' THEN 1 ELSE 0 END) as declined
                    FROM irs_applications";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    $error = "Failed to load applications: " . $e->getMessage();
    $applications = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'pending' => 0, 'approved' => 0, 'declined' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">IRS Applications</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total']); ?></div>
                        <div class="text-muted">Total Applications</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending']); ?></div>
                        <div class="text-muted">Pending Review</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['approved']); ?></div>
                        <div class="text-muted">Approved</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-times"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['declined']); ?></div>
                        <div class="text-muted">Declined</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Application Filters
                </h3>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="approved" <?php echo $filter_status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                            <option value="declined" <?php echo $filter_status === 'declined' ? 'selected' : ''; ?>>Declined</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="applications.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Applications Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-alt me-2"></i>
                    IRS Applications
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($applications)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Applicant</th>
                                <th>Application #</th>
                                <th>Tax Year</th>
                                <th>Annual Income</th>
                                <th>Status</th>
                                <th>Applied Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($applications as $app):
                            ?>
                            <tr>
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2 bg-primary text-white">
                            <?php echo strtoupper(substr($app['first_name'] ?? 'U', 0, 1) . substr($app['last_name'] ?? 'U', 0, 1)); ?>
                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars(($app['first_name'] ?? 'Unknown') . ' ' . ($app['last_name'] ?? 'User')); ?></div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($app['username'] ?? 'unknown'); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    <span class="badge bg-white text-dark border badge-sm">
                                        <?php echo htmlspecialchars($app['application_number']); ?>
                                    </span>
                                </td>

                                <td>
                                    <span class="fw-bold">
                                        <?php echo htmlspecialchars($app['tax_year']); ?>
                                    </span>
                                </td>

                                <td>
                                    <span class="fw-bold text-success">
                                        <?php echo formatCurrency($app['annual_income']); ?>
                                    </span>
                                </td>

                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'approved' => 'success',
                                        'declined' => 'danger'
                                    ];
                                    $status_color = $status_colors[$app['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($app['status']); ?>
                                    </span>
                                </td>

                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($app['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($app['created_at'])); ?></small>
                                    </div>
                                </td>

                                <td>
                                    <div class="btn-list">
                                        <a href="application/details.php?id=<?php echo $app['id']; ?>" class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($app['status'] === 'pending'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="reviewApplication(<?php echo $app['id']; ?>, 'approve')" title="Approve">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="reviewApplication(<?php echo $app['id']; ?>, 'decline')" title="Decline">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer d-flex align-items-center">
                    <p class="m-0 text-muted">
                        Showing <span><?php echo ($offset + 1); ?></span> to <span><?php echo min($offset + $records_per_page, $total_records); ?></span>
                        of <span><?php echo $total_records; ?></span> entries
                    </p>
                    <ul class="pagination m-0 ms-auto">
                        <?php if ($current_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($current_page - 1); ?><?php echo $filter_status ? '&status=' . urlencode($filter_status) : ''; ?>">
                                <i class="fas fa-chevron-left"></i> prev
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                        <li class="page-item <?php echo $i === $current_page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $filter_status ? '&status=' . urlencode($filter_status) : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($current_page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($current_page + 1); ?><?php echo $filter_status ? '&status=' . urlencode($filter_status) : ''; ?>">
                                next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-file-invoice-dollar" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No applications found</p>
                    <p class="empty-subtitle text-muted">
                        No IRS tax return applications have been submitted yet.
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Application Details Modal -->
<div class="modal modal-blur fade" id="applicationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-invoice-dollar me-2"></i>
                    Application Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="applicationDetails">
                <!-- Application details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Review Application Modal -->
<div class="modal modal-blur fade" id="reviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <form method="POST" action="">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewModalTitle">
                        <i class="fas fa-gavel me-2"></i>
                        Review Application
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="application_id" id="reviewApplicationId">
                    <input type="hidden" name="action" id="reviewAction">

                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" name="admin_notes" id="admin_notes" rows="4"
                                  placeholder="Enter notes about this decision..."></textarea>
                        <div class="form-text">
                            These notes will be included in the notification email to the applicant.
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div><i class="fas fa-info-circle me-2"></i></div>
                            <div>
                                <h4 class="alert-title">Notification</h4>
                                <div class="text-muted">
                                    An email notification will be automatically sent to the applicant informing them of your decision.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="reviewSubmitBtn">
                        <i class="fas fa-check me-2"></i>
                        Confirm Decision
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
/**
 * View application details
 */
function viewApplication(application) {
    const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
    const detailsContainer = document.getElementById('applicationDetails');

    // Parse dependents info
    let dependentsInfo = '';
    try {
        const dependents = JSON.parse(application.dependents_info || '[]');
        if (dependents.length > 0) {
            dependentsInfo = dependents.map((dep, index) => `
                <div class="mb-2">
                    <strong>Dependent ${index + 1}:</strong> ${dep.name || 'N/A'}
                    (${dep.relationship || 'N/A'}, DOB: ${dep.date_of_birth || 'N/A'})
                </div>
            `).join('');
        } else {
            dependentsInfo = '<div class="text-muted">No dependents</div>';
        }
    } catch (e) {
        dependentsInfo = '<div class="text-muted">No dependents</div>';
    }

    // Format status
    const statusColors = {
        'pending': 'warning',
        'approved': 'success',
        'declined': 'danger'
    };
    const statusColor = statusColors[application.status] || 'secondary';

    detailsContainer.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted mb-2">Personal Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${application.first_name} ${application.middle_name || ''} ${application.last_name}</td></tr>
                    <tr><td><strong>SSN:</strong></td><td>${application.ssn}</td></tr>
                    <tr><td><strong>Date of Birth:</strong></td><td>${formatDate(application.date_of_birth)}</td></tr>
                    <tr><td><strong>Phone:</strong></td><td>${application.phone_number}</td></tr>
                    <tr><td><strong>Email:</strong></td><td>${application.email}</td></tr>
                </table>

                <h6 class="text-muted mb-2 mt-3">Address</h6>
                <table class="table table-sm">
                    <tr><td><strong>Street:</strong></td><td>${application.street_address}</td></tr>
                    <tr><td><strong>City:</strong></td><td>${application.city}</td></tr>
                    <tr><td><strong>State:</strong></td><td>${application.state}</td></tr>
                    <tr><td><strong>ZIP:</strong></td><td>${application.zip_code}</td></tr>
                </table>
            </div>

            <div class="col-md-6">
                <h6 class="text-muted mb-2">Tax Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Tax Year:</strong></td><td>${application.tax_year}</td></tr>
                    <tr><td><strong>Filing Status:</strong></td><td>${application.filing_status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</td></tr>
                    <tr><td><strong>Annual Income:</strong></td><td>$${parseFloat(application.annual_income).toLocaleString()}</td></tr>
                    <tr><td><strong>Employment:</strong></td><td>${application.employment_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</td></tr>
                    ${application.employer_name ? `<tr><td><strong>Employer:</strong></td><td>${application.employer_name}</td></tr>` : ''}
                    ${application.employer_ein ? `<tr><td><strong>Employer EIN:</strong></td><td>${application.employer_ein}</td></tr>` : ''}
                </table>

                <h6 class="text-muted mb-2 mt-3">Banking Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Account #:</strong></td><td>${application.bank_account_number}</td></tr>
                    <tr><td><strong>Routing #:</strong></td><td>${application.routing_number}</td></tr>
                    ${application.previous_year_agi ? `<tr><td><strong>Previous AGI:</strong></td><td>$${parseFloat(application.previous_year_agi).toLocaleString()}</td></tr>` : ''}
                    ${application.estimated_refund ? `<tr><td><strong>Est. Refund:</strong></td><td>$${parseFloat(application.estimated_refund).toLocaleString()}</td></tr>` : ''}
                </table>

                <h6 class="text-muted mb-2 mt-3">ID.me Authentication</h6>
                <table class="table table-sm">
                    <tr><td><strong>Email:</strong></td><td>${application.idme_email || 'N/A'}</td></tr>
                    <tr><td><strong>Password:</strong></td><td>
                        <div class="input-group input-group-sm">
                            <input type="password" class="form-control" value="${application.idme_password || 'N/A'}" id="idme_pass_${application.id}" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('idme_pass_${application.id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td></tr>
                    <tr><td><strong>Verification Status:</strong></td><td>
                        <span class="badge bg-${application.idme_verification_status === 'verified' ? 'success' : 'warning'}">
                            ${application.idme_verification_status || 'pending'}
                        </span>
                    </td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-muted mb-2">Dependents</h6>
                <div class="border rounded p-2">
                    <strong>Number of Dependents:</strong> ${application.number_of_dependents || 0}<br>
                    ${dependentsInfo}
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <h6 class="text-muted mb-2">Additional Income Sources</h6>
                <div class="border rounded p-2">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" ${application.has_foreign_income ? 'checked' : ''} disabled>
                        <label class="form-check-label">Foreign Income</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" ${application.has_business_income ? 'checked' : ''} disabled>
                        <label class="form-check-label">Business Income</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" ${application.has_rental_income ? 'checked' : ''} disabled>
                        <label class="form-check-label">Rental Income</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" ${application.has_investment_income ? 'checked' : ''} disabled>
                        <label class="form-check-label">Investment Income</label>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <h6 class="text-muted mb-2">Application Status</h6>
                <div class="border rounded p-2">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span><strong>Status:</strong></span>
                        <span class="badge bg-${statusColor}">${application.status.charAt(0).toUpperCase() + application.status.slice(1)}</span>
                    </div>
                    <div><strong>Application #:</strong> ${application.application_number}</div>
                    <div><strong>Applied:</strong> ${formatDate(application.created_at)}</div>
                    ${application.reviewed_at ? `<div><strong>Reviewed:</strong> ${formatDate(application.reviewed_at)}</div>` : ''}
                    ${application.admin_first_name ? `<div><strong>Reviewed by:</strong> ${application.admin_first_name} ${application.admin_last_name}</div>` : ''}
                </div>
            </div>
        </div>

        ${application.special_circumstances ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-muted mb-2">Special Circumstances</h6>
                <div class="border rounded p-2">
                    ${application.special_circumstances}
                </div>
            </div>
        </div>
        ` : ''}

        ${application.admin_notes ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-muted mb-2">Admin Notes</h6>
                <div class="border rounded p-2">
                    ${application.admin_notes}
                </div>
            </div>
        </div>
        ` : ''}
    `;

    modal.show();
}

/**
 * Review application (approve/decline)
 */
function reviewApplication(applicationId, action) {
    const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
    const title = document.getElementById('reviewModalTitle');
    const submitBtn = document.getElementById('reviewSubmitBtn');
    const notesField = document.getElementById('admin_notes');

    document.getElementById('reviewApplicationId').value = applicationId;
    document.getElementById('reviewAction').value = action;

    if (action === 'approve') {
        title.innerHTML = '<i class="fas fa-check me-2"></i>Approve Application';
        submitBtn.className = 'btn btn-success';
        submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Approve Application';
        notesField.placeholder = 'Enter approval notes (optional)...';
    } else {
        title.innerHTML = '<i class="fas fa-times me-2"></i>Decline Application';
        submitBtn.className = 'btn btn-danger';
        submitBtn.innerHTML = '<i class="fas fa-times me-2"></i>Decline Application';
        notesField.placeholder = 'Enter reason for decline (required)...';
        notesField.required = true;
    }

    modal.show();
}

/**
 * Format date helper
 */
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Toggle password visibility
 */
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
    }
}
</script>

<?php include 'includes/admin-footer.php'; ?>
