<?php
/**
 * Check Admin Users Table Structure
 * NO SESSION REQUIRED - DEBUG TOOL
 */

session_start();
require_once '../config/config.php';

echo "<h1>🔍 Admin Users Table Structure</h1>";

try {
    $db = getDB();
    
    // Check table structure
    echo "<h3>Admin Users Table Columns:</h3>";
    $columns = $db->query("DESCRIBE admin_users")->fetchAll();
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check actual data
    echo "<h3>Admin Users Data:</h3>";
    $users = $db->query("SELECT * FROM admin_users LIMIT 5")->fetchAll();
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr>";
        foreach (array_keys($users[0]) as $key) {
            if (!is_numeric($key)) {
                echo "<th>$key</th>";
            }
        }
        echo "</tr>";
        foreach ($users as $user) {
            echo "<tr>";
            foreach ($user as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No admin users found.";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
