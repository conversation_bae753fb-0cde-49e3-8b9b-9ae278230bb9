<?php
/**
 * Debug Script for Transfers Logout Issue
 * This script helps diagnose why transfers pages are causing logout
 */

require_once '../config/config.php';
requireAdmin();

$page_title = 'Debug Transfers Logout Issue';

// Get session information
$session_info = [
    'session_id' => session_id(),
    'user_id' => $_SESSION['user_id'] ?? 'not_set',
    'username' => $_SESSION['username'] ?? 'not_set',
    'is_admin' => $_SESSION['is_admin'] ?? 'not_set',
    'is_admin_session' => $_SESSION['is_admin_session'] ?? 'not_set',
    'last_activity' => $_SESSION['last_activity'] ?? 'not_set',
    'current_time' => time(),
    'session_timeout' => SESSION_TIMEOUT,
    'time_since_activity' => isset($_SESSION['last_activity']) ? (time() - $_SESSION['last_activity']) : 'no_activity'
];

// Test database connection
$db_status = 'Unknown';
try {
    $db = getDB();
    $test_query = $db->query("SELECT 1 as test");
    if ($test_query) {
        $db_status = 'Connected';
    } else {
        $db_status = 'Query Failed';
    }
} catch (Exception $e) {
    $db_status = 'Error: ' . $e->getMessage();
}

// Test transfers table access
$transfers_status = 'Unknown';
try {
    $db = getDB();
    $transfers_query = $db->query("SELECT COUNT(*) as count FROM local_transfers LIMIT 1");
    if ($transfers_query) {
        $transfers_status = 'Accessible';
    } else {
        $transfers_status = 'Query Failed';
    }
} catch (Exception $e) {
    $transfers_status = 'Error: ' . $e->getMessage();
}

include 'includes/admin-header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bug me-2"></i>Debug: Transfers Logout Issue
                    </h3>
                </div>
                <div class="card-body">
                    
                    <!-- Session Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Session Information</h5>
                            <table class="table table-sm">
                                <?php foreach ($session_info as $key => $value): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($key); ?>:</strong></td>
                                    <td><?php echo htmlspecialchars($value); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>System Status</h5>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Database Connection:</strong></td>
                                    <td><span class="badge bg-<?php echo $db_status === 'Connected' ? 'success' : 'danger'; ?>"><?php echo htmlspecialchars($db_status); ?></span></td>
                                </tr>
                                <tr>
                                    <td><strong>Transfers Table:</strong></td>
                                    <td><span class="badge bg-<?php echo $transfers_status === 'Accessible' ? 'success' : 'danger'; ?>"><?php echo htmlspecialchars($transfers_status); ?></span></td>
                                </tr>
                                <tr>
                                    <td><strong>Session Timeout:</strong></td>
                                    <td><?php echo SESSION_TIMEOUT; ?> seconds (<?php echo round(SESSION_TIMEOUT/60, 1); ?> minutes)</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Test Links -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Test Navigation</h5>
                            <p>Click these links to test the logout issue:</p>
                            <div class="btn-group" role="group">
                                <a href="transfers.php" class="btn btn-primary">Go to Transfers</a>
                                <a href="transfers/view.php?id=1&type=local-bank" class="btn btn-secondary">View Transfer (if exists)</a>
                                <a href="transfers/edit.php?id=1&type=local-bank" class="btn btn-warning">Edit Transfer (if exists)</a>
                                <a href="index.php" class="btn btn-success">Back to Dashboard</a>
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>How to Use This Debug Tool:</h6>
                        <ol>
                            <li>Note the current session information above</li>
                            <li>Click "Go to Transfers" and check if you get logged out</li>
                            <li>If you stay logged in, try clicking "View Transfer" or "Edit Transfer"</li>
                            <li>Then click "Go to Transfers" again to see if that causes logout</li>
                            <li>Check your error logs at <code>logs/error.log</code> for debug messages</li>
                        </ol>
                    </div>

                    <!-- Log Viewer -->
                    <div class="row">
                        <div class="col-12">
                            <h5>Recent Error Log Entries</h5>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;">
                                <pre style="margin: 0; font-size: 12px;"><?php
                                $log_file = '../logs/error.log';
                                if (file_exists($log_file)) {
                                    $log_content = file_get_contents($log_file);
                                    $log_lines = explode("\n", $log_content);
                                    $recent_lines = array_slice($log_lines, -50); // Last 50 lines
                                    echo htmlspecialchars(implode("\n", $recent_lines));
                                } else {
                                    echo "Error log file not found at: $log_file";
                                }
                                ?></pre>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
