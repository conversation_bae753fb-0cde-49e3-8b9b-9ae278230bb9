<?php
require_once '../config/config.php';

// Start output buffering to capture any output
ob_start();

// Debug session information
?>
<!DOCTYPE html>
<html>
<head>
    <title>Session Debug Information</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
        .debug-info { background: #e8f4fd; padding: 10px; margin: 5px 0; }
        .debug-warning { background: #fff3cd; padding: 10px; margin: 5px 0; }
        .debug-error { background: #f8d7da; padding: 10px; margin: 5px 0; }
        pre { background: #f4f4f4; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Session Debug Information</h1>
    
    <div class="debug-section">
        <h2>Current Time & Session Timeout</h2>
        <div class="debug-info">
            <strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
            <strong>Current Timestamp:</strong> <?php echo time(); ?><br>
            <strong>SESSION_TIMEOUT:</strong> <?php echo defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT . ' seconds (' . (SESSION_TIMEOUT/60) . ' minutes)' : 'NOT DEFINED'; ?><br>
        </div>
    </div>

    <div class="debug-section">
        <h2>Session Status</h2>
        <div class="debug-info">
            <strong>Session ID:</strong> <?php echo session_id(); ?><br>
            <strong>Session Status:</strong> <?php echo session_status() === PHP_SESSION_ACTIVE ? 'ACTIVE' : 'INACTIVE'; ?><br>
            <strong>Session Save Path:</strong> <?php echo session_save_path(); ?><br>
        </div>
    </div>

    <div class="debug-section">
        <h2>Session Variables</h2>
        <pre><?php print_r($_SESSION); ?></pre>
    </div>

    <div class="debug-section">
        <h2>Authentication Status</h2>
        <?php
        $isLoggedIn = isLoggedIn();
        $isAdminLoggedIn = isAdminLoggedIn();
        ?>
        <div class="debug-info">
            <strong>isLoggedIn():</strong> <?php echo $isLoggedIn ? 'TRUE' : 'FALSE'; ?><br>
            <strong>isAdminLoggedIn():</strong> <?php echo $isAdminLoggedIn ? 'TRUE' : 'FALSE'; ?><br>
        </div>
    </div>

    <div class="debug-section">
        <h2>Session Timeout Check</h2>
        <?php
        if (isset($_SESSION['last_activity'])) {
            $lastActivity = $_SESSION['last_activity'];
            $currentTime = time();
            $timeSinceLastActivity = $currentTime - $lastActivity;
            $timeoutThreshold = defined('SESSION_TIMEOUT') ? SESSION_TIMEOUT : 1800;
            $timeRemaining = $timeoutThreshold - $timeSinceLastActivity;
            
            echo "<div class='debug-info'>";
            echo "<strong>Last Activity:</strong> " . date('Y-m-d H:i:s', $lastActivity) . "<br>";
            echo "<strong>Time Since Last Activity:</strong> {$timeSinceLastActivity} seconds<br>";
            echo "<strong>Timeout Threshold:</strong> {$timeoutThreshold} seconds<br>";
            echo "<strong>Time Remaining:</strong> {$timeRemaining} seconds<br>";
            
            if ($timeRemaining <= 0) {
                echo "</div><div class='debug-error'>";
                echo "<strong>SESSION EXPIRED!</strong> Session should be destroyed.";
            } elseif ($timeRemaining < 300) { // Less than 5 minutes
                echo "</div><div class='debug-warning'>";
                echo "<strong>WARNING:</strong> Session will expire soon!";
            }
            echo "</div>";
        } else {
            echo "<div class='debug-warning'>No last_activity timestamp found in session.</div>";
        }
        ?>
    </div>

    <div class="debug-section">
        <h2>Server Information</h2>
        <div class="debug-info">
            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
            <strong>Server Time:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
            <strong>Timezone:</strong> <?php echo date_default_timezone_get(); ?><br>
            <strong>User Agent:</strong> <?php echo $_SERVER['HTTP_USER_AGENT'] ?? 'Not set'; ?><br>
            <strong>Remote IP:</strong> <?php echo $_SERVER['REMOTE_ADDR'] ?? 'Not set'; ?><br>
        </div>
    </div>

    <div class="debug-section">
        <h2>Test Session Update</h2>
        <p>This page access should update the last_activity timestamp.</p>
        <p><a href="debug_session.php">Refresh this page</a> to see updated timestamps.</p>
        <p><a href="transfers.php">Go to Transfers</a> | <a href="transfers/view.php?id=1&type=inter-bank">Go to Transfer View</a></p>
    </div>

    <div class="debug-section">
        <h2>Recommendations</h2>
        <?php
        if (!defined('SESSION_TIMEOUT')) {
            echo "<div class='debug-error'>SESSION_TIMEOUT constant is not defined!</div>";
        }
        
        if (!isset($_SESSION['last_activity'])) {
            echo "<div class='debug-warning'>last_activity is not set in session. This could cause immediate timeouts.</div>";
        }
        
        if (session_status() !== PHP_SESSION_ACTIVE) {
            echo "<div class='debug-error'>Session is not active!</div>";
        }
        
        if (!$isAdminLoggedIn && isset($_SESSION['user_id'])) {
            echo "<div class='debug-warning'>User is logged in but not as admin. Check is_admin and is_admin_session flags.</div>";
        }
        ?>
    </div>
</body>
</html>