<?php
/**
 * DEEP SESSION DEBUG - NO AUTHENTICATION REQUIRED
 * This will trace exactly what's happening with sessions
 */

// Start session manually to debug
session_start();

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Don't require any authentication - just debug
?>
<!DOCTYPE html>
<html>
<head>
    <title>Deep Session Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid py-4">
        <h1>🔍 Deep Session Debug Analysis</h1>
        
        <div class="alert alert-warning">
            <strong>NO AUTHENTICATION REQUIRED</strong> - This is pure debugging
        </div>

        <!-- Raw Session Data -->
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h4>Raw Session Data</h4>
            </div>
            <div class="card-body">
                <pre><?php 
                echo "Session ID: " . session_id() . "\n";
                echo "Session Status: " . session_status() . "\n";
                echo "Session Save Path: " . session_save_path() . "\n";
                echo "Session Name: " . session_name() . "\n";
                echo "\nRaw \$_SESSION contents:\n";
                var_dump($_SESSION);
                ?></pre>
            </div>
        </div>

        <!-- Config File Analysis -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h4>Config File Analysis</h4>
            </div>
            <div class="card-body">
                <?php
                $config_path = '../config/config.php';
                if (file_exists($config_path)) {
                    echo "<h5>Config file exists: $config_path</h5>";
                    
                    // Try to include config and catch any errors
                    ob_start();
                    try {
                        include_once $config_path;
                        $config_output = ob_get_clean();
                        echo "<div class='alert alert-success'>Config loaded successfully</div>";
                        
                        // Test the functions
                        echo "<h6>Function Tests:</h6>";
                        echo "<ul>";
                        
                        if (function_exists('isAdminLoggedIn')) {
                            $admin_status = isAdminLoggedIn();
                            echo "<li>isAdminLoggedIn(): " . ($admin_status ? 'TRUE' : 'FALSE') . "</li>";
                        } else {
                            echo "<li>isAdminLoggedIn(): FUNCTION NOT FOUND</li>";
                        }
                        
                        if (function_exists('isLoggedIn')) {
                            $user_status = isLoggedIn();
                            echo "<li>isLoggedIn(): " . ($user_status ? 'TRUE' : 'FALSE') . "</li>";
                        } else {
                            echo "<li>isLoggedIn(): FUNCTION NOT FOUND</li>";
                        }
                        
                        if (defined('SESSION_TIMEOUT')) {
                            echo "<li>SESSION_TIMEOUT: " . SESSION_TIMEOUT . " seconds</li>";
                        } else {
                            echo "<li>SESSION_TIMEOUT: NOT DEFINED</li>";
                        }
                        
                        echo "</ul>";
                        
                        // Show any output from config
                        if (!empty($config_output)) {
                            echo "<h6>Config Output:</h6>";
                            echo "<pre>" . htmlspecialchars($config_output) . "</pre>";
                        }
                        
                    } catch (Exception $e) {
                        ob_end_clean();
                        echo "<div class='alert alert-danger'>Config Error: " . $e->getMessage() . "</div>";
                    } catch (Error $e) {
                        ob_end_clean();
                        echo "<div class='alert alert-danger'>Config Fatal Error: " . $e->getMessage() . "</div>";
                    }
                } else {
                    echo "<div class='alert alert-danger'>Config file not found: $config_path</div>";
                }
                ?>
            </div>
        </div>

        <!-- Database Connection Test -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-white">
                <h4>Database Connection Test</h4>
            </div>
            <div class="card-body">
                <?php
                try {
                    if (function_exists('getDB')) {
                        $db = getDB();
                        echo "<div class='alert alert-success'>Database connection successful</div>";
                        
                        // Test admin users table
                        $admin_query = $db->query("SELECT COUNT(*) as count FROM admin_users");
                        if ($admin_query) {
                            $admin_count = $admin_query->fetch()['count'];
                            echo "<p>Admin users in database: $admin_count</p>";
                        }
                        
                    } else {
                        echo "<div class='alert alert-danger'>getDB() function not found</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>

        <!-- Session File Check -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h4>Session File System Check</h4>
            </div>
            <div class="card-body">
                <?php
                $session_path = session_save_path();
                echo "<p><strong>Session Save Path:</strong> $session_path</p>";
                
                if (is_dir($session_path)) {
                    echo "<div class='alert alert-success'>Session directory exists</div>";
                    
                    if (is_writable($session_path)) {
                        echo "<div class='alert alert-success'>Session directory is writable</div>";
                    } else {
                        echo "<div class='alert alert-danger'>Session directory is NOT writable</div>";
                    }
                    
                    // List session files
                    $session_files = glob($session_path . '/sess_*');
                    echo "<p>Session files found: " . count($session_files) . "</p>";
                    
                    if (count($session_files) > 0) {
                        echo "<h6>Recent session files:</h6>";
                        echo "<ul>";
                        foreach (array_slice($session_files, -5) as $file) {
                            $mtime = filemtime($file);
                            $age = time() - $mtime;
                            echo "<li>" . basename($file) . " (age: {$age}s)</li>";
                        }
                        echo "</ul>";
                    }
                } else {
                    echo "<div class='alert alert-danger'>Session directory does not exist</div>";
                }
                ?>
            </div>
        </div>

        <!-- Manual Login Test -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4>Manual Session Test</h4>
            </div>
            <div class="card-body">
                <p>Let's manually set admin session variables and test:</p>
                
                <?php
                // Manually set session variables for testing
                $_SESSION['user_id'] = 999;
                $_SESSION['username'] = 'debug_admin';
                $_SESSION['is_admin'] = true;
                $_SESSION['is_admin_session'] = true;
                $_SESSION['last_activity'] = time();
                
                echo "<div class='alert alert-info'>Manually set session variables</div>";
                
                // Now test the functions again
                if (function_exists('isAdminLoggedIn')) {
                    $admin_status = isAdminLoggedIn();
                    echo "<p>After manual setup - isAdminLoggedIn(): " . ($admin_status ? 'TRUE' : 'FALSE') . "</p>";
                }
                
                if (function_exists('isLoggedIn')) {
                    $user_status = isLoggedIn();
                    echo "<p>After manual setup - isLoggedIn(): " . ($user_status ? 'TRUE' : 'FALSE') . "</p>";
                }
                ?>
                
                <div class="mt-3">
                    <a href="transfers.php" class="btn btn-primary">Test Transfers Page Now</a>
                    <a href="index.php" class="btn btn-success">Test Admin Dashboard</a>
                </div>
            </div>
        </div>

        <!-- Error Log Check -->
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h4>Recent Error Logs</h4>
            </div>
            <div class="card-body">
                <?php
                $log_files = [
                    '../logs/error.log',
                    '../error.log',
                    'error.log',
                    '/tmp/php_errors.log'
                ];
                
                foreach ($log_files as $log_file) {
                    if (file_exists($log_file)) {
                        echo "<h6>Log file: $log_file</h6>";
                        $log_content = file_get_contents($log_file);
                        $log_lines = explode("\n", $log_content);
                        $recent_lines = array_slice($log_lines, -20);
                        echo "<pre style='max-height: 200px; overflow-y: auto; font-size: 11px;'>";
                        echo htmlspecialchars(implode("\n", $recent_lines));
                        echo "</pre>";
                        break;
                    }
                }
                ?>
            </div>
        </div>

    </div>
</body>
</html>
