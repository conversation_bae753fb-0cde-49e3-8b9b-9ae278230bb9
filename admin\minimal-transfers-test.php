<?php
/**
 * MINIMAL TRANSFERS TEST - Replicate exact transfers.php logic
 * NO AUTHENTICATION - Pure debugging
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔬 Minimal Transfers Test</h1>";
echo "<div style='background: #f8f9fa; padding: 20px; font-family: monospace;'>";

// Step 1: Show current session
echo "<h3>Current Session State:</h3>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

// Step 2: Try to load config exactly like transfers.php
echo "<h3>Loading config.php (like transfers.php):</h3>";
try {
    require_once '../config/config.php';
    echo "✅ Config loaded<br>";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "<br>";
    exit;
}

// Step 3: Check what requireAdmin would do
echo "<h3>Testing requireAdmin logic:</h3>";

// First check isAdminLoggedIn
$isAdminLoggedIn = isAdminLoggedIn();
echo "isAdminLoggedIn(): " . ($isAdminLoggedIn ? 'TRUE ✅' : 'FALSE ❌') . "<br>";

if (!$isAdminLoggedIn) {
    echo "❌ Would redirect to login because isAdminLoggedIn() = false<br>";
    echo "<h4>Session Analysis:</h4>";
    echo "user_id: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'NOT SET') . "<br>";
    echo "username: " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'NOT SET') . "<br>";
    echo "is_admin: " . (isset($_SESSION['is_admin']) ? ($_SESSION['is_admin'] ? 'true' : 'false') : 'NOT SET') . "<br>";
    echo "is_admin_session: " . (isset($_SESSION['is_admin_session']) ? ($_SESSION['is_admin_session'] ? 'true' : 'false') : 'NOT SET') . "<br>";
    
    echo "<h4>Quick Fix - Set Admin Session:</h4>";
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['first_name'] = 'Admin';
    $_SESSION['last_name'] = 'User';
    $_SESSION['is_admin'] = true;
    $_SESSION['is_admin_session'] = true;
    $_SESSION['last_activity'] = time();
    
    echo "✅ Admin session variables set manually<br>";
    echo "isAdminLoggedIn() now: " . (isAdminLoggedIn() ? 'TRUE ✅' : 'FALSE ❌') . "<br>";
} else {
    echo "✅ Admin is logged in<br>";
}

// Step 4: Try database connection like transfers.php
echo "<h3>Testing Database Connection:</h3>";
try {
    $db = getDB();
    echo "✅ Database connected<br>";
    
    // Test the exact query from transfers.php
    $test_query = "SELECT COUNT(*) as count FROM local_transfers";
    $result = $db->query($test_query);
    if ($result) {
        $count = $result->fetch()['count'];
        echo "✅ Local transfers table accessible, count: $count<br>";
    }
    
    $test_query2 = "SELECT COUNT(*) as count FROM interbank_transfers";
    $result2 = $db->query($test_query2);
    if ($result2) {
        $count2 = $result2->fetch()['count'];
        echo "✅ Interbank transfers table accessible, count: $count2<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Step 5: Test admin header include
echo "<h3>Testing Admin Header Include:</h3>";
try {
    ob_start();
    include 'includes/admin-header.php';
    $header_output = ob_get_clean();
    echo "✅ Admin header loaded successfully<br>";
    echo "Header output length: " . strlen($header_output) . " characters<br>";
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ Admin header error: " . $e->getMessage() . "<br>";
}

echo "<h3>Final Test Links:</h3>";
echo "<a href='transfers.php' style='background: #dc3545; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🚨 Test Real Transfers Page</a><br>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>✅ Test Admin Dashboard</a><br>";

echo "<h3>Session After All Tests:</h3>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

echo "</div>";

// Add a simple form to manually trigger a login
echo "<div style='background: #fff3cd; padding: 20px; margin: 20px 0; border: 1px solid #ffeaa7;'>";
echo "<h3>🔧 Emergency Admin Login</h3>";
echo "<p>If you're still getting logged out, use this to force an admin session:</p>";
echo "<form method='post'>";
echo "<button type='submit' name='force_admin' style='background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer;'>Force Admin Session</button>";
echo "</form>";

if (isset($_POST['force_admin'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'emergency_admin';
    $_SESSION['first_name'] = 'Emergency';
    $_SESSION['last_name'] = 'Admin';
    $_SESSION['is_admin'] = true;
    $_SESSION['is_admin_session'] = true;
    $_SESSION['last_activity'] = time();
    
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
    echo "✅ Emergency admin session created! Now try: <a href='transfers.php'>transfers.php</a>";
    echo "</div>";
}

echo "</div>";
?>
