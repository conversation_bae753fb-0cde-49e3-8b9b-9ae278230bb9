<?php
/**
 * Session Fix Test - Test the corrected admin login
 * NO SESSION REQUIRED - DEBUG TOOL
 */

session_start();
require_once '../config/config.php';

echo "<h1>✅ Session Fix Test</h1>";
echo "<div style='background: #f8f9fa; padding: 20px; font-family: monospace;'>";

echo "<h3>🔧 What Was Fixed:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Database Column Issue:</strong> admin_users table uses 'full_name' not 'first_name'/'last_name'</li>";
echo "<li>✅ <strong>Login Files Updated:</strong> admin/login.php and admin/verify-otp.php now split full_name</li>";
echo "<li>✅ <strong>Avatar Colors Fixed:</strong> Beneficiary avatars now use primary color</li>";
echo "</ul>";

echo "<h3>📊 Current Session State:</h3>";
if (empty($_SESSION)) {
    echo "<p style='color: red;'>❌ No session data found</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Key</th><th>Value</th></tr>";
    foreach ($_SESSION as $key => $value) {
        echo "<tr><td>$key</td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
}

echo "<h3>🧪 Admin Login Test:</h3>";
if (function_exists('isAdminLoggedIn')) {
    $admin_status = isAdminLoggedIn();
    echo "<p>isAdminLoggedIn(): " . ($admin_status ? '✅ TRUE' : '❌ FALSE') . "</p>";
} else {
    echo "<p>❌ isAdminLoggedIn() function not found</p>";
}

echo "<h3>🔗 Test Links:</h3>";
echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔑 Admin Login</a><br>";
echo "<a href='transfers.php' style='background: #dc3545; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🚨 Test Transfers Page</a><br>";
echo "<a href='transfers/edit.php?id=1&type=local-bank' style='background: #ffc107; color: black; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🎨 Test Avatar Colors</a><br>";

echo "<h3>💡 Instructions:</h3>";
echo "<ol>";
echo "<li>Click 'Admin Login' and login with your admin credentials</li>";
echo "<li>After successful login, try 'Test Transfers Page'</li>";
echo "<li>Navigate: transfers → edit → transfers (should not logout)</li>";
echo "<li>Check 'Test Avatar Colors' to see the fixed beneficiary avatars</li>";
echo "</ol>";

echo "</div>";
?>
