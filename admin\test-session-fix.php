<?php
/**
 * Test Session Fix - NO AGGRESSIVE SESSION CHECKING
 * Simple test to verify admin session is working without logout issues
 */

require_once '../config/config.php';
requireAdmin();

$page_title = 'Session Fix Test';

// Get session info for display
$session_info = [
    'user_id' => $_SESSION['user_id'] ?? 'not_set',
    'username' => $_SESSION['username'] ?? 'not_set',
    'is_admin' => $_SESSION['is_admin'] ?? 'not_set',
    'is_admin_session' => $_SESSION['is_admin_session'] ?? 'not_set',
    'last_activity' => $_SESSION['last_activity'] ?? 'not_set',
    'session_id' => session_id(),
    'current_time' => time()
];

include 'includes/admin-header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>Session Fix Applied - No More Aggressive Checking!
                    </h3>
                </div>
                <div class="card-body">
                    
                    <div class="alert alert-success">
                        <h5><i class="fas fa-thumbs-up me-2"></i>What Changed:</h5>
                        <ul class="mb-0">
                            <li>✅ <strong>Removed aggressive session timeout</strong> for admin users</li>
                            <li>✅ <strong>Admin sessions now last 4 hours</strong> instead of 1 hour</li>
                            <li>✅ <strong>No automatic session checks</strong> on every page load for admins</li>
                            <li>✅ <strong>Simple admin validation</strong> - just check if logged in</li>
                        </ul>
                    </div>

                    <!-- Session Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Current Session Info:</h5>
                            <table class="table table-sm">
                                <?php foreach ($session_info as $key => $value): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($key); ?>:</strong></td>
                                    <td><?php echo htmlspecialchars($value); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Test Navigation:</h5>
                            <p>Try these links - you should NOT get logged out:</p>
                            <div class="d-grid gap-2">
                                <a href="transfers.php" class="btn btn-primary">
                                    <i class="fas fa-exchange-alt me-2"></i>Go to Transfers
                                </a>
                                <a href="transfers/view.php?id=1&type=local-bank" class="btn btn-secondary">
                                    <i class="fas fa-eye me-2"></i>View Transfer (if exists)
                                </a>
                                <a href="transfers/edit.php?id=1&type=local-bank" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>Edit Transfer (if exists)
                                </a>
                                <a href="users.php" class="btn btn-info">
                                    <i class="fas fa-users me-2"></i>Go to Users
                                </a>
                                <a href="index.php" class="btn btn-success">
                                    <i class="fas fa-home me-2"></i>Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Test Instructions -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Test Instructions</h5>
                        </div>
                        <div class="card-body">
                            <h6>Test the Transfer Navigation Issue:</h6>
                            <ol>
                                <li>Click <strong>"Go to Transfers"</strong> above</li>
                                <li>On the transfers page, click <strong>"Edit"</strong> on any transfer</li>
                                <li>On the edit page, click <strong>"Transfers"</strong> in the sidebar</li>
                                <li><strong>Expected Result:</strong> You should stay logged in and return to transfers page</li>
                            </ol>
                            
                            <div class="alert alert-info mt-3">
                                <h6><i class="fas fa-info-circle me-2"></i>What Should Happen Now:</h6>
                                <p class="mb-0">
                                    <strong>No more logout issues!</strong> Admin users can navigate freely between pages 
                                    without being logged out. The session will only expire after 4 hours of complete inactivity.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Details -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Technical Changes Made</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Before (Aggressive):</h6>
                                    <ul class="small">
                                        <li>Session timeout: 30-60 minutes</li>
                                        <li>Timeout check on every page</li>
                                        <li>Session regeneration</li>
                                        <li>Debug logging</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>After (Relaxed):</h6>
                                    <ul class="small">
                                        <li>Session timeout: 4 hours for admins</li>
                                        <li>No automatic timeout checks</li>
                                        <li>Simple admin validation</li>
                                        <li>Clean session handling</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>
