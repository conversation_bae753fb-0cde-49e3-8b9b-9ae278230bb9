<?php
// Comprehensive test for all transfer fixes
?>
<!DOCTYPE html>
<html>
<head>
    <title>Transfer Fixes Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-pass { color: green; font-weight: bold; margin: 5px 0; }
        .test-fail { color: red; font-weight: bold; margin: 5px 0; }
        .test-section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        h2 { color: #333; }
    </style>
</head>
<body>
    <h1>Transfer System Fixes Test Results</h1>
    
    <div class="test-section">
        <h2>1. Bank Name Fix in view.php</h2>
        <?php
        $view_path = __DIR__ . '/transfers/view.php';
        if (file_exists($view_path)) {
            $view_content = file_get_contents($view_path);
            if (strpos($view_content, "isset(\$transfer['bank_name']) && \$transfer['bank_name']") !== false) {
                echo "<div class='test-pass'>✓ bank_name isset check properly implemented in view.php</div>";
            } else {
                echo "<div class='test-fail'>✗ bank_name isset check not found in view.php</div>";
            }
        } else {
            echo "<div class='test-fail'>✗ view.php not found</div>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>2. Recipient Account Fix in edit.php</h2>
        <?php
        $edit_path = __DIR__ . '/transfers/edit.php';
        if (file_exists($edit_path)) {
            $edit_content = file_get_contents($edit_path);
            if (strpos($edit_content, "\$transfer['recipient_account']") !== false) {
                echo "<div class='test-pass'>✓ recipient_account field properly used in edit.php</div>";
            } else {
                echo "<div class='test-fail'>✗ recipient_account field not found in edit.php</div>";
            }
        } else {
            echo "<div class='test-fail'>✗ edit.php not found</div>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>3. Session Management Checks</h2>
        <?php
        // Check if SESSION_TIMEOUT is defined in config.php
        $config_path = __DIR__ . '/../config/config.php';
        if (file_exists($config_path)) {
            $config_content = file_get_contents($config_path);
            if (strpos($config_content, 'SESSION_TIMEOUT') !== false) {
                echo "<div class='test-pass'>✓ SESSION_TIMEOUT constant found in config.php</div>";
            } else {
                echo "<div class='test-fail'>✗ SESSION_TIMEOUT constant not found in config.php</div>";
            }
        } else {
            echo "<div class='test-fail'>✗ config.php not found at expected path: $config_path</div>";
        }

        // Check if config.php is properly included in transfers.php
        $transfers_path = __DIR__ . '/transfers.php';
        if (file_exists($transfers_path)) {
            $transfers_content = file_get_contents($transfers_path);
            if (strpos($transfers_content, "require_once '../config/config.php'") !== false || 
                strpos($transfers_content, "include '../config/config.php'") !== false) {
                echo "<div class='test-pass'>✓ config.php properly included in transfers.php</div>";
            } else {
                echo "<div class='test-fail'>✗ config.php not properly included in transfers.php</div>";
            }
        } else {
            echo "<div class='test-fail'>✗ transfers.php not found</div>";
        }

        // Check if config.php is properly included in view.php
        $view_path = __DIR__ . '/transfers/view.php';
        if (file_exists($view_path)) {
            $view_content = file_get_contents($view_path);
            if (strpos($view_content, "require_once '../../config/config.php'") !== false || 
                strpos($view_content, "include '../../config/config.php'") !== false) {
                echo "<div class='test-pass'>✓ config.php properly included in view.php</div>";
            } else {
                echo "<div class='test-fail'>✗ config.php not properly included in view.php</div>";
            }
        } else {
            echo "<div class='test-fail'>✗ view.php not found</div>";
        }

        // Check that session_start() is not called in transfers.php and view.php (should be in config.php)
        if (file_exists($transfers_path)) {
            $transfers_content = file_get_contents($transfers_path);
            if (strpos($transfers_content, 'session_start()') === false) {
                echo "<div class='test-pass'>✓ No session_start() call in transfers.php (good - should be in config.php)</div>";
            } else {
                echo "<div class='test-fail'>✗ session_start() found in transfers.php (potential conflict)</div>";
            }
        }

        if (file_exists($view_path)) {
            $view_content = file_get_contents($view_path);
            if (strpos($view_content, 'session_start()') === false) {
                echo "<div class='test-pass'>✓ No session_start() call in view.php (good - should be in config.php)</div>";
            } else {
                echo "<div class='test-fail'>✗ session_start() found in view.php (potential conflict)</div>";
            }
        }
        ?>
    </div>

    <div class="test-section">
        <h2>4. Edit Link Verification</h2>
        <?php
        if (file_exists($view_path)) {
            $view_content = file_get_contents($view_path);
            if (strpos($view_content, 'href="<?php echo $edit_url; ?>"') !== false || 
                strpos($view_content, "edit.php?id=") !== false) {
                echo "<div class='test-pass'>✓ Edit link properly formatted in view.php</div>";
            } else {
                echo "<div class='test-fail'>✗ Edit link not found or improperly formatted in view.php</div>";
            }
        }
        ?>
    </div>

    <div class="test-section">
        <h2>5. Summary</h2>
        <p><strong>Fixes Applied:</strong></p>
        <ul>
            <li>Added isset() check for bank_name in view.php line 349</li>
            <li>Fixed recipient_account field reference in edit.php</li>
            <li>Verified session management configuration</li>
        </ul>
        <p><strong>Test completed at:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    </div>
</body>
</html>