<?php
require_once '../config/config.php';
require_once '../config/database.php';

// Initialize database connection
$db = Database::getInstance();

echo "<h2>OTP Status Analysis</h2>";

try {
    // Get sample users with their OTP settings
    $sql = "SELECT 
                a.id, a.account_number, a.username, a.first_name, a.last_name, 
                a.email, a.status as account_status, a.created_at,
                COALESCE(uss.otp_enabled, 1) as otp_enabled,
                uss.otp_enabled as raw_otp_enabled,
                uss.updated_at as otp_updated_at
            FROM accounts a 
            LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
            WHERE a.is_admin = 0
            ORDER BY a.first_name, a.last_name 
            LIMIT 10";
    
    $result = $db->query($sql);
    
    echo "<h3>User Data Analysis</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr>
            <th>ID</th>
            <th>Name</th>
            <th>Account Status</th>
            <th>Raw OTP Enabled</th>
            <th>COALESCE OTP Enabled</th>
            <th>Button Should Show</th>
            <th>Current Logic Shows</th>
            <th>Issue?</th>
          </tr>";
    
    while ($user = $result->fetch_assoc()) {
        $otp_enabled = $user['otp_enabled'];
        $raw_otp = $user['raw_otp_enabled'];
        
        // What the button should show
        $correct_button = $otp_enabled ? 'Disable' : 'Enable';
        
        // What the current logic shows (from line 378 in the original file)
        $current_button = $otp_enabled ? 'Disable' : 'Enable';
        
        // Check if there's an issue
        $has_issue = ($correct_button !== $current_button) ? 'YES' : 'NO';
        
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['first_name']} {$user['last_name']}</td>";
        echo "<td>{$user['account_status']}</td>";
        echo "<td>" . ($raw_otp === null ? 'NULL' : $raw_otp) . "</td>";
        echo "<td>{$otp_enabled}</td>";
        echo "<td>{$correct_button}</td>";
        echo "<td>{$current_button}</td>";
        echo "<td>{$has_issue}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for specific user mentioned (novakane)
    echo "<h3>Specific User Check: novakane</h3>";
    $novakane_sql = "SELECT 
                        a.id, a.username, a.first_name, a.last_name, 
                        a.status as account_status,
                        COALESCE(uss.otp_enabled, 1) as otp_enabled,
                        uss.otp_enabled as raw_otp_enabled
                     FROM accounts a 
                     LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                     WHERE a.username LIKE '%novakane%' OR a.first_name LIKE '%novakane%' OR a.last_name LIKE '%novakane%'";
    
    $novakane_result = $db->query($novakane_sql);
    
    if ($novakane_result && $novakane_result->num_rows > 0) {
        while ($user = $novakane_result->fetch_assoc()) {
            echo "<p><strong>User:</strong> {$user['username']} ({$user['first_name']} {$user['last_name']})</p>";
            echo "<p><strong>Account Status:</strong> {$user['account_status']}</p>";
            echo "<p><strong>Raw OTP Enabled:</strong> " . ($user['raw_otp_enabled'] === null ? 'NULL (defaults to 1)' : $user['raw_otp_enabled']) . "</p>";
            echo "<p><strong>COALESCE OTP Enabled:</strong> {$user['otp_enabled']}</p>";
            echo "<p><strong>Button Should Show:</strong> " . ($user['otp_enabled'] ? 'Disable' : 'Enable') . "</p>";
            
            if ($user['account_status'] === 'active' && $user['otp_enabled'] == 1) {
                echo "<p style='color: red;'><strong>ISSUE CONFIRMED:</strong> User is active with OTP enabled, button should show 'Disable'</p>";
            }
        }
    } else {
        echo "<p>No user found matching 'novakane'</p>";
        
        // Let's check all users to see if we can find the issue
        echo "<h3>All Users with Active Status and OTP Enabled</h3>";
        $active_sql = "SELECT 
                          a.id, a.username, a.first_name, a.last_name, 
                          a.status as account_status,
                          COALESCE(uss.otp_enabled, 1) as otp_enabled
                       FROM accounts a 
                       LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                       WHERE a.status = 'active' AND COALESCE(uss.otp_enabled, 1) = 1 AND a.is_admin = 0
                       LIMIT 5";
        
        $active_result = $db->query($active_sql);
        while ($user = $active_result->fetch_assoc()) {
            echo "<p>{$user['username']} - Status: {$user['account_status']}, OTP: {$user['otp_enabled']} - Button should show: Disable</p>";
        }
    }
    
    echo "<h3>Summary</h3>";
    echo "<p><strong>Key Findings:</strong></p>";
    echo "<ul>";
    echo "<li>The 'Status' column shows <strong>account status</strong> (active, suspended, pending, rejected)</li>";
    echo "<li>The 'OTP Setting' column shows <strong>OTP status</strong> (enabled/disabled)</li>";
    echo "<li>Users are confusing account status with OTP status</li>";
    echo "<li>The button logic appears to be working correctly</li>";
    echo "<li>COALESCE(uss.otp_enabled, 1) defaults to 1 (enabled) when no record exists</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>