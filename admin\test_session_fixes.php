<?php
/**
 * Comprehensive Test Script for Session and Transfer Fixes
 * Tests all applied fixes and session management
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to capture any output
ob_start();

echo "<h1>Session and Transfer Fixes Test Results</h1>";
echo "<p>Testing all applied fixes and session management...</p>";

$test_results = [];

// Test 1: Check if transfers.php uses requireAdmin()
echo "<h2>Test 1: Session Management in transfers.php</h2>";
$transfers_content = file_get_contents(__DIR__ . '/transfers.php');
if (strpos($transfers_content, 'requireAdmin()') !== false) {
    $test_results['transfers_requireAdmin'] = 'PASS';
    echo "<p style='color: green;'>✓ PASS: transfers.php uses requireAdmin() function</p>";
} else {
    $test_results['transfers_requireAdmin'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: transfers.php does not use requireAdmin() function</p>";
}

// Test 2: Check if transfers/view.php uses requireAdmin()
echo "<h2>Test 2: Session Management in transfers/view.php</h2>";
$view_content = file_get_contents(__DIR__ . '/transfers/view.php');
if (strpos($view_content, 'requireAdmin()') !== false) {
    $test_results['view_requireAdmin'] = 'PASS';
    echo "<p style='color: green;'>✓ PASS: transfers/view.php uses requireAdmin() function</p>";
} else {
    $test_results['view_requireAdmin'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: transfers/view.php does not use requireAdmin() function</p>";
}

// Test 3: Check bank_name fix in view.php
echo "<h2>Test 3: Bank Name Fix in transfers/view.php</h2>";
if (strpos($view_content, "bank_name ?? 'N/A'") !== false || 
    strpos($view_content, "isset(\$transfer['bank_name'])") !== false) {
    $test_results['bank_name_fix'] = 'PASS';
    echo "<p style='color: green;'>✓ PASS: Bank name undefined key fix is present</p>";
} else {
    $test_results['bank_name_fix'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: Bank name undefined key fix is missing</p>";
}

// Test 4: Check recipient_account fix in edit.php
echo "<h2>Test 4: Recipient Account Fix in transfers/edit.php</h2>";
$edit_content = file_get_contents(__DIR__ . '/transfers/edit.php');
if (strpos($edit_content, "recipient_account'] ?? ''") !== false || 
    strpos($edit_content, "recipient_account'] ?? 'N/A'") !== false ||
    strpos($edit_content, "isset(\$transfer['recipient_account'])") !== false) {
    $test_results['recipient_account_fix'] = 'PASS';
    echo "<p style='color: green;'>✓ PASS: Recipient account undefined key fix is present</p>";
} else {
    $test_results['recipient_account_fix'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: Recipient account undefined key fix is missing</p>";
}

// Test 5: Check config.php inclusion
echo "<h2>Test 5: Config.php Inclusion</h2>";
$config_path = __DIR__ . '/../config/config.php';
if (file_exists($config_path)) {
    $test_results['config_exists'] = 'PASS';
    echo "<p style='color: green;'>✓ PASS: config.php exists at expected location</p>";
} else {
    $test_results['config_exists'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: config.php not found at expected location</p>";
}

// Test 6: Check SESSION_TIMEOUT constant
echo "<h2>Test 6: SESSION_TIMEOUT Constant</h2>";
if (file_exists($config_path)) {
    $config_content = file_get_contents($config_path);
    if (strpos($config_content, 'SESSION_TIMEOUT') !== false) {
        $test_results['session_timeout_constant'] = 'PASS';
        echo "<p style='color: green;'>✓ PASS: SESSION_TIMEOUT constant is defined in config.php</p>";
    } else {
        $test_results['session_timeout_constant'] = 'FAIL';
        echo "<p style='color: red;'>✗ FAIL: SESSION_TIMEOUT constant not found in config.php</p>";
    }
} else {
    $test_results['session_timeout_constant'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: Cannot check SESSION_TIMEOUT - config.php not found</p>";
}

// Test 7: Check requireAdmin function exists
echo "<h2>Test 7: requireAdmin Function</h2>";
if (file_exists($config_path)) {
    $config_content = file_get_contents($config_path);
    if (strpos($config_content, 'function requireAdmin()') !== false) {
        $test_results['requireAdmin_function'] = 'PASS';
        echo "<p style='color: green;'>✓ PASS: requireAdmin() function is defined in config.php</p>";
    } else {
        $test_results['requireAdmin_function'] = 'FAIL';
        echo "<p style='color: red;'>✗ FAIL: requireAdmin() function not found in config.php</p>";
    }
} else {
    $test_results['requireAdmin_function'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: Cannot check requireAdmin - config.php not found</p>";
}

// Test 8: Check checkSessionTimeout function exists
echo "<h2>Test 8: checkSessionTimeout Function</h2>";
if (file_exists($config_path)) {
    $config_content = file_get_contents($config_path);
    if (strpos($config_content, 'function checkSessionTimeout()') !== false) {
        $test_results['checkSessionTimeout_function'] = 'PASS';
        echo "<p style='color: green;'>✓ PASS: checkSessionTimeout() function is defined in config.php</p>";
    } else {
        $test_results['checkSessionTimeout_function'] = 'FAIL';
        echo "<p style='color: red;'>✗ FAIL: checkSessionTimeout() function not found in config.php</p>";
    }
} else {
    $test_results['checkSessionTimeout_function'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: Cannot check checkSessionTimeout - config.php not found</p>";
}

// Test 9: Check edit link in view.php
echo "<h2>Test 9: Edit Link in transfers/view.php</h2>";
if (strpos($view_content, 'edit.php?id=') !== false) {
    $test_results['edit_link'] = 'PASS';
    echo "<p style='color: green;'>✓ PASS: Edit link is properly formatted in view.php</p>";
} else {
    $test_results['edit_link'] = 'FAIL';
    echo "<p style='color: red;'>✗ FAIL: Edit link not found or improperly formatted in view.php</p>";
}

// Summary
echo "<h2>Test Summary</h2>";
$total_tests = count($test_results);
$passed_tests = count(array_filter($test_results, function($result) { return $result === 'PASS'; }));
$failed_tests = $total_tests - $passed_tests;

echo "<p><strong>Total Tests:</strong> $total_tests</p>";
echo "<p><strong>Passed:</strong> <span style='color: green;'>$passed_tests</span></p>";
echo "<p><strong>Failed:</strong> <span style='color: red;'>$failed_tests</span></p>";

if ($failed_tests === 0) {
    echo "<h3 style='color: green;'>🎉 All tests passed! Session and transfer fixes are working correctly.</h3>";
} else {
    echo "<h3 style='color: red;'>⚠️ Some tests failed. Please review the issues above.</h3>";
}

// Detailed results
echo "<h3>Detailed Results:</h3>";
echo "<ul>";
foreach ($test_results as $test => $result) {
    $color = $result === 'PASS' ? 'green' : 'red';
    $icon = $result === 'PASS' ? '✓' : '✗';
    echo "<li style='color: $color;'>$icon $test: $result</li>";
}
echo "</ul>";

echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";

// Get the output and save it
$output = ob_get_clean();
echo $output;
?>