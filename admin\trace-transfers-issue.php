<?php
/**
 * TRACE TRANSFERS ISSUE - NO AUTHENTICATION
 * This will step through exactly what happens when accessing transfers
 */

// Start session and enable debugging
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Tracing Transfers Page Issue</h1>";
echo "<div style='font-family: monospace; background: #f8f9fa; padding: 20px; margin: 10px 0;'>";

echo "<h3>Step 1: Raw Session State</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Status: " . session_status() . "<br>";
echo "Raw \$_SESSION: <pre>" . print_r($_SESSION, true) . "</pre>";

echo "<h3>Step 2: Loading Config File</h3>";
try {
    echo "Including config.php...<br>";
    require_once '../config/config.php';
    echo "✅ Config loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "<br>";
    exit;
}

echo "<h3>Step 3: Testing isAdminLoggedIn() Function</h3>";
if (function_exists('isAdminLoggedIn')) {
    echo "Function exists: isAdminLoggedIn()<br>";
    
    // Let's trace what this function does step by step
    echo "Checking session variables:<br>";
    echo "- \$_SESSION['user_id']: " . ($_SESSION['user_id'] ?? 'NOT SET') . "<br>";
    echo "- \$_SESSION['is_admin']: " . ($_SESSION['is_admin'] ?? 'NOT SET') . "<br>";
    echo "- \$_SESSION['is_admin_session']: " . ($_SESSION['is_admin_session'] ?? 'NOT SET') . "<br>";
    
    $admin_status = isAdminLoggedIn();
    echo "isAdminLoggedIn() result: " . ($admin_status ? 'TRUE ✅' : 'FALSE ❌') . "<br>";
} else {
    echo "❌ isAdminLoggedIn() function not found<br>";
}

echo "<h3>Step 4: Testing requireAdmin() Function</h3>";
if (function_exists('requireAdmin')) {
    echo "Function exists: requireAdmin()<br>";
    
    // Manually set admin session for testing
    $_SESSION['user_id'] = 1;
    $_SESSION['is_admin'] = true;
    $_SESSION['is_admin_session'] = true;
    $_SESSION['username'] = 'test_admin';
    $_SESSION['first_name'] = 'Test';
    $_SESSION['last_name'] = 'Admin';
    $_SESSION['last_activity'] = time();
    
    echo "Manually set admin session variables<br>";
    echo "Testing isAdminLoggedIn() again: " . (isAdminLoggedIn() ? 'TRUE ✅' : 'FALSE ❌') . "<br>";
    
    echo "Now calling requireAdmin()...<br>";
    ob_start();
    try {
        requireAdmin();
        $output = ob_get_clean();
        echo "✅ requireAdmin() passed without redirect<br>";
        if ($output) {
            echo "Output from requireAdmin(): " . htmlspecialchars($output) . "<br>";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ requireAdmin() threw exception: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ requireAdmin() function not found<br>";
}

echo "<h3>Step 5: Testing Database Connection</h3>";
try {
    $db = getDB();
    echo "✅ Database connection successful<br>";
    
    // Test admin users table
    $admin_query = $db->query("SELECT id, username, first_name, last_name FROM admin_users LIMIT 1");
    if ($admin_query) {
        $admin_user = $admin_query->fetch();
        if ($admin_user) {
            echo "✅ Found admin user: " . $admin_user['username'] . " (" . $admin_user['first_name'] . " " . $admin_user['last_name'] . ")<br>";
        } else {
            echo "⚠️ No admin users found in database<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>Step 6: Testing Transfers Page Access</h3>";
echo "Current session after all tests:<br>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

echo "<h3>Step 7: Direct Test Links</h3>";
echo "<a href='transfers.php' style='background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔗 Test Transfers Page</a><br>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔗 Test Admin Dashboard</a><br>";
echo "<a href='users.php' style='background: #17a2b8; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;'>🔗 Test Users Page</a><br>";

echo "<h3>Step 8: Manual Login Form</h3>";
echo "<form method='post' style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>";
echo "<h4>Manual Admin Login (for testing)</h4>";
echo "<input type='text' name='username' placeholder='Admin Username' style='padding: 5px; margin: 5px;'><br>";
echo "<input type='password' name='password' placeholder='Admin Password' style='padding: 5px; margin: 5px;'><br>";
echo "<input type='submit' name='manual_login' value='Test Login' style='padding: 5px 15px; background: #007bff; color: white; border: none;'>";
echo "</form>";

// Handle manual login
if (isset($_POST['manual_login'])) {
    echo "<h3>Manual Login Attempt</h3>";
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($username && $password) {
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT * FROM admin_users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['first_name'] = $user['first_name'];
                $_SESSION['last_name'] = $user['last_name'];
                $_SESSION['is_admin'] = true;
                $_SESSION['is_admin_session'] = true;
                $_SESSION['last_activity'] = time();
                
                echo "✅ Login successful! Session set.<br>";
                echo "Now try the transfers page: <a href='transfers.php'>transfers.php</a><br>";
            } else {
                echo "❌ Invalid credentials<br>";
            }
        } catch (Exception $e) {
            echo "❌ Login error: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ Please provide username and password<br>";
    }
}

echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
</style>
