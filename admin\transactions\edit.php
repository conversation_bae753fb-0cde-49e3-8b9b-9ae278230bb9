<?php
require_once '../../config/config.php';
requireAdmin();

$page_title = 'Edit Transaction';

// Get transaction ID from URL
$transaction_id = intval($_GET['id'] ?? 0);

if (!$transaction_id) {
    header('Location: ../transactions.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_transaction') {
    try {
        $db = getDB();

        $update_query = "UPDATE account_transactions SET 
                        amount = ?, 
                        transaction_type = ?, 
                        currency = ?, 
                        status = ?, 
                        category = ?, 
                        description = ?,
                        updated_at = NOW()
                        WHERE id = ?";

        $db->query($update_query, [
            floatval($_POST['amount']),
            $_POST['transaction_type'],
            $_POST['currency'],
            $_POST['status'],
            $_POST['category'],
            $_POST['description'],
            $transaction_id
        ]);

        $success = "Transaction updated successfully!";

    } catch (Exception $e) {
        $error = "Failed to update transaction: " . $e->getMessage();
    }
}

try {
    $db = getDB();

    // Get transaction details with user information from account_transactions
    $query = "SELECT at.*, 
              a.first_name, a.last_name, a.username, a.account_number
              FROM account_transactions at 
              LEFT JOIN accounts a ON at.account_id = a.id 
              WHERE at.id = ?";

    $result = $db->query($query, [$transaction_id]);
    $transaction = $result->fetch_assoc();

    if (!$transaction) {
        header('Location: ../transactions.php?error=Transaction not found');
        exit;
    }

    // Get available currencies
    $currencies_query = "SELECT code, name, symbol FROM currencies WHERE status = 'active' ORDER BY code";
    $currencies_result = $db->query($currencies_query);
    $currencies = [];
    while ($currency = $currencies_result->fetch_assoc()) {
        $currencies[] = $currency;
    }



} catch (Exception $e) {
    $error = "Failed to load transaction: " . $e->getMessage();
}

include '../includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="../index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="../transactions.php">Transactions</a></li>
        <li class="breadcrumb-item"><a href="view.php?id=<?php echo $transaction['id']; ?>">Transaction #<?php echo $transaction['id']; ?></a></li>
        <li class="breadcrumb-item active" aria-current="page">Edit</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Edit Form -->
<form method="POST" action="">
    <input type="hidden" name="action" value="update_transaction">
    
    <div class="row g-3">
        <!-- User Information (Read-only) -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        User Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Transaction ID</label>
                        <input type="text" class="form-control" value="#<?php echo $transaction['id']; ?>" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Reference Number</label>
                        <input type="text" class="form-control" value="<?php echo htmlspecialchars(substr($transaction['reference_number'] ?? '', -8) ?: 'N/A'); ?>" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">User</label>
                        <div class="d-flex align-items-center p-3 bg-light rounded">
                            <div class="avatar avatar-sm me-3" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; font-size: 0.7rem;">
                                <?php echo strtoupper(substr($transaction['first_name'] ?? 'U', 0, 1) . substr($transaction['last_name'] ?? 'U', 0, 1)); ?>
                            </div>
                            <div>
                                <div class="fw-bold"><?php echo htmlspecialchars(($transaction['first_name'] ?? 'Unknown') . ' ' . ($transaction['last_name'] ?? 'User')); ?></div>
                                <small class="text-muted">@<?php echo htmlspecialchars($transaction['username'] ?? 'unknown'); ?></small>
                                <br><small class="text-muted">Account: <?php echo htmlspecialchars($transaction['account_number'] ?? 'N/A'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Transaction Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Transaction Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold required">Amount</label>
                        <div class="input-group">
                            <span class="input-group-text" id="currency-symbol">
                                <?php echo getCurrencySymbol($transaction['currency'] ?? 'USD'); ?>
                            </span>
                            <input type="number" name="amount" class="form-control" value="<?php echo $transaction['amount']; ?>" step="0.01" min="0" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold required">Transaction Type</label>
                        <select name="transaction_type" class="form-select" required>
                            <option value="credit" <?php echo $transaction['transaction_type'] === 'credit' ? 'selected' : ''; ?>>Credit</option>
                            <option value="debit" <?php echo $transaction['transaction_type'] === 'debit' ? 'selected' : ''; ?>>Debit</option>
                            <option value="transfer_in" <?php echo $transaction['transaction_type'] === 'transfer_in' ? 'selected' : ''; ?>>Transfer In</option>
                            <option value="transfer_out" <?php echo $transaction['transaction_type'] === 'transfer_out' ? 'selected' : ''; ?>>Transfer Out</option>
                            <option value="deposit" <?php echo $transaction['transaction_type'] === 'deposit' ? 'selected' : ''; ?>>Deposit</option>
                            <option value="withdrawal" <?php echo $transaction['transaction_type'] === 'withdrawal' ? 'selected' : ''; ?>>Withdrawal</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold required">Currency</label>
                        <select name="currency" class="form-select" required onchange="updateCurrencySymbol(this.value)">
                            <?php foreach ($currencies as $currency): ?>
                                <option value="<?php echo $currency['code']; ?>"
                                        data-symbol="<?php echo htmlspecialchars($currency['symbol']); ?>"
                                        <?php echo $transaction['currency'] === $currency['code'] ? 'selected' : ''; ?>>
                                    <?php echo $currency['code']; ?> - <?php echo htmlspecialchars($currency['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold required">Status</label>
                        <select name="status" class="form-select" required>
                            <option value="pending" <?php echo $transaction['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $transaction['status'] === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="completed" <?php echo $transaction['status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="cancelled" <?php echo $transaction['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            <option value="failed" <?php echo $transaction['status'] === 'failed' ? 'selected' : ''; ?>>Failed</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Additional Details -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Additional Details
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Category</label>
                                <select name="category" class="form-select">
                                    <option value="deposit" <?php echo $transaction['category'] === 'deposit' ? 'selected' : ''; ?>>Deposit</option>
                                    <option value="withdrawal" <?php echo $transaction['category'] === 'withdrawal' ? 'selected' : ''; ?>>Withdrawal</option>
                                    <option value="transfer" <?php echo $transaction['category'] === 'transfer' ? 'selected' : ''; ?>>Transfer</option>
                                    <option value="fee" <?php echo $transaction['category'] === 'fee' ? 'selected' : ''; ?>>Fee</option>
                                    <option value="interest" <?php echo $transaction['category'] === 'interest' ? 'selected' : ''; ?>>Interest</option>
                                    <option value="adjustment" <?php echo $transaction['category'] === 'adjustment' ? 'selected' : ''; ?>>Adjustment</option>
                                    <option value="virtual_card" <?php echo $transaction['category'] === 'virtual_card' ? 'selected' : ''; ?>>Virtual Card</option>
                                    <option value="crypto" <?php echo $transaction['category'] === 'crypto' ? 'selected' : ''; ?>>Crypto</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Description -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-comment-alt me-2"></i>
                        Transaction Description
                    </h3>
                </div>
                <div class="card-body">
                    <textarea name="description" class="form-control" rows="3" placeholder="Enter transaction description..."><?php echo htmlspecialchars($transaction['description'] ?? ''); ?></textarea>
                    <small class="form-text text-muted">Add any additional notes or details about this transaction.</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex gap-2">
                <a href="view.php?id=<?php echo $transaction['id']; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn" style="background: var(--success-color); color: white;">
                    <i class="fas fa-save me-2"></i>
                    Save Changes
                </button>
                <a href="../transactions.php" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>
                    Back to List
                </a>
            </div>
        </div>
    </div>
</form>

<script>
function updateCurrencySymbol(currencyCode) {
    const currencySelect = document.querySelector('select[name="currency"]');
    const selectedOption = currencySelect.querySelector(`option[value="${currencyCode}"]`);
    const symbol = selectedOption ? selectedOption.getAttribute('data-symbol') : '$';

    const currencySymbolElement = document.getElementById('currency-symbol');
    if (currencySymbolElement) {
        currencySymbolElement.textContent = symbol;
    }
}
</script>

<?php include '../includes/admin-footer.php'; ?>
