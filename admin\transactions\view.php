<?php
require_once '../../config/config.php';
requireAdmin();

$page_title = 'Transaction Details';

// Get transaction ID from URL
$transaction_id = intval($_GET['id'] ?? 0);

if (!$transaction_id) {
    header('Location: ../transactions.php');
    exit;
}

try {
    $db = getDB();

    // Get transaction details with user information from account_transactions
    $query = "SELECT at.*,
              a.first_name, a.last_name, a.username, a.account_number, a.balance AS account_balance, a.currency AS user_currency,
              0 AS fee,
              admin.first_name AS admin_first_name, admin.last_name AS admin_last_name
              FROM account_transactions at
              LEFT JOIN accounts a ON at.account_id = a.id
              LEFT JOIN accounts admin ON at.processed_by = admin.id
              WHERE at.id = ?";

    $result = $db->query($query, [$transaction_id]);
    $transaction = $result->fetch_assoc();

    if (!$transaction) {
        header('Location: ../transactions.php?error=Transaction not found');
        exit;
    }



} catch (Exception $e) {
    $error = "Failed to load transaction: " . $e->getMessage();
}

include '../includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="../index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="../transactions.php">Transactions</a></li>
        <li class="breadcrumb-item active" aria-current="page">Transaction #<?php echo $transaction['id']; ?></li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Transaction Details -->
<div class="row g-3">
    <!-- User Information Card -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    User Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">User:</label>
                    </div>
                    <div class="col-sm-8">
                        <div class="d-flex align-items-center">
                            <div class="avatar avatar-sm me-2" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; font-size: 0.7rem;">
                                <?php echo strtoupper(substr($transaction['first_name'] ?? 'U', 0, 1) . substr($transaction['last_name'] ?? 'U', 0, 1)); ?>
                            </div>
                            <div>
                                <div class="fw-bold"><?php echo htmlspecialchars(($transaction['first_name'] ?? 'Unknown') . ' ' . ($transaction['last_name'] ?? 'User')); ?></div>
                                <small class="text-muted">@<?php echo htmlspecialchars($transaction['username'] ?? 'unknown'); ?></small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Account Number:</label>
                    </div>
                    <div class="col-sm-8">
                        <code class="small"><?php echo htmlspecialchars($transaction['account_number'] ?? 'N/A'); ?></code>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Processed By:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php if ($transaction['admin_first_name']): ?>
                            <?php echo htmlspecialchars($transaction['admin_first_name'] . ' ' . $transaction['admin_last_name']); ?>
                        <?php else: ?>
                            <span class="text-muted">System</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Account Balance:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php
                        $balance_currency = $transaction['user_currency'] ?? 'USD';
                        $balance_symbol = getCurrencySymbol($balance_currency);
                        ?>
                        <span class="fw-bold text-info"><?php echo $balance_symbol . number_format($transaction['account_balance'] ?? 0, 2); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Transaction Information Card -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    Transaction Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Transaction ID:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="fw-bold">#<?php echo $transaction['id']; ?></span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Reference:</label>
                    </div>
                    <div class="col-sm-8">
                        <code class="small"><?php echo htmlspecialchars(substr($transaction['reference_number'] ?? '', -8) ?: 'N/A'); ?></code>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Type:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge" style="background: var(--primary-color); color: white;">
                            <?php echo ucfirst(str_replace('_', ' ', $transaction['transaction_type'])); ?>
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Amount:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php
                        $amount_class = in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? 'text-success' : 'text-danger';
                        $amount_prefix = in_array($transaction['transaction_type'], ['credit', 'deposit', 'transfer_in']) ? '+' : '-';
                        $currency = $transaction['currency'] ?? $transaction['user_currency'] ?? 'USD';
                        $currency_symbol = getCurrencySymbol($currency);
                        ?>
                        <span class="fw-bold <?php echo $amount_class; ?> fs-5">
                            <?php echo $amount_prefix . $currency_symbol . number_format($transaction['amount'], 2); ?>
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Currency:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php echo $transaction['currency'] ?? 'USD'; ?>
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Status:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge" style="background: var(--primary-color); color: white;">
                            <?php echo ucfirst($transaction['status']); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Details Card -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Transaction Timeline
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <label class="form-label mb-0 fw-bold">Created:</label>
                            </div>
                            <div class="col-sm-8">
                                <?php echo date('M d, Y \a\t g:i A', strtotime($transaction['created_at'])); ?>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-sm-4">
                                <label class="form-label mb-0 fw-bold">Category:</label>
                            </div>
                            <div class="col-sm-8">
                                <?php echo htmlspecialchars($transaction['category'] ?? 'General'); ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <label class="form-label mb-0 fw-bold">Last Updated:</label>
                            </div>
                            <div class="col-sm-8">
                                <?php echo date('M d, Y \a\t g:i A', strtotime($transaction['updated_at'])); ?>
                                <?php if ($transaction['updated_at'] !== $transaction['created_at']): ?>
                                    <small class="badge bg-warning ms-1">Modified</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($transaction['description']): ?>
<!-- Description Card -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-comment-alt me-2"></i>
                    Transaction Description
                </h3>
            </div>
            <div class="card-body">
                <p class="mb-0"><?php echo nl2br(htmlspecialchars($transaction['description'])); ?></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2">
            <a href="../transactions.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Transactions
            </a>
            <a href="edit.php?id=<?php echo $transaction['id']; ?>" class="btn" style="background: var(--primary-color); color: white;">
                <i class="fas fa-edit me-2"></i>
                Edit Transaction
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print me-2"></i>
                Print Details
            </button>
        </div>
    </div>
</div>

<?php include '../includes/admin-footer.php'; ?>
