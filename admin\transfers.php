<?php
/**
 * Admin Transfer Management Page
 * Comprehensive transfer management with editing capabilities
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../admin/login.php');
}

// Get database connection
$db = getDB();

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_transfer_status':
                    $transfer_id = (int)$_POST['transfer_id'];
                    $transfer_type = sanitizeInput($_POST['transfer_type']);
                    $new_status = sanitizeInput($_POST['status']);

                    // Validate status
                    $valid_statuses = ['pending', 'completed', 'failed', 'cancelled'];
                    if (!in_array($new_status, $valid_statuses)) {
                        throw new Exception('Invalid status selected');
                    }

                    // Update transfer status in the correct table
                    if ($transfer_type === 'local-bank') {
                        $update_sql = "UPDATE local_transfers SET status = ? WHERE id = ?";
                    } else {
                        $update_sql = "UPDATE interbank_transfers SET status = ? WHERE id = ?";
                    }
                    $db->query($update_sql, [$new_status, $transfer_id]);

                    $success_message = "Transfer status updated successfully";
                    break;

                case 'update_transfer_details':
                    $transfer_id = (int)$_POST['transfer_id'];
                    $transfer_type = sanitizeInput($_POST['transfer_type']);
                    $amount = floatval($_POST['amount']);
                    $status = sanitizeInput($_POST['status']);
                    $recipient_name = sanitizeInput($_POST['recipient_name']);
                    $recipient_account = sanitizeInput($_POST['recipient_account']);
                    $description = sanitizeInput($_POST['description']);

                    // Validate amount
                    if ($amount <= 0) {
                        throw new Exception('Amount must be greater than 0');
                    }

                    // Validate status
                    $valid_statuses = ['pending', 'completed', 'failed', 'cancelled'];
                    if (!in_array($status, $valid_statuses)) {
                        throw new Exception('Invalid status provided');
                    }

                    // Update transfer details in the correct table
                    if ($transfer_type === 'local-bank') {
                        $update_sql = "UPDATE local_transfers SET
                                       amount = ?, status = ?, beneficiary_account_name = ?, beneficiary_account_number = ?, narration = ?
                                       WHERE id = ?";
                    } else {
                        $update_sql = "UPDATE interbank_transfers SET
                                       amount = ?, status = ?, narration = ?
                                       WHERE id = ?";
                        // For inter-bank, we can't update recipient details as they're linked to accounts
                        $db->query($update_sql, [$amount, $status, $description, $transfer_id]);
                        $success_message = "Transfer details updated successfully";
                        break;
                    }
                    $db->query($update_sql, [$amount, $status, $recipient_name, $recipient_account, $description, $transfer_id]);

                    $success_message = "Transfer details updated successfully";
                    break;

                case 'toggle_otp_setting':
                    $otp_enabled = isset($_POST['otp_enabled']) ? '1' : '0';

                    // Update or insert OTP setting using correct column names
                    $setting_sql = "INSERT INTO admin_settings (setting_key, value, description, updated_at)
                                   VALUES ('transfer_otp_enabled', ?, 'Enable/disable OTP for local bank transfers', NOW())
                                   ON DUPLICATE KEY UPDATE value = VALUES(value), updated_at = NOW()";
                    $db->query($setting_sql, [$otp_enabled]);

                    $success_message = "OTP setting updated successfully";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get transfer statistics from both local_transfers and interbank_transfers tables
$stats_sql = "SELECT
                (SELECT COUNT(*) FROM local_transfers) + (SELECT COUNT(*) FROM interbank_transfers) as total_transfers,
                (SELECT COUNT(*) FROM local_transfers WHERE status = 'completed') + (SELECT COUNT(*) FROM interbank_transfers WHERE status = 'completed') as completed_transfers,
                (SELECT COUNT(*) FROM local_transfers WHERE status = 'pending') + (SELECT COUNT(*) FROM interbank_transfers WHERE status = 'pending') as pending_transfers,
                (SELECT COUNT(*) FROM local_transfers WHERE status = 'failed') + (SELECT COUNT(*) FROM interbank_transfers WHERE status = 'failed') as failed_transfers,
                (SELECT COALESCE(SUM(amount), 0) FROM local_transfers WHERE status = 'completed') + (SELECT COALESCE(SUM(amount), 0) FROM interbank_transfers WHERE status = 'completed') as total_amount,
                (SELECT COUNT(*) FROM interbank_transfers) as inter_bank_count,
                (SELECT COUNT(*) FROM local_transfers) as local_bank_count";
$stats_result = $db->query($stats_sql);
$stats = $stats_result->fetch_assoc();

// Get current OTP setting
$otp_setting_sql = "SELECT value FROM admin_settings WHERE setting_key = 'transfer_otp_enabled'";
$otp_setting_result = $db->query($otp_setting_sql);
$otp_enabled = true; // Default to enabled
if ($otp_setting_result && $otp_setting_result->num_rows > 0) {
    $setting = $otp_setting_result->fetch_assoc();
    $otp_enabled = ($setting['value'] === '1');
}

// Pagination setup
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Filter setup
$status_filter = sanitizeInput($_GET['status'] ?? '');
$type_filter = sanitizeInput($_GET['type'] ?? '');
$search_query = sanitizeInput($_GET['search'] ?? '');

// Build filter conditions for UNION query (local and inter-bank transfers)
$local_where_conditions = [];
$interbank_where_conditions = [];
$params = [];

if (!empty($status_filter)) {
    $local_where_conditions[] = "lt.status = ?";
    $interbank_where_conditions[] = "it.status = ?";
    $params[] = $status_filter;
}

if (!empty($type_filter)) {
    if ($type_filter === 'local-bank') {
        // Only show local transfers
        $interbank_where_conditions[] = "1 = 0"; // Exclude inter-bank
    } elseif ($type_filter === 'inter-bank') {
        // Only show inter-bank transfers
        $local_where_conditions[] = "1 = 0"; // Exclude local
    }
}

if (!empty($search_query)) {
    $search_param = "%{$search_query}%";
    $local_where_conditions[] = "(lt.transaction_id LIKE ? OR lt.beneficiary_account_name LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ?)";
    $interbank_where_conditions[] = "(it.transaction_id LIKE ? OR CONCAT(r.first_name, ' ', r.last_name) LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ?)";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$local_where_clause = !empty($local_where_conditions) ? "WHERE " . implode(" AND ", $local_where_conditions) : "";
$interbank_where_clause = !empty($interbank_where_conditions) ? "WHERE " . implode(" AND ", $interbank_where_conditions) : "";

$page_title = 'Local & Inter-Bank Transfers';

// Define page actions
$page_actions = [
    [
        'url' => 'index.php',
        'label' => 'Back to Dashboard',
        'icon' => 'fas fa-arrow-left'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Transfer Management</li>
    </ol>
</nav>

<!-- Wire Transfer Notice -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle me-2"></i>
        <div class="flex-grow-1">
            <strong>Note:</strong> This page manages Inter-Bank and Local Bank transfers only.
            Wire transfers (international) are managed separately for better organization and specialized features.
        </div>
        <a href="wire-transfers.php" class="btn btn-outline-primary btn-sm">
            <i class="fas fa-globe me-1"></i>Manage Wire Transfers
        </a>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (!empty($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-paper-plane"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_transfers']); ?></div>
                        <div class="text-muted">Total Transfers</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['completed_transfers']); ?></div>
                        <div class="text-muted">Completed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending_transfers']); ?></div>
                        <div class="text-muted">Pending</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo formatCurrency($stats['total_amount'], 'USD'); ?></div>
                        <div class="text-muted">Total Amount</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Settings Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cog me-2"></i>Transfer Settings
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" class="row">
                    <input type="hidden" name="action" value="toggle_otp_setting">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="otp_enabled" id="otp_enabled"
                                   <?php echo $otp_enabled ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="otp_enabled">
                                <strong>Require OTP for Local Bank Transfers</strong>
                            </label>
                            <div class="form-text">
                                When enabled, users must verify OTP via email for local bank transfers.
                                Inter-bank transfers are always processed instantly without OTP.
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Global OTP Settings
                        </button>
                        <a href="user-otp-settings.php" class="btn btn-outline-primary ms-2">
                            <i class="fas fa-users-cog me-2"></i>Per-User OTP Settings
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transfers Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exchange-alt me-2"></i>Local & Inter-Bank Transfers
                </h3>
                <div class="card-actions">
                    <!-- Filters -->
                    <form method="GET" class="d-flex gap-2">
                        <select name="status" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>

                        </select>

                        <select name="type" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="">All Types</option>
                            <option value="local" <?php echo $type_filter === 'local' ? 'selected' : ''; ?>>Local Transfer</option>
                            <option value="bitcoin" <?php echo $type_filter === 'bitcoin' ? 'selected' : ''; ?>>Bitcoin Transfer</option>
                        </select>

                        <input type="text" name="search" class="form-control form-control-sm"
                               placeholder="Search..." value="<?php echo htmlspecialchars($search_query); ?>">
                        <button type="submit" class="btn btn-sm btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if (!empty($status_filter) || !empty($type_filter) || !empty($search_query)): ?>
                        <a href="transfers.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <?php
            // Get transfers from both local_transfers and interbank_transfers tables with pagination
            $transfers_sql = "
                (SELECT lt.id, lt.transaction_id, lt.sender_id, NULL as recipient_id,
                        lt.beneficiary_account_name as recipient_name, lt.beneficiary_account_number as recipient_account,
                        lt.amount, lt.currency, lt.transfer_fee, lt.narration, lt.status, lt.created_at,
                        'local-bank' as transfer_type,
                        u.first_name, u.last_name, u.email, u.account_number as sender_account,
                        NULL as recipient_first_name, NULL as recipient_last_name
                 FROM local_transfers lt
                 LEFT JOIN accounts u ON lt.sender_id = u.id
                 {$local_where_clause})
                UNION ALL
                (SELECT it.id, it.transaction_id, it.sender_id, it.recipient_id,
                        CONCAT(r.first_name, ' ', r.last_name) as recipient_name, r.account_number as recipient_account,
                        it.amount, it.currency, it.transfer_fee, it.narration, it.status, it.created_at,
                        'inter-bank' as transfer_type,
                        u.first_name, u.last_name, u.email, u.account_number as sender_account,
                        r.first_name as recipient_first_name, r.last_name as recipient_last_name
                 FROM interbank_transfers it
                 LEFT JOIN accounts u ON it.sender_id = u.id
                 LEFT JOIN accounts r ON it.recipient_id = r.id
                 {$interbank_where_clause})
                ORDER BY created_at DESC
                LIMIT {$per_page} OFFSET {$offset}";

            $transfers_result = $db->query($transfers_sql, $params);
            $transfers = [];
            while ($row = $transfers_result->fetch_assoc()) {
                $transfers[] = $row;
            }

            // Get total count for pagination with filters
            $count_sql = "SELECT
                            (SELECT COUNT(*) FROM local_transfers lt
                             LEFT JOIN accounts u ON lt.sender_id = u.id
                             {$local_where_clause}) +
                            (SELECT COUNT(*) FROM interbank_transfers it
                             LEFT JOIN accounts u ON it.sender_id = u.id
                             LEFT JOIN accounts r ON it.recipient_id = r.id
                             {$interbank_where_clause}) as total";
            $count_result = $db->query($count_sql, $params);
            $total_transfers = $count_result->fetch_assoc()['total'];
            $total_pages = ceil($total_transfers / $per_page);
            ?>

            <?php if (!empty($transfers)): ?>
            <div class="table-responsive">
                <table class="table table-vcenter card-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Sender</th>
                            <th>Reference</th>
                            <th>Recipient</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $row_number = ($page - 1) * $per_page + 1;
                        foreach ($transfers as $transfer):
                        ?>
                        <tr>
                            <td>
                                <div class="text-muted"><?php echo $row_number++; ?></div>
                            </td>
                            <td>
                                <div class="d-flex py-1 align-items-center">
                                    <div class="avatar avatar-sm me-2" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; font-weight: 600;">
                                        <?php echo strtoupper(substr($transfer['first_name'], 0, 1) . substr($transfer['last_name'], 0, 1)); ?>
                                    </div>
                                    <div class="flex-fill">
                                        <div class="font-weight-medium"><?php echo htmlspecialchars($transfer['first_name'] . ' ' . $transfer['last_name']); ?></div>
                                        <div class="text-muted"><small><?php echo htmlspecialchars($transfer['sender_account']); ?></small></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-muted font-monospace"><?php echo htmlspecialchars($transfer['transaction_id']); ?></div>
                            </td>
                            <td>
                                <div class="font-weight-medium"><?php echo htmlspecialchars($transfer['recipient_name'] ?? 'Unknown'); ?></div>
                                <div class="text-muted"><small>****<?php echo $transfer['recipient_account'] ? substr($transfer['recipient_account'], -4) : '****'; ?></small></div>
                                <?php if (!empty($transfer['recipient_first_name']) && !empty($transfer['recipient_last_name'])): ?>
                                <div class="text-muted"><small><?php echo htmlspecialchars($transfer['recipient_first_name'] . ' ' . $transfer['recipient_last_name']); ?></small></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $type_badges = [
                                    'local-bank' => '<span class="badge" style="background: var(--primary-color); color: white; font-weight: 600;">Local Bank</span>',
                                    'inter-bank' => '<span class="badge" style="background: var(--primary-dark); color: white; font-weight: 600;">Inter-Bank</span>',
                                ];
                                echo $type_badges[$transfer['transfer_type']] ?? '<span class="badge bg-secondary">Unknown</span>';
                                ?>
                            </td>
                            <td>
                                <div class="font-weight-medium"><?php echo formatCurrency($transfer['amount'], $transfer['currency']); ?></div>
                                <?php if (isset($transfer['fee']) && $transfer['fee'] > 0): ?>
                                <div class="text-muted"><small>Fee: <?php echo formatCurrency($transfer['fee'], $transfer['currency']); ?></small></div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $status_badges = [
                                    'completed' => '<span class="badge" style="background: var(--success-color); color: white; font-weight: 600;">Completed</span>',
                                    'pending' => '<span class="badge" style="background: var(--warning-color); color: white; font-weight: 600;">Pending</span>',
                                    'failed' => '<span class="badge" style="background: var(--danger-color); color: white; font-weight: 600;">Failed</span>',
                                    'cancelled' => '<span class="badge" style="background: var(--primary-color); color: white; font-weight: 600;">Cancelled</span>'
                                ];
                                echo $status_badges[$transfer['status']] ?? '<span class="badge bg-secondary">Unknown</span>';
                                ?>
                            </td>
                            <td>
                                <div class="text-muted"><?php echo formatDate($transfer['created_at'], 'M j, Y'); ?></div>
                                <div class="text-muted"><small><?php echo formatDate($transfer['created_at'], 'g:i A'); ?></small></div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="transfers/edit.php?id=<?php echo $transfer['id']; ?>&type=<?php echo $transfer['transfer_type']; ?>" class="btn btn-sm btn-outline-primary" title="Edit Transfer">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="transfers/view.php?id=<?php echo $transfer['id']; ?>&type=<?php echo $transfer['transfer_type']; ?>" class="btn btn-sm btn-outline-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="card-footer d-flex align-items-center">
                <p class="m-0 text-muted">
                    Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $per_page, $total_transfers); ?>
                    of <?php echo $total_transfers; ?> transfers
                </p>
                <ul class="pagination m-0 ms-auto">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                            <i class="fas fa-chevron-left"></i> prev
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&type=<?php echo $type_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                            next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            <?php endif; ?>

            <?php else: ?>
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-paper-plane" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No transfers found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty($status_filter) || !empty($type_filter) || !empty($search_query)): ?>
                        No transfers match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                        No transfers have been made yet. Transfers will appear here once users start making transactions.
                        <?php endif; ?>
                    </p>
                    <?php if (!empty($status_filter) || !empty($type_filter) || !empty($search_query)): ?>
                    <div class="empty-action">
                        <a href="transfers.php" class="btn btn-primary">
                            <i class="fas fa-times me-2"></i>Clear Filters
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>





<script>
// Simple working functions
function editTransferFromData(button) {
    try {
        var transfer = JSON.parse(button.getAttribute('data-transfer'));
        editTransfer(transfer);
    } catch (e) {
        alert('Error loading transfer data');
    }
}

function viewTransferDetailsFromData(button) {
    try {
        var transfer = JSON.parse(button.getAttribute('data-transfer'));
        viewTransferDetails(transfer);
    } catch (e) {
        alert('Error loading transfer data');
    }
}

function editTransfer(transfer) {
    try {
        document.getElementById('editTransferId').value = transfer.id || '';
        document.getElementById('editReferenceNumber').value = transfer.transaction_id || '';
        document.getElementById('editTransferType').value = transfer.transfer_type || '';
        document.getElementById('editAmount').value = transfer.amount || '';
        document.getElementById('editStatus').value = transfer.status || '';
        document.getElementById('editRecipientName').value = transfer.recipient_name || '';
        document.getElementById('editRecipientAccount').value = transfer.recipient_account || '';
        document.getElementById('editDescription').value = transfer.description || '';

        var modal = new bootstrap.Modal(document.getElementById('editTransferModal'));
        modal.show();
    } catch (e) {
        alert('Error opening edit modal');
    }
}

function viewTransferDetails(transfer) {
    try {
        var content = `
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm" style="border: 2px solid var(--primary-color) !important;">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; border-bottom: 3px solid rgba(255,255,255,0.1);">
                            <h3 class="card-title mb-0 h6">
                                <i class="fas fa-info-circle me-2"></i>
                                Transfer Information
                            </h3>
                        </div>
                        <div class="card-body py-3" style="background: rgba(var(--primary-color-rgb), 0.02);">
                            <div class="row mb-2 pb-2" style="border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.1);">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Reference:</label>
                                </div>
                                <div class="col-sm-8">
                                    <code class="small">${transfer.transaction_id || 'N/A'}</code>
                                </div>
                            </div>
                            <div class="row mb-2 pb-2" style="border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.1);">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Type:</label>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-info">${(transfer.transfer_type || 'N/A').replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                                </div>
                            </div>
                            <div class="row mb-2 pb-2" style="border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.1);">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Status:</label>
                                </div>
                                <div class="col-sm-8">
                                    <span class="badge bg-${getTransferStatusColor(transfer.status)}">${(transfer.status || 'N/A').charAt(0).toUpperCase() + (transfer.status || 'N/A').slice(1)}</span>
                                </div>
                            </div>
                            <div class="row mb-2 pb-2" style="border-bottom: 1px solid rgba(var(--primary-color-rgb), 0.1);">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Amount:</label>
                                </div>
                                <div class="col-sm-8">
                                    <span class="fw-bold text-success fs-6">$${parseFloat(transfer.amount || 0).toFixed(2)}</span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Currency:</label>
                                </div>
                                <div class="col-sm-8">
                                    ${transfer.currency || 'USD'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-0 shadow-sm" style="border: 2px solid var(--secondary-color) !important;">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%); color: white; border-bottom: 3px solid rgba(255,255,255,0.1);">
                            <h3 class="card-title mb-0 h6">
                                <i class="fas fa-users me-2"></i>
                                Sender & Recipient
                            </h3>
                        </div>
                        <div class="card-body py-3" style="background: rgba(var(--secondary-color-rgb), 0.02);">
                            <div class="row mb-2 pb-2" style="border-bottom: 1px solid rgba(var(--secondary-color-rgb), 0.1);">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Sender:</label>
                                </div>
                                <div class="col-sm-8">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; font-size: 0.7rem;">
                                            ${((transfer.first_name || 'U').charAt(0) + (transfer.last_name || 'U').charAt(0)).toUpperCase()}
                                        </div>
                                        <div>
                                            <div class="fw-bold small">${(transfer.first_name || 'Unknown') + ' ' + (transfer.last_name || 'User')}</div>
                                            <small class="text-muted">${transfer.sender_account || 'N/A'}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-2 pb-2" style="border-bottom: 1px solid rgba(var(--secondary-color-rgb), 0.1);">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Recipient:</label>
                                </div>
                                <div class="col-sm-8">
                                    <div class="fw-bold small">${transfer.recipient_name || 'Unknown'}</div>
                                    <small class="text-muted">${transfer.recipient_account || 'N/A'}</small>
                                </div>
                            </div>
                            <div class="row mb-2 pb-2" style="border-bottom: 1px solid rgba(var(--secondary-color-rgb), 0.1);">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Created:</label>
                                </div>
                                <div class="col-sm-8">
                                    <div class="small">${formatTransferDate(transfer.created_at)}</div>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-sm-4">
                                    <label class="form-label mb-0 small">Fee:</label>
                                </div>
                                <div class="col-sm-8">
                                    ${transfer.fee && transfer.fee > 0 ? '$' + parseFloat(transfer.fee).toFixed(2) : '<span class="text-muted">No fee</span>'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            ${transfer.description ? `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card border-0 shadow-sm" style="border: 2px solid var(--success-color) !important;">
                        <div class="card-header py-2" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%); color: white; border-bottom: 3px solid rgba(255,255,255,0.1);">
                            <h3 class="card-title mb-0 h6">
                                <i class="fas fa-comment-alt me-2"></i>
                                Description
                            </h3>
                        </div>
                        <div class="card-body py-3" style="background: rgba(var(--success-color-rgb), 0.02);">
                            <p class="mb-0 small">${transfer.description}</p>
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}
        `;

        document.getElementById('transferDetailsContent').innerHTML = content;
        var modal = new bootstrap.Modal(document.getElementById('viewTransferModal'));
        modal.show();
    } catch (e) {
        alert('Error opening view modal');
    }
}

function printTransferDetails() {
    try {
        var content = document.getElementById('transferDetailsContent').innerHTML;
        var printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Transfer Details</title></head><body><h3>Transfer Details</h3>' + content + '</body></html>');
        printWindow.document.close();
        printWindow.print();
    } catch (e) {
        alert('Error printing transfer details');
    }
}

// Helper functions for transfer modal
function getTransferStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}

function formatTransferDate(dateString) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}
</script>

<?php include 'includes/admin-footer.php'; ?>
