<?php
require_once '../../config/config.php';
requireAdmin();

// Get transfer ID and type from URL
$transfer_id = intval($_GET['id'] ?? 0);
$transfer_type = $_GET['type'] ?? '';

if (!$transfer_id) {
    header('Location: ../transfers.php');
    exit;
}

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_transfer') {
    try {
        $db = getDB();

        // Build dynamic update query based on transfer type
        if ($transfer_type === 'local-bank') {
            $update_fields = [];
            $update_values = [];

            // Core editable fields for local bank transfers
            if (isset($_POST['amount']) && is_numeric($_POST['amount'])) {
                $update_fields[] = "amount = ?";
                $update_values[] = floatval($_POST['amount']);
            }

            if (isset($_POST['currency'])) {
                $update_fields[] = "currency = ?";
                $update_values[] = $_POST['currency'];
            }

            if (isset($_POST['status'])) {
                $update_fields[] = "status = ?";
                $update_values[] = $_POST['status'];
            }

            if (isset($_POST['transfer_fee'])) {
                $update_fields[] = "transfer_fee = ?";
                $update_values[] = floatval($_POST['transfer_fee']);
            }

            if (isset($_POST['narration'])) {
                $update_fields[] = "narration = ?";
                $update_values[] = $_POST['narration'];
            }

            // Local bank specific fields
            if (isset($_POST['beneficiary_account_name'])) {
                $update_fields[] = "beneficiary_account_name = ?";
                $update_values[] = $_POST['beneficiary_account_name'];
            }

            if (isset($_POST['beneficiary_account_number'])) {
                $update_fields[] = "beneficiary_account_number = ?";
                $update_values[] = $_POST['beneficiary_account_number'];
            }

            if (isset($_POST['beneficiary_bank_name'])) {
                $update_fields[] = "beneficiary_bank_name = ?";
                $update_values[] = $_POST['beneficiary_bank_name'];
            }

            if (isset($_POST['routing_code'])) {
                $update_fields[] = "routing_code = ?";
                $update_values[] = $_POST['routing_code'];
            }

            if (isset($_POST['account_type'])) {
                $update_fields[] = "account_type = ?";
                $update_values[] = $_POST['account_type'];
            }

            // Date/time fields - Handle backdating properly (no system time override)
            if (isset($_POST['created_at']) && !empty($_POST['created_at'])) {
                $created_date = DateTime::createFromFormat('Y-m-d\TH:i', $_POST['created_at']);
                if ($created_date) {
                    // Validate reasonable date range (1900-2100)
                    $year = (int)$created_date->format('Y');
                    if ($year >= 1900 && $year <= 2100) {
                        $update_fields[] = "created_at = ?";
                        $update_values[] = $created_date->format('Y-m-d H:i:s');
                    } else {
                        $error = "Created date must be between 1900 and 2100.";
                    }
                }
            }

            if (!empty($update_fields)) {
                $update_values[] = $transfer_id;

                $update_query = "UPDATE local_transfers SET " . implode(", ", $update_fields) . " WHERE id = ?";
                $db->query($update_query, $update_values);
                $success = "Local bank transfer updated successfully!";
            }

        } elseif ($transfer_type === 'inter-bank') {
            $update_fields = [];
            $update_values = [];

            // Core editable fields for inter-bank transfers
            if (isset($_POST['amount']) && is_numeric($_POST['amount'])) {
                $update_fields[] = "amount = ?";
                $update_values[] = floatval($_POST['amount']);
            }

            if (isset($_POST['currency'])) {
                $update_fields[] = "currency = ?";
                $update_values[] = $_POST['currency'];
            }

            if (isset($_POST['status'])) {
                $update_fields[] = "status = ?";
                $update_values[] = $_POST['status'];
            }

            if (isset($_POST['transfer_fee'])) {
                $update_fields[] = "transfer_fee = ?";
                $update_values[] = floatval($_POST['transfer_fee']);
            }

            if (isset($_POST['narration'])) {
                $update_fields[] = "narration = ?";
                $update_values[] = $_POST['narration'];
            }

            // Date/time fields - Handle backdating properly (no system time override)
            if (isset($_POST['created_at']) && !empty($_POST['created_at'])) {
                $created_date = DateTime::createFromFormat('Y-m-d\TH:i', $_POST['created_at']);
                if ($created_date) {
                    // Validate reasonable date range (1900-2100)
                    $year = (int)$created_date->format('Y');
                    if ($year >= 1900 && $year <= 2100) {
                        $update_fields[] = "created_at = ?";
                        $update_values[] = $created_date->format('Y-m-d H:i:s');
                    } else {
                        $error = "Created date must be between 1900 and 2100.";
                    }
                }
            }

            if (isset($_POST['completed_at'])) {
                if (!empty($_POST['completed_at'])) {
                    $completed_date = DateTime::createFromFormat('Y-m-d\TH:i', $_POST['completed_at']);
                    if ($completed_date) {
                        // Validate reasonable date range (1900-2100)
                        $year = (int)$completed_date->format('Y');
                        if ($year >= 1900 && $year <= 2100) {
                            $update_fields[] = "completed_at = ?";
                            $update_values[] = $completed_date->format('Y-m-d H:i:s');
                        } else {
                            $error = "Completed date must be between 1900 and 2100.";
                        }
                    }
                } else {
                    $update_fields[] = "completed_at = NULL";
                }
            }

            if (!empty($update_fields)) {
                $update_values[] = $transfer_id;
                $update_query = "UPDATE interbank_transfers SET " . implode(", ", $update_fields) . " WHERE id = ?";
                $db->query($update_query, $update_values);
                $success = "Inter-bank transfer updated successfully!";
            }
        }

        if (empty($update_fields)) {
            $error = "No fields to update.";
        }

    } catch (Exception $e) {
        $error = "Failed to update transfer: " . $e->getMessage();
    }
}

try {
    $db = getDB();

    // Auto-detect transfer type if not provided (same logic as view.php)
    if (empty($transfer_type)) {
        $check_local = "SELECT 'local-bank' as transfer_type FROM local_transfers WHERE id = ?";
        $local_result = $db->query($check_local, [$transfer_id]);

        if ($local_result && $local_result->num_rows > 0) {
            $transfer_type = 'local-bank';
        } else {
            $check_interbank = "SELECT 'inter-bank' as transfer_type FROM interbank_transfers WHERE id = ?";
            $interbank_result = $db->query($check_interbank, [$transfer_id]);

            if ($interbank_result && $interbank_result->num_rows > 0) {
                $transfer_type = 'inter-bank';
            }
        }
    }

    // Get transfer details based on type (same queries as view.php)
    if ($transfer_type === 'local-bank') {
        $query = "SELECT lt.*,
                  sender.first_name as sender_first_name, sender.last_name as sender_last_name,
                  sender.username as sender_username, sender.account_number as sender_account,
                  sender.email as sender_email, sender.phone as sender_phone,
                  lt.beneficiary_account_name as recipient_name,
                  lt.beneficiary_account_number as recipient_account,
                  lt.beneficiary_bank_name as bank_name,
                  lt.routing_code as routing_code,
                  lt.account_type as account_type,
                  'local-bank' as transfer_type,
                  lt.transfer_fee as fee,
                  lt.narration as description
                  FROM local_transfers lt
                  LEFT JOIN accounts sender ON lt.sender_id = sender.id
                  WHERE lt.id = ?";
    } elseif ($transfer_type === 'inter-bank') {
        $query = "SELECT it.*,
                  sender.first_name as sender_first_name, sender.last_name as sender_last_name,
                  sender.username as sender_username, sender.account_number as sender_account,
                  sender.email as sender_email, sender.phone as sender_phone,
                  recipient.first_name as recipient_first_name, recipient.last_name as recipient_last_name,
                  recipient.username as recipient_username, recipient.account_number as recipient_account,
                  recipient.email as recipient_email, recipient.phone as recipient_phone,
                  CONCAT(recipient.first_name, ' ', recipient.last_name) as recipient_name,
                  'inter-bank' as transfer_type,
                  it.transfer_fee as fee,
                  it.narration as description
                  FROM interbank_transfers it
                  LEFT JOIN accounts sender ON it.sender_id = sender.id
                  LEFT JOIN accounts recipient ON it.recipient_id = recipient.id
                  WHERE it.id = ?";
    } else {
        header('Location: ../transfers.php?error=Invalid transfer type');
        exit;
    }

    $result = $db->query($query, [$transfer_id]);
    $transfer = $result->fetch_assoc();

    if (!$transfer) {
        header('Location: ../transfers.php?error=Transfer not found');
        exit;
    }

} catch (Exception $e) {
    $error = "Failed to load transfer: " . $e->getMessage();
}

// Set page title and actions for admin header
$page_title = 'Edit Transfer #' . ($transfer['id'] ?? 'Unknown');

// Define page actions
$page_actions = [
    [
        'url' => 'view.php?id=' . ($transfer['id'] ?? '') . '&type=' . $transfer_type,
        'label' => 'View Details',
        'icon' => 'fas fa-eye'
    ],
    [
        'url' => '../transfers.php',
        'label' => 'Back to List',
        'icon' => 'fas fa-list'
    ]
];

include '../includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="../index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="../transfers.php">Transfers</a></li>
        <li class="breadcrumb-item active" aria-current="page">Edit Transfer #<?php echo $transfer['id']; ?></li>
    </ol>
</nav>

<?php if ($success): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div>
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success); ?>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<?php if ($error): ?>
<div class="alert alert-danger alert-dismissible" role="alert">
    <div class="d-flex">
        <div>
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

            <form method="POST" action="">
                <input type="hidden" name="action" value="update_transfer">

                <!-- Transfer Details -->
                <div class="row g-3">
                    <!-- Transfer Information Card -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title mb-0">
                                    <i class="fas fa-edit me-2"></i>
                                    Transfer Information
                                </h3>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Transfer ID:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="fw-bold">#<?php echo $transfer['id']; ?></span>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Reference:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <code class="small"><?php echo htmlspecialchars($transfer['transaction_id'] ?? 'N/A'); ?></code>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Amount:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <span class="input-group-text" id="currency-symbol">
                                                <?php echo getCurrencySymbol($transfer['currency'] ?? 'USD'); ?>
                                            </span>
                                            <input type="number" name="amount" class="form-control" value="<?php echo $transfer['amount']; ?>" step="0.01" min="0" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Currency:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <select name="currency" class="form-select" required onchange="updateCurrencySymbol(this.value)">
                                            <option value="USD" data-symbol="$" <?php echo $transfer['currency'] === 'USD' ? 'selected' : ''; ?>>USD - US Dollar</option>
                                            <option value="EUR" data-symbol="€" <?php echo $transfer['currency'] === 'EUR' ? 'selected' : ''; ?>>EUR - Euro</option>
                                            <option value="GBP" data-symbol="£" <?php echo $transfer['currency'] === 'GBP' ? 'selected' : ''; ?>>GBP - British Pound</option>
                                            <option value="CAD" data-symbol="C$" <?php echo $transfer['currency'] === 'CAD' ? 'selected' : ''; ?>>CAD - Canadian Dollar</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Status:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <select name="status" class="form-select" required>
                                            <option value="pending" <?php echo $transfer['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                            <option value="completed" <?php echo $transfer['status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                            <option value="failed" <?php echo $transfer['status'] === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                            <option value="cancelled" <?php echo $transfer['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Transfer Fee:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" name="transfer_fee" class="form-control" value="<?php echo $transfer['fee'] ?? 0; ?>" step="0.01" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Description:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <textarea name="narration" class="form-control" rows="3" placeholder="Enter transfer description..."><?php echo htmlspecialchars($transfer['description'] ?? ''); ?></textarea>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Created Date:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <input type="datetime-local" name="created_at" class="form-control"
                                               value="<?php echo date('Y-m-d\TH:i', strtotime($transfer['created_at'])); ?>"
                                               title="Edit the transfer creation date and time">
                                        <small class="text-muted">Allows backdating - system time will not override this</small>
                                    </div>
                                </div>

                                <?php if ($transfer_type === 'inter-bank'): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Completed Date:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <input type="datetime-local" name="completed_at" class="form-control"
                                               value="<?php echo !empty($transfer['completed_at']) ? date('Y-m-d\TH:i', strtotime($transfer['completed_at'])) : ''; ?>"
                                               title="Edit the transfer completion date and time">
                                        <small class="text-muted">Allows backdating - leave empty if not completed</small>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Sender & Recipient Information Card -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title mb-0">
                                    <i class="fas fa-users me-2"></i>
                                    Sender & Recipient
                                </h3>
                            </div>
                            <div class="card-body">
                                <!-- Sender Details -->
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user me-1"></i>
                                    Sender Details
                                </h6>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Name:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span><?php echo htmlspecialchars(($transfer['sender_first_name'] ?? 'Unknown') . ' ' . ($transfer['sender_last_name'] ?? 'User')); ?></span>
                                        <br><small class="text-muted">@<?php echo htmlspecialchars($transfer['sender_username'] ?? 'unknown'); ?></small>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Account:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span><?php echo htmlspecialchars($transfer['sender_account'] ?? 'N/A'); ?></span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="form-label mb-0 fw-bold">Email:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span><?php echo htmlspecialchars($transfer['sender_email'] ?? 'N/A'); ?></span>
                                    </div>
                                </div>

                                <!-- Recipient Details -->
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-user-check me-1"></i>
                                    Recipient Details
                                </h6>

                                <?php if ($transfer_type === 'local-bank'): ?>
                                    <!-- Local Bank Transfer Fields -->
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 fw-bold">Name:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <input type="text" name="beneficiary_account_name" class="form-control form-control-sm"
                                                   value="<?php echo htmlspecialchars($transfer['beneficiary_account_name'] ?? ''); ?>"
                                                   placeholder="Enter beneficiary name">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 fw-bold">Account:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <input type="text" name="beneficiary_account_number" class="form-control form-control-sm"
                                                   value="<?php echo htmlspecialchars($transfer['beneficiary_account_number'] ?? ''); ?>"
                                                   placeholder="Enter account number">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 fw-bold">Bank:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <input type="text" name="beneficiary_bank_name" class="form-control form-control-sm"
                                                   value="<?php echo htmlspecialchars($transfer['beneficiary_bank_name'] ?? ''); ?>"
                                                   placeholder="Enter bank name">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 fw-bold">Routing:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <input type="text" name="routing_code" class="form-control form-control-sm"
                                                   value="<?php echo htmlspecialchars($transfer['routing_code'] ?? ''); ?>"
                                                   placeholder="Bank routing number">
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 fw-bold">Type:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <select name="account_type" class="form-select form-select-sm">
                                                <option value="">Select Type</option>
                                                <option value="checking" <?php echo ($transfer['account_type'] ?? '') === 'checking' ? 'selected' : ''; ?>>Checking</option>
                                                <option value="savings" <?php echo ($transfer['account_type'] ?? '') === 'savings' ? 'selected' : ''; ?>>Savings</option>
                                                <option value="business" <?php echo ($transfer['account_type'] ?? '') === 'business' ? 'selected' : ''; ?>>Business</option>
                                            </select>
                                        </div>
                                    </div>

                                <?php else: ?>
                                    <!-- Inter-Bank Transfer Fields (Read-only) -->
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 fw-bold">Name:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <span><?php echo htmlspecialchars($transfer['recipient_name'] ?? 'Unknown'); ?></span>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 fw-bold">Account:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <span><?php echo htmlspecialchars($transfer['recipient_account'] ?? 'N/A'); ?></span>
                                        </div>
                                    </div>

                                    <?php if (!empty($transfer['recipient_first_name']) && !empty($transfer['recipient_last_name'])): ?>
                                    <div class="row mb-3">
                                        <div class="col-sm-4">
                                            <label class="form-label mb-0 fw-bold">User:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <span><?php echo htmlspecialchars($transfer['recipient_first_name'] . ' ' . $transfer['recipient_last_name']); ?></span>
                                            <br><small class="text-muted">@<?php echo htmlspecialchars($transfer['recipient_username'] ?? 'unknown'); ?></small>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Save Changes
                            </button>
                            <a href="view.php?id=<?php echo $transfer['id']; ?>&type=<?php echo $transfer_type; ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                            <a href="../transfers.php" class="btn btn-outline-primary">
                                <i class="fas fa-list me-2"></i>
                                Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </form>

<script>
// Add client-side validation and UX improvements
document.addEventListener('DOMContentLoaded', function() {
    // Highlight required fields on validation
    const requiredFields = document.querySelectorAll('input[required], select[required]');
    requiredFields.forEach(field => {
        field.addEventListener('invalid', function() {
            this.classList.add('is-invalid');
        });

        field.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });

    // Form submits directly without confirmation for better UX

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.querySelector('.btn-close')) {
                alert.querySelector('.btn-close').click();
            }
        }, 5000);
    });
});

// Update currency symbol when currency selection changes
function updateCurrencySymbol(currencyCode) {
    const currencySelect = document.querySelector('select[name="currency"]');
    const selectedOption = currencySelect.querySelector(`option[value="${currencyCode}"]`);
    const symbol = selectedOption ? selectedOption.getAttribute('data-symbol') : '$';

    const currencySymbolElement = document.getElementById('currency-symbol');
    if (currencySymbolElement) {
        currencySymbolElement.textContent = symbol;
    }
}
</script>

<?php include '../includes/admin-footer.php'; ?>