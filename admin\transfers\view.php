<?php
require_once '../../config/config.php';
requireAdmin();

$page_title = 'Transfer Details';

// Get transfer ID from URL
$transfer_id = intval($_GET['id'] ?? 0);
$transfer_type = $_GET['type'] ?? '';

if (!$transfer_id) {
    header('Location: ../transfers.php');
    exit;
}

try {
    $db = getDB();
    
    // First, try to determine the transfer type by checking both tables
    if (empty($transfer_type)) {
        // Check local_transfers first
        $check_local = "SELECT 'local-bank' as transfer_type FROM local_transfers WHERE id = ?";
        $local_result = $db->query($check_local, [$transfer_id]);
        
        if ($local_result && $local_result->num_rows > 0) {
            $transfer_type = 'local-bank';
        } else {
            // Check interbank_transfers
            $check_interbank = "SELECT 'inter-bank' as transfer_type FROM interbank_transfers WHERE id = ?";
            $interbank_result = $db->query($check_interbank, [$transfer_id]);
            
            if ($interbank_result && $interbank_result->num_rows > 0) {
                $transfer_type = 'inter-bank';
            }
        }
    }
    
    // Get transfer details based on type
    if ($transfer_type === 'local-bank') {
        $query = "SELECT lt.*,
                  sender.first_name as sender_first_name, sender.last_name as sender_last_name,
                  sender.username as sender_username, sender.account_number as sender_account,
                  sender.email as sender_email, sender.phone as sender_phone,
                  lt.beneficiary_account_name as recipient_name,
                  lt.beneficiary_account_number as recipient_account,
                  lt.beneficiary_bank_name as bank_name,
                  lt.routing_code as routing_code,
                  lt.account_type as account_type,
                  'local-bank' as transfer_type,
                  lt.transfer_fee as fee,
                  lt.narration as description
                  FROM local_transfers lt
                  LEFT JOIN accounts sender ON lt.sender_id = sender.id
                  WHERE lt.id = ?";
    } elseif ($transfer_type === 'inter-bank') {
        $query = "SELECT it.*,
                  sender.first_name as sender_first_name, sender.last_name as sender_last_name,
                  sender.username as sender_username, sender.account_number as sender_account,
                  sender.email as sender_email, sender.phone as sender_phone,
                  recipient.first_name as recipient_first_name, recipient.last_name as recipient_last_name,
                  recipient.username as recipient_username, recipient.account_number as recipient_account,
                  recipient.email as recipient_email, recipient.phone as recipient_phone,
                  CONCAT(recipient.first_name, ' ', recipient.last_name) as recipient_name,
                  'inter-bank' as transfer_type,
                  it.transfer_fee as fee,
                  it.narration as description
                  FROM interbank_transfers it
                  LEFT JOIN accounts sender ON it.sender_id = sender.id
                  LEFT JOIN accounts recipient ON it.recipient_id = recipient.id
                  WHERE it.id = ?";
    } else {
        // Fallback to old transfers table for backward compatibility
        $query = "SELECT t.*,
                  sender.first_name as sender_first_name, sender.last_name as sender_last_name,
                  sender.username as sender_username, sender.account_number as sender_account,
                  sender.email as sender_email, sender.phone as sender_phone,
                  recipient.first_name as recipient_first_name, recipient.last_name as recipient_last_name,
                  recipient.username as recipient_username, recipient.account_number as recipient_account,
                  recipient.email as recipient_email, recipient.phone as recipient_phone
                  FROM transfers t
                  LEFT JOIN accounts sender ON t.sender_id = sender.id
                  LEFT JOIN accounts recipient ON t.recipient_id = recipient.id
                  WHERE t.id = ? AND t.transfer_type IN ('local', 'bitcoin')";
    }
    
    $result = $db->query($query, [$transfer_id]);
    $transfer = $result->fetch_assoc();
    
    if (!$transfer) {
        header('Location: ../transfers.php?error=Transfer not found');
        exit;
    }
    
} catch (Exception $e) {
    $error = "Failed to load transfer: " . $e->getMessage();
}

include '../includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="../index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="../transfers.php">Transfers</a></li>
        <li class="breadcrumb-item active" aria-current="page">Transfer #<?php echo $transfer['id']; ?></li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Transfer Details -->
<div class="row g-3">
    <!-- Transfer Information Card -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Transfer Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Transfer ID:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="fw-bold">#<?php echo $transfer['id']; ?></span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Reference:</label>
                    </div>
                    <div class="col-sm-8">
                        <code class="small"><?php echo htmlspecialchars($transfer['transaction_id'] ?? 'N/A'); ?></code>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Amount:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="fw-bold text-success fs-5">$<?php echo number_format($transfer['amount'], 2); ?></span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Type:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge" style="background: var(--primary-color); color: white;">
                            <?php echo ucfirst(str_replace('_', ' ', $transfer['transfer_type'] ?? 'transfer')); ?>
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Status:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge" style="background: var(--primary-color); color: white;">
                            <?php echo ucfirst($transfer['status']); ?>
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Currency:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php echo $transfer['currency'] ?? 'USD'; ?>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Fee:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php if ($transfer['fee'] && $transfer['fee'] > 0): ?>
                            $<?php echo number_format($transfer['fee'], 2); ?>
                        <?php else: ?>
                            <span class="text-muted">No fee</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Priority:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php echo ucfirst($transfer['priority'] ?? 'normal'); ?>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Processing Time:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php echo htmlspecialchars($transfer['processing_time'] ?? 'Standard'); ?>
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Transfer Date:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php echo date('M d, Y \a\t g:i A', strtotime($transfer['transfer_date'] ?? $transfer['created_at'])); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sender & Recipient Information Card -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    Sender & Recipient
                </h3>
            </div>
            <div class="card-body">
                <!-- Sender Information -->
                <div class="mb-4">
                    <h6 class="fw-bold text-primary mb-2">Sender Details</h6>
                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Name:</label>
                        </div>
                        <div class="col-sm-8">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-2" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; font-size: 0.7rem;">
                                    <?php echo strtoupper(substr($transfer['sender_first_name'] ?? 'U', 0, 1) . substr($transfer['sender_last_name'] ?? 'U', 0, 1)); ?>
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars(($transfer['sender_first_name'] ?? 'Unknown') . ' ' . ($transfer['sender_last_name'] ?? 'User')); ?></div>
                                    <small class="text-muted">@<?php echo htmlspecialchars($transfer['sender_username'] ?? 'unknown'); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Account:</label>
                        </div>
                        <div class="col-sm-8">
                            <code class="small"><?php echo htmlspecialchars($transfer['sender_account'] ?? 'N/A'); ?></code>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Email:</label>
                        </div>
                        <div class="col-sm-8">
                            <?php echo htmlspecialchars($transfer['sender_email'] ?? 'N/A'); ?>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Phone:</label>
                        </div>
                        <div class="col-sm-8">
                            <?php echo htmlspecialchars($transfer['sender_phone'] ?? 'N/A'); ?>
                        </div>
                    </div>
                </div>

                <!-- Recipient Information -->
                <div>
                    <h6 class="fw-bold text-success mb-2">Recipient Details</h6>
                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Name:</label>
                        </div>
                        <div class="col-sm-8">
                            <?php if (!empty($transfer['recipient_first_name'])): ?>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; font-size: 0.7rem;">
                                        <?php echo strtoupper(substr($transfer['recipient_first_name'] ?? 'U', 0, 1) . substr($transfer['recipient_last_name'] ?? 'U', 0, 1)); ?>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars(($transfer['recipient_first_name'] ?? 'Unknown') . ' ' . ($transfer['recipient_last_name'] ?? 'User')); ?></div>
                                        <small class="text-muted">@<?php echo htmlspecialchars($transfer['recipient_username'] ?? 'unknown'); ?></small>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="fw-bold"><?php echo htmlspecialchars($transfer['recipient_name'] ?? 'Unknown'); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Account:</label>
                        </div>
                        <div class="col-sm-8">
                            <code class="small"><?php echo htmlspecialchars($transfer['recipient_account'] ?? 'N/A'); ?></code>
                        </div>
                    </div>

                    <?php if (!empty($transfer['recipient_email'])): ?>
                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Email:</label>
                        </div>
                        <div class="col-sm-8">
                            <?php echo htmlspecialchars($transfer['recipient_email']); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($transfer['recipient_phone'])): ?>
                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Phone:</label>
                        </div>
                        <div class="col-sm-8">
                            <?php echo htmlspecialchars($transfer['recipient_phone']); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if (isset($transfer['bank_name']) && $transfer['bank_name']): ?>
                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Bank:</label>
                        </div>
                        <div class="col-sm-8">
                            <?php echo htmlspecialchars($transfer['bank_name']); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <hr>

                <!-- Timeline -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <strong>Created:</strong><br>
                            <?php echo date('M d, Y \a\t g:i A', strtotime($transfer['created_at'])); ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2">
                            <strong>Last Updated:</strong><br>
                            <?php
                            $updated_at = isset($transfer['updated_at']) ? $transfer['updated_at'] : $transfer['created_at'];
                            echo date('M d, Y \a\t g:i A', strtotime($updated_at));
                            ?>
                            <?php if (isset($transfer['updated_at']) && $transfer['updated_at'] !== $transfer['created_at']): ?>
                                <small class="badge bg-warning ms-1">Modified</small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Transfer Details -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Complete Transfer Details
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <?php if (isset($transfer['exchange_rate']) && $transfer['exchange_rate'] && $transfer['exchange_rate'] != 1.0000): ?>
                        <div class="row mb-3">
                            <div class="col-sm-5">
                                <label class="form-label mb-0 fw-bold">Exchange Rate:</label>
                            </div>
                            <div class="col-sm-7">
                                <?php echo number_format($transfer['exchange_rate'], 4); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($transfer['completed_at']) && !empty($transfer['completed_at'])): ?>
                        <div class="row mb-3">
                            <div class="col-sm-5">
                                <label class="form-label mb-0 fw-bold">Completed At:</label>
                            </div>
                            <div class="col-sm-7">
                                <?php echo date('M d, Y \a\t g:i A', strtotime($transfer['completed_at'])); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($transfer['processing_status']) && $transfer['processing_status']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-5">
                                <label class="form-label mb-0 fw-bold">Processing Status:</label>
                            </div>
                            <div class="col-sm-7">
                                <span class="badge" style="background: var(--primary-color); color: white;">
                                    <?php echo ucfirst($transfer['processing_status']); ?>
                                </span>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($transfer['purpose_of_payment']) && $transfer['purpose_of_payment']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-5">
                                <label class="form-label mb-0 fw-bold">Purpose:</label>
                            </div>
                            <div class="col-sm-7">
                                <?php echo htmlspecialchars($transfer['purpose_of_payment']); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-6">
                        <?php if (isset($transfer['swift_code']) && $transfer['swift_code']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-5">
                                <label class="form-label mb-0 fw-bold">SWIFT Code:</label>
                            </div>
                            <div class="col-sm-7">
                                <code><?php echo htmlspecialchars($transfer['swift_code']); ?></code>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($transfer['routing_code']) && $transfer['routing_code']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-5">
                                <label class="form-label mb-0 fw-bold">Routing Code:</label>
                            </div>
                            <div class="col-sm-7">
                                <code><?php echo htmlspecialchars($transfer['routing_code']); ?></code>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($transfer['iban']) && $transfer['iban']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-5">
                                <label class="form-label mb-0 fw-bold">IBAN:</label>
                            </div>
                            <div class="col-sm-7">
                                <code><?php echo htmlspecialchars($transfer['iban']); ?></code>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (isset($transfer['admin_notes']) && $transfer['admin_notes']): ?>
                        <div class="row mb-3">
                            <div class="col-sm-5">
                                <label class="form-label mb-0 fw-bold">Admin Notes:</label>
                            </div>
                            <div class="col-sm-7">
                                <div class="alert alert-info mb-0 p-2">
                                    <?php echo nl2br(htmlspecialchars($transfer['admin_notes'])); ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (isset($transfer['description']) && $transfer['description']): ?>
<!-- Description Card -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-comment-alt me-2"></i>
                    Description
                </h3>
            </div>
            <div class="card-body">
                <p class="mb-0"><?php echo nl2br(htmlspecialchars($transfer['description'])); ?></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2">
            <a href="../transfers.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Transfers
            </a>
            <?php
            // All transfers use the same edit page in transfers folder
            $edit_url = "edit.php?id=" . $transfer['id'];
            ?>
            <a href="<?php echo $edit_url; ?>" class="btn" style="background: var(--primary-color); color: white;">
                <i class="fas fa-edit me-2"></i>
                Edit Transfer
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print me-2"></i>
                Print Details
            </button>
        </div>
    </div>
</div>

<?php include '../includes/admin-footer.php'; ?>
