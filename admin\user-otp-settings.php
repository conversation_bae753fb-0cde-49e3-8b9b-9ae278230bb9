<?php
/**
 * Admin Per-User OTP Settings Management
 * Manage OTP requirements for individual users
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../admin/login.php');
}

// Get database connection
$db = getDB();

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_user_otp':
                    $user_id = (int)$_POST['user_id'];
                    $otp_enabled = isset($_POST['otp_enabled']) ? 1 : 0;
                    
                    // Validate user exists and is not admin
                    $user_check_sql = "SELECT id, username, first_name, last_name FROM accounts WHERE id = ? AND is_admin = 0";
                    $user_result = $db->query($user_check_sql, [$user_id]);
                    
                    if (!$user_result || $user_result->num_rows === 0) {
                        throw new Exception('Invalid user selected');
                    }
                    
                    $user = $user_result->fetch_assoc();
                    
                    // Update or insert user security settings
                    $update_sql = "INSERT INTO user_security_settings (user_id, otp_enabled, created_by, updated_by, updated_at) 
                                   VALUES (?, ?, ?, ?, NOW()) 
                                   ON DUPLICATE KEY UPDATE 
                                   otp_enabled = VALUES(otp_enabled), 
                                   updated_by = VALUES(updated_by),
                                   updated_at = VALUES(updated_at)";
                    $db->query($update_sql, [$user_id, $otp_enabled, $_SESSION['user_id'], $_SESSION['user_id']]);
                    
                    $status_text = $otp_enabled ? 'enabled' : 'disabled';
                    $success_message = "OTP requirement {$status_text} for user: {$user['first_name']} {$user['last_name']} ({$user['username']})";
                    break;
                    
                case 'bulk_update_otp':
                    $user_ids = $_POST['user_ids'] ?? [];
                    $bulk_action = $_POST['bulk_action'] ?? '';
                    
                    if (empty($user_ids) || !in_array($bulk_action, ['enable', 'disable'])) {
                        throw new Exception('Invalid bulk action or no users selected');
                    }
                    
                    $otp_enabled = ($bulk_action === 'enable') ? 1 : 0;
                    $updated_count = 0;
                    
                    foreach ($user_ids as $user_id) {
                        $user_id = (int)$user_id;
                        if ($user_id > 0) {
                            $update_sql = "INSERT INTO user_security_settings (user_id, otp_enabled, created_by, updated_by, updated_at) 
                                           VALUES (?, ?, ?, ?, NOW()) 
                                           ON DUPLICATE KEY UPDATE 
                                           otp_enabled = VALUES(otp_enabled), 
                                           updated_by = VALUES(updated_by),
                                           updated_at = VALUES(updated_at)";
                            $db->query($update_sql, [$user_id, $otp_enabled, $_SESSION['user_id'], $_SESSION['user_id']]);
                            $updated_count++;
                        }
                    }
                    
                    $status_text = $otp_enabled ? 'enabled' : 'disabled';
                    $success_message = "OTP requirement {$status_text} for {$updated_count} users";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Pagination setup
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Filter setup
$search_query = sanitizeInput($_GET['search'] ?? '');
$otp_filter = sanitizeInput($_GET['otp_filter'] ?? '');

// Build WHERE clause
$where_conditions = ["a.is_admin = 0"]; // Only show regular users
$params = [];

if (!empty($search_query)) {
    $where_conditions[] = "(a.username LIKE ? OR a.first_name LIKE ? OR a.last_name LIKE ? OR a.email LIKE ? OR a.account_number LIKE ?)";
    $search_param = "%{$search_query}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

if ($otp_filter !== '') {
    if ($otp_filter === 'enabled') {
        $where_conditions[] = "COALESCE(uss.otp_enabled, 1) = 1";
    } elseif ($otp_filter === 'disabled') {
        $where_conditions[] = "COALESCE(uss.otp_enabled, 1) = 0";
    }
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total 
              FROM accounts a 
              LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
              {$where_clause}";
$count_result = $db->query($count_sql, $params);
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// Get users with their OTP settings
$users_sql = "SELECT 
                a.id, a.account_number, a.username, a.first_name, a.last_name, 
                a.email, a.status, a.created_at,
                COALESCE(uss.otp_enabled, 1) as otp_enabled,
                uss.updated_at as otp_updated_at
              FROM accounts a 
              LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
              {$where_clause}
              ORDER BY a.first_name, a.last_name 
              LIMIT {$per_page} OFFSET {$offset}";

$users_result = $db->query($users_sql, $params);
$users = [];
if ($users_result) {
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
}

// Get statistics
$stats_sql = "SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN COALESCE(uss.otp_enabled, 1) = 1 THEN 1 END) as otp_enabled_count,
                COUNT(CASE WHEN COALESCE(uss.otp_enabled, 1) = 0 THEN 1 END) as otp_disabled_count
              FROM accounts a 
              LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
              WHERE a.is_admin = 0";
$stats_result = $db->query($stats_sql);
$stats = $stats_result->fetch_assoc();

$page_title = 'Local Transfer OTP Settings';

// Define page actions
$page_actions = [
    [
        'url' => 'transfers.php',
        'label' => 'Back to Transfers',
        'icon' => 'fas fa-arrow-left'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="transfers.php">Transfers</a></li>
        <li class="breadcrumb-item active" aria-current="page">Local Transfer OTP Settings</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (!empty($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-4">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-users"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_users']); ?></div>
                        <div class="text-muted">Total Users</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-4">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-shield-alt"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['otp_enabled_count']); ?></div>
                        <div class="text-muted">OTP Enabled</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-4">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-shield-alt"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['otp_disabled_count']); ?></div>
                        <div class="text-muted">OTP Disabled</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-filter me-2"></i>Filters & Search
        </h3>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">Search Users</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="Username, name, email, account number..." 
                       value="<?php echo htmlspecialchars($search_query); ?>">
            </div>
            
            <div class="col-md-3">
                <label class="form-label">OTP Status</label>
                <select name="otp_filter" class="form-select">
                    <option value="">All Users</option>
                    <option value="enabled" <?php echo $otp_filter === 'enabled' ? 'selected' : ''; ?>>OTP Enabled</option>
                    <option value="disabled" <?php echo $otp_filter === 'disabled' ? 'selected' : ''; ?>>OTP Disabled</option>
                </select>
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                <a href="user-otp-settings.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Explanation Card -->
<div class="card mb-4" style="background-color: white;">
    <div class="card-body" style="background-color: white;">
        <div class="row align-items-center">
            <div class="col-auto">
                <span class="bg-primary text-white avatar">
                    <i class="fas fa-info-circle"></i>
                </span>
            </div>
            <div class="col">
                <h4 class="card-title mb-1">Understanding OTP Settings</h4>
                <p class="text-muted mb-0">
                    This page manages <strong>Local Transfer OTP</strong> settings for users. The <span class="badge bg-secondary" style="color: white;">Account Status</span> 
                    shows whether a user's account is active, suspended, etc. The <span class="badge bg-primary" style="color: white;">Local Transfer OTP</span> 
                    shows whether OTP is required for local transfers (independent of account status).
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Users OTP Management -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-users-cog me-2"></i>Local Transfer OTP Management
        </h3>
        <div class="card-actions">
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cog me-1"></i>Bulk Actions
                </button>
                <div class="dropdown-menu">
                    <button class="dropdown-item" onclick="bulkAction('enable')">
                        <i class="fas fa-shield-alt text-success me-2"></i>Enable OTP for Selected
                    </button>
                    <button class="dropdown-item" onclick="bulkAction('disable')">
                        <i class="fas fa-shield-alt text-warning me-2"></i>Disable OTP for Selected
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($users)): ?>
        <form id="bulk-form" method="POST">
            <input type="hidden" name="action" value="bulk_update_otp">
            <input type="hidden" name="bulk_action" id="bulk_action">

            <div class="table-responsive">
                <table class="table table-vcenter card-table">
                    <thead>
                        <tr>
                            <th class="w-1">
                                <input class="form-check-input" type="checkbox" id="select-all">
                            </th>
                            <th>User Information</th>
                            <th>Account Details</th>
                            <th>
                                Account Status
                                <small class="text-muted d-block">Active/Suspended/etc.</small>
                            </th>
                            <th>
                                Local Transfer OTP
                                <small class="text-muted d-block">Enabled/Disabled</small>
                            </th>
                            <th>OTP Last Updated</th>
                            <th class="w-1">Quick Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <input class="form-check-input user-checkbox" type="checkbox"
                                       name="user_ids[]" value="<?php echo $user['id']; ?>">
                            </td>
                            <td>
                                <div class="d-flex py-1 align-items-center">
                                    <span class="avatar bg-primary text-white me-2">
                                        <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                    </span>
                                    <div class="flex-fill">
                                        <div class="font-weight-medium"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                        <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                        <div class="text-muted small"><?php echo htmlspecialchars($user['email']); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="font-monospace"><?php echo htmlspecialchars($user['account_number']); ?></div>
                                <div class="text-muted small">Joined <?php echo date('M Y', strtotime($user['created_at'])); ?></div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?> text-white">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-<?php echo $user['otp_enabled'] ? 'success' : 'warning'; ?> text-white me-2">
                                        <i class="fas fa-<?php echo $user['otp_enabled'] ? 'shield-alt' : 'shield-alt'; ?>"></i>
                                        <?php echo $user['otp_enabled'] ? 'ENABLED' : 'DISABLED'; ?>
                                    </span>
                                    <form method="POST" class="d-inline" onsubmit="return confirmOTPChange(this)">
                                        <input type="hidden" name="action" value="update_user_otp">
                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="otp_enabled"
                                                   <?php echo $user['otp_enabled'] ? 'checked' : ''; ?>
                                                   onchange="this.form.submit()" title="Toggle OTP Setting">
                                        </div>
                                    </form>
                                </div>
                            </td>
                            <td>
                                <?php if ($user['otp_updated_at']): ?>
                                    <div class="text-muted small">
                                        <?php echo date('M j, Y g:i A', strtotime($user['otp_updated_at'])); ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">Default</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-list flex-nowrap">
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                            onclick="quickToggleOTP(<?php echo $user['id']; ?>, <?php echo $user['otp_enabled'] ? 'false' : 'true'; ?>)">
                                        <i class="fas fa-<?php echo $user['otp_enabled'] ? 'shield-alt' : 'shield-alt'; ?>"></i>
                                        <?php echo $user['otp_enabled'] ? 'Disable' : 'Enable'; ?>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </form>
        <?php else: ?>
        <div class="empty">
            <div class="empty-icon">
                <i class="fas fa-users"></i>
            </div>
            <p class="empty-title">No users found</p>
            <p class="empty-subtitle text-muted">
                Try adjusting your search criteria or filters.
            </p>
        </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="card-footer d-flex align-items-center">
        <p class="m-0 text-muted">
            Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $per_page, $total_records); ?>
            of <?php echo $total_records; ?> entries
        </p>
        <ul class="pagination m-0 ms-auto">
            <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">
                    <i class="fas fa-chevron-left"></i> prev
                </a>
            </li>
            <?php endif; ?>

            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>">
                    <?php echo $i; ?>
                </a>
            </li>
            <?php endfor; ?>

            <?php if ($page < $total_pages): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">
                    next <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </div>
    <?php endif; ?>
</div>

<script>
// Select all functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk actions
function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Please select at least one user.');
        return;
    }

    const actionText = action === 'enable' ? 'enable' : 'disable';
    if (confirm(`Are you sure you want to ${actionText} OTP for ${checkedBoxes.length} selected user(s)?`)) {
        document.getElementById('bulk_action').value = action;
        document.getElementById('bulk-form').submit();
    }
}

// Quick toggle OTP
function quickToggleOTP(userId, enable) {
    const actionText = enable ? 'enable' : 'disable';
    if (confirm(`Are you sure you want to ${actionText} OTP for this user?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="update_user_otp">
            <input type="hidden" name="user_id" value="${userId}">
            ${enable ? '<input type="hidden" name="otp_enabled" value="1">' : ''}
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Confirm OTP change
function confirmOTPChange(form) {
    const checkbox = form.querySelector('input[name="otp_enabled"]');
    const action = checkbox.checked ? 'enable' : 'disable';
    return confirm(`Are you sure you want to ${action} OTP for this user?`);
}
</script>

<?php include 'includes/admin-footer.php'; ?>
