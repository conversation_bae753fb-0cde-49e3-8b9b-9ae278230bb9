<?php
/**
 * Admin Wire Transfer Management Page
 * Specialized page for managing wire transfers with dynamic fields
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../admin/login.php');
}

// Get database connection
$db = getDB();

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'update_wire_transfer_status':
                    $transfer_id = (int)$_POST['transfer_id'];
                    $new_status = sanitizeInput($_POST['status']);
                    $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');

                    // Validate status
                    $valid_statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled'];
                    if (!in_array($new_status, $valid_statuses)) {
                        throw new Exception('Invalid status selected');
                    }

                    // Update wire transfer status
                    $update_sql = "UPDATE transfers SET processing_status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ? AND transfer_type = 'international'";
                    $db->query($update_sql, [$new_status, $admin_notes, $transfer_id]);

                    $success_message = "Wire transfer status updated successfully";
                    break;

                case 'update_wire_transfer_details':
                    $transfer_id = (int)$_POST['transfer_id'];
                    $amount = floatval($_POST['amount']);
                    $status = sanitizeInput($_POST['status']);
                    $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');

                    // Get current wire transfer data
                    $current_sql = "SELECT wire_transfer_data FROM transfers WHERE id = ? AND transfer_type = 'international'";
                    $current_result = $db->query($current_sql, [$transfer_id]);
                    $current_data = $current_result->fetch_assoc();
                    
                    $wire_data = json_decode($current_data['wire_transfer_data'] ?? '{}', true);
                    
                    // Update dynamic fields
                    foreach ($_POST as $key => $value) {
                        if (strpos($key, 'wire_field_') === 0) {
                            $field_name = substr($key, 11); // Remove 'wire_field_' prefix
                            $wire_data[$field_name] = sanitizeInput($value);
                        }
                    }

                    // First, add missing columns if they don't exist
                    try {
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_name VARCHAR(150) DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS swift_code VARCHAR(20) DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS routing_code VARCHAR(50) DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS iban VARCHAR(50) DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_address TEXT DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_city VARCHAR(100) DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_country VARCHAR(50) DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS beneficiary_address TEXT DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS purpose_of_payment VARCHAR(200) DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS wire_transfer_data JSON DEFAULT NULL");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS processing_status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending'");
                        $db->query("ALTER TABLE transfers ADD COLUMN IF NOT EXISTS admin_notes TEXT DEFAULT NULL");
                    } catch (Exception $e) {
                        // Columns might already exist, continue
                    }

                    // Update transfer with both direct columns and JSON data
                    $update_sql = "UPDATE transfers SET
                                   amount = ?, processing_status = ?, admin_notes = ?, wire_transfer_data = ?,
                                   bank_name = ?, swift_code = ?, routing_code = ?, iban = ?,
                                   bank_address = ?, bank_city = ?, bank_country = ?,
                                   beneficiary_address = ?, purpose_of_payment = ?,
                                   recipient_name = ?, recipient_account = ?,
                                   updated_at = NOW()
                                   WHERE id = ? AND transfer_type = 'international'";

                    $db->query($update_sql, [
                        $amount, $status, $admin_notes, json_encode($wire_data),
                        $wire_data['bank_name'] ?? '',
                        $wire_data['swift_code'] ?? '',
                        $wire_data['routing_code'] ?? '',
                        $wire_data['iban'] ?? '',
                        $wire_data['bank_address'] ?? '',
                        $wire_data['bank_city'] ?? '',
                        $wire_data['bank_country'] ?? '',
                        $wire_data['beneficiary_address'] ?? '',
                        $wire_data['purpose_of_payment'] ?? '',
                        $wire_data['beneficiary_account_name'] ?? '',
                        $wire_data['beneficiary_account_number'] ?? '',
                        $transfer_id
                    ]);

                    $success_message = "Wire transfer details updated successfully";
                    break;
            }
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get wire transfer statistics
$stats_sql = "SELECT 
                COUNT(*) as total_wire_transfers,
                COUNT(CASE WHEN processing_status = 'completed' THEN 1 END) as completed_transfers,
                COUNT(CASE WHEN processing_status = 'pending' THEN 1 END) as pending_transfers,
                COUNT(CASE WHEN processing_status = 'processing' THEN 1 END) as processing_transfers,
                COUNT(CASE WHEN processing_status = 'failed' THEN 1 END) as failed_transfers,
                COALESCE(SUM(CASE WHEN processing_status = 'completed' THEN amount ELSE 0 END), 0) as total_amount
              FROM transfers WHERE transfer_type = 'international'";
$stats_result = $db->query($stats_sql);
$stats = $stats_result->fetch_assoc();

// Get wire transfer fields for dynamic rendering
$fields_sql = "SELECT * FROM wire_transfer_fields WHERE is_active = 1 ORDER BY field_group, display_order";
$fields_result = $db->query($fields_sql);
$wire_fields = [];
while ($field = $fields_result->fetch_assoc()) {
    $wire_fields[$field['field_group']][] = $field;
}

// Pagination and filtering
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Filter parameters
$status_filter = $_GET['status'] ?? '';
$search_query = $_GET['search'] ?? '';

// Build WHERE clause
$where_conditions = ["t.transfer_type = 'international'"];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "t.processing_status = ?";
    $params[] = $status_filter;
}

if (!empty($search_query)) {
    $where_conditions[] = "(t.transaction_id LIKE ? OR t.recipient_name LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ? OR t.bank_name LIKE ?)";
    $search_param = "%{$search_query}%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

$page_title = 'Wire Transfer Management';

// Define page actions
$page_actions = [
    [
        'url' => 'wire-transfer-fields.php',
        'label' => 'Manage Fields',
        'icon' => 'fas fa-cogs'
    ],
    [
        'url' => 'billing-code-settings.php',
        'label' => 'Billing Settings',
        'icon' => 'fas fa-key'
    ],
    [
        'url' => 'transfers.php',
        'label' => 'Regular Transfers',
        'icon' => 'fas fa-exchange-alt'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Wire Transfer Management</li>
    </ol>
</nav>

<!-- Success/Error Messages -->
<?php if (!empty($success_message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-globe"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_wire_transfers']); ?></div>
                        <div class="text-muted">Total Wire Transfers</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-check-circle"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['completed_transfers']); ?></div>
                        <div class="text-muted">Completed</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-clock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['pending_transfers'] + $stats['processing_transfers']); ?></div>
                        <div class="text-muted">Pending/Processing</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo formatCurrency($stats['total_amount'], 'USD'); ?></div>
                        <div class="text-muted">Total Amount</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Wire Transfers Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-globe me-2"></i>All Wire Transfers
                </h3>
                <div class="card-actions">
                    <!-- Filters -->
                    <form method="GET" class="d-flex gap-2">
                        <select name="status" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>

                        <input type="text" name="search" class="form-control form-control-sm"
                               placeholder="Search..." value="<?php echo htmlspecialchars($search_query); ?>">
                        <button type="submit" class="btn btn-sm btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if (!empty($status_filter) || !empty($search_query)): ?>
                        <a href="wire-transfers.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

        <?php
        // Get wire transfers with pagination
        $transfers_sql = "SELECT t.*,
                                 u.first_name, u.last_name, u.email, u.account_number as sender_account
                          FROM transfers t
                          LEFT JOIN accounts u ON t.sender_id = u.id
                          {$where_clause}
                          ORDER BY t.created_at DESC
                          LIMIT {$per_page} OFFSET {$offset}";

        $transfers_result = $db->query($transfers_sql, $params);
        $wire_transfers = [];
        while ($row = $transfers_result->fetch_assoc()) {
            $wire_transfers[] = $row;
        }

        // Get total count for pagination
        $count_sql = "SELECT COUNT(*) as total FROM transfers t LEFT JOIN accounts u ON t.sender_id = u.id {$where_clause}";
        $count_result = $db->query($count_sql, $params);
        $total_transfers = $count_result->fetch_assoc()['total'];
        $total_pages = ceil($total_transfers / $per_page);
        ?>

            <?php if (!empty($wire_transfers)): ?>
            <div class="table-responsive">
                <table class="table table-vcenter card-table">
                    <thead>
                        <tr>
                            <th class="w-1">#</th>
                            <th>Sender</th>
                            <th>Recipient</th>
                            <th>Bank</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th class="w-1">Actions</th>
                        </tr>
                    </thead>
                <tbody>
                    <?php
                    $row_number = ($page - 1) * $per_page + 1;
                    foreach ($wire_transfers as $transfer):
                    ?>
                    <tr>
                        <td>
                            <span class="text-muted"><?php echo $row_number++; ?></span>
                        </td>
                        <td>
                            <div><?php echo htmlspecialchars($transfer['first_name'] . ' ' . $transfer['last_name']); ?></div>
                            <small class="text-muted"><?php echo htmlspecialchars($transfer['sender_account']); ?></small>
                        </td>
                        <td>
                            <div class="font-weight-medium"><?php echo htmlspecialchars($transfer['recipient_name']); ?></div>
                            <small class="text-muted"><?php echo htmlspecialchars($transfer['recipient_account']); ?></small>
                        </td>
                        <td>
                            <div><?php echo htmlspecialchars($transfer['bank_name'] ?? 'N/A'); ?></div>
                            <small class="text-muted"><?php echo htmlspecialchars($transfer['swift_code'] ?? ''); ?></small>
                        </td>
                        <td>
                            <div class="font-weight-medium"><?php echo formatCurrency($transfer['amount'], $transfer['currency']); ?></div>
                            <?php if (isset($transfer['fee']) && $transfer['fee'] > 0): ?>
                            <small class="text-muted">Fee: <?php echo formatCurrency($transfer['fee'], $transfer['currency']); ?></small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $status_badges = [
                                'pending' => '<span class="badge bg-warning">Pending</span>',
                                'processing' => '<span class="badge bg-info">Processing</span>',
                                'completed' => '<span class="badge bg-success">Completed</span>',
                                'failed' => '<span class="badge bg-danger">Failed</span>',
                                'cancelled' => '<span class="badge bg-secondary">Cancelled</span>'
                            ];
                            echo $status_badges[$transfer['processing_status']] ?? '<span class="badge bg-secondary">Unknown</span>';
                            ?>
                        </td>
                        <td>
                            <div><?php echo date('M j, Y', strtotime($transfer['created_at'])); ?></div>
                            <small class="text-muted"><?php echo date('g:i A', strtotime($transfer['created_at'])); ?></small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="wire-transfers/view.php?id=<?php echo $transfer['id']; ?>" 
                                   class="btn btn-outline-primary" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="wire-transfers/edit.php?id=<?php echo $transfer['id']; ?>" 
                                   class="btn btn-outline-success" title="Edit Transfer">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info wire-status-btn"
                                        data-transfer-id="<?php echo $transfer['id']; ?>"
                                        data-current-status="<?php echo $transfer['processing_status']; ?>"
                                        title="Update Status">
                                    <i class="fas fa-tasks"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="card-footer d-flex align-items-center">
            <p class="m-0 text-muted">
                Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $per_page, $total_transfers); ?>
                of <?php echo $total_transfers; ?> wire transfers
            </p>
            <ul class="pagination m-0 ms-auto">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                        <i class="fas fa-chevron-left"></i> prev
                    </a>
                </li>
                <?php endif; ?>

                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>

                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search_query); ?>">
                        next <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </div>
        <?php endif; ?>

            <?php else: ?>
            <div class="card-body">
                <div class="text-center py-5">
                    <div class="empty">
                        <div class="empty-icon">
                            <i class="fas fa-globe fa-3x text-muted"></i>
                        </div>
                        <p class="empty-title">No wire transfers found</p>
                        <p class="empty-subtitle text-muted">
                            <?php if (!empty($status_filter) || !empty($search_query)): ?>
                            No wire transfers match your current filters. Try adjusting your search criteria.
                            <?php else: ?>
                            No wire transfers have been made yet. Wire transfers will appear here once users start making international transfers.
                            <?php endif; ?>
                        </p>
                        <?php if (!empty($status_filter) || !empty($search_query)): ?>
                        <div class="empty-action">
                            <a href="wire-transfers.php" class="btn btn-primary">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>






<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-tasks me-2"></i>Update Transfer Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="updateStatusForm">
                <input type="hidden" name="action" value="update_wire_transfer_status">
                <input type="hidden" name="transfer_id" id="statusTransferId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Processing Status</label>
                        <select name="status" class="form-control" id="statusSelect" required>
                            <option value="pending">Pending</option>
                            <option value="processing">Processing</option>
                            <option value="completed">Completed</option>
                            <option value="failed">Failed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Admin Notes</label>
                        <textarea name="admin_notes" class="form-control" rows="3" id="statusAdminNotes"
                                  placeholder="Add notes about this status change..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>




function updateWireTransferStatus(transferId, currentStatus) {
    try {
        document.getElementById('statusTransferId').value = transferId;
        document.getElementById('statusSelect').value = currentStatus;
        document.getElementById('statusAdminNotes').value = '';

        new bootstrap.Modal(document.getElementById('updateStatusModal')).show();

    } catch (error) {
        console.error('Error updating wire transfer status:', error);
        alert('Error loading status update form. Please try again.');
    }
}



// Quick action functions
function exportWireTransfers() {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('export', 'csv');
    window.location.href = currentUrl.toString();
}

function bulkStatusUpdate() {
    alert('Bulk status update feature coming soon. This will allow you to update multiple wire transfers at once.');
}

// Enhanced form validation and event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for wire transfer action buttons

    document.querySelectorAll('.wire-status-btn').forEach(button => {
        button.addEventListener('click', function() {
            try {
                const transferId = this.getAttribute('data-transfer-id');
                const currentStatus = this.getAttribute('data-current-status');
                updateWireTransferStatus(transferId, currentStatus);
            } catch (error) {
                console.error('Error loading status update:', error);
                alert('Error loading status update form. Please refresh the page and try again.');
            }
        });
    });



    // Add form validation for status update form
    const statusForm = document.getElementById('updateStatusForm');
    if (statusForm) {
        statusForm.addEventListener('submit', function(e) {
            const status = document.getElementById('statusSelect').value;
            if (!status) {
                e.preventDefault();
                alert('Please select a status.');
                return false;
            }
        });
    }
});
</script>

<?php include 'includes/admin-footer.php'; ?>
