<?php
require_once '../../config/config.php';
requireAdmin();

$page_title = 'Edit Wire Transfer';

// Get transfer ID from URL
$transfer_id = intval($_GET['id'] ?? 0);

if (!$transfer_id) {
    header('Location: ../wire-transfers.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        // Validate required fields
        $amount = floatval($_POST['amount'] ?? 0);
        $status = $_POST['status'] ?? '';
        $recipient_name = trim($_POST['recipient_name'] ?? '');
        $recipient_account = trim($_POST['recipient_account'] ?? '');
        
        if ($amount <= 0) {
            throw new Exception('Amount must be greater than 0');
        }
        
        if (empty($recipient_name)) {
            throw new Exception('Recipient name is required');
        }
        
        if (empty($recipient_account)) {
            throw new Exception('Recipient account is required');
        }
        
        // Get all configured wire transfer fields
        $fields_result = $db->query("SELECT field_name FROM wire_transfer_fields WHERE is_active = 1");
        $configured_field_names = [];
        while ($field = $fields_result->fetch_assoc()) {
            $configured_field_names[] = $field['field_name'];
        }
        
        // Collect wire transfer data from configured fields
        $wire_data = [];
        $static_fields = [
            'beneficiary_account_number', 'beneficiary_account_name', 'bank_name', 
            'swift_code', 'routing_code', 'iban', 'bank_address', 'bank_city', 
            'bank_country', 'beneficiary_address'
        ];
        
        foreach ($configured_field_names as $field_name) {
            if (isset($_POST[$field_name])) {
                $value = trim($_POST[$field_name]);
                if (!in_array($field_name, $static_fields)) {
                    // Store non-static fields in JSON
                    $wire_data[$field_name] = $value;
                }
            }
        }
        
        // Update transfer
        $update_sql = "UPDATE transfers SET 
                      amount = ?, 
                      status = ?, 
                      recipient_name = ?, 
                      recipient_account = ?, 
                      bank_name = ?, 
                      swift_code = ?, 
                      routing_code = ?, 
                      iban = ?, 
                      bank_address = ?, 
                      bank_city = ?, 
                      bank_country = ?, 
                      beneficiary_address = ?, 
                      purpose_of_payment = ?, 
                      description = ?, 
                      admin_notes = ?, 
                      wire_transfer_data = ?, 
                      updated_at = NOW() 
                      WHERE id = ? AND transfer_type = 'international'";
        
        $params = [
            $amount,
            $status,
            $_POST['beneficiary_account_name'] ?? $recipient_name,
            $_POST['beneficiary_account_number'] ?? $recipient_account,
            $_POST['bank_name'] ?? '',
            $_POST['swift_code'] ?? '',
            $_POST['routing_code'] ?? '',
            $_POST['iban'] ?? '',
            $_POST['bank_address'] ?? '',
            $_POST['bank_city'] ?? '',
            $_POST['bank_country'] ?? '',
            $_POST['beneficiary_address'] ?? '',
            $_POST['purpose_of_payment'] ?? '',
            $_POST['description'] ?? '',
            $_POST['admin_notes'] ?? '',
            json_encode($wire_data),
            $transfer_id
        ];
        
        $result = $db->query($update_sql, $params);
        
        if ($result) {
            $success = "Wire transfer updated successfully!";
        } else {
            throw new Exception('Failed to update wire transfer');
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

try {
    $db = getDB();
    
    // Get wire transfer details with user information
    $query = "SELECT t.*,
              sender.first_name, sender.last_name, sender.username, sender.account_number,
              recipient.first_name as recipient_first_name, recipient.last_name as recipient_last_name
              FROM transfers t
              LEFT JOIN accounts sender ON t.sender_id = sender.id
              LEFT JOIN accounts recipient ON t.recipient_id = recipient.id
              WHERE t.id = ? AND t.transfer_type = 'international'";
    
    $result = $db->query($query, [$transfer_id]);
    $transfer = $result->fetch_assoc();

    if (!$transfer) {
        header('Location: ../wire-transfers.php?error=Wire transfer not found');
        exit;
    }
    
} catch (Exception $e) {
    $error = "Failed to load wire transfer: " . $e->getMessage();
}

include '../includes/admin-header.php';
?>

<style>
    :root {
        --primary-color: #007bff;
        --primary-dark: #0056b3;
        --success-color: #28a745;
        --info-color: #17a2b8;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
    }

    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    .card-header {
        background-color: var(--light-color);
        border-bottom: 1px solid #dee2e6;
        border-radius: 0.5rem 0.5rem 0 0 !important;
        padding: 1rem 1.25rem;
    }

    .card-title {
        color: var(--dark-color);
        font-weight: 600;
        margin-bottom: 0;
    }

    .card-body {
        padding: 1.25rem;
    }

    .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }

    .avatar-sm {
        width: 24px;
        height: 24px;
        font-size: 0.7rem;
    }

    .form-label {
        font-weight: 500;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn {
        border-radius: 0.375rem;
        font-weight: 500;
        padding: 0.5rem 1rem;
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
    }

    .badge {
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
    }

    .text-primary {
        color: var(--primary-color) !important;
    }

    .text-success {
        color: var(--success-color) !important;
    }

    .text-info {
        color: var(--info-color) !important;
    }

    .text-warning {
        color: var(--warning-color) !important;
    }

    .text-danger {
        color: var(--danger-color) !important;
    }

    .bg-light {
        background-color: var(--light-color) !important;
    }

    .breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: #6c757d;
    }

    .alert {
        border: none;
        border-radius: 0.5rem;
        padding: 1rem;
    }

    .alert-success {
        background-color: #d1edff;
        color: #0c5460;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
    }

    .row.g-3 > * {
        padding-right: calc(var(--bs-gutter-x) * 0.5);
        padding-left: calc(var(--bs-gutter-x) * 0.5);
        margin-top: var(--bs-gutter-y);
    }

    @media (max-width: 768px) {
        .card-body {
            padding: 1rem;
        }
        
        .row.mb-3 {
            margin-bottom: 1rem !important;
        }
        
        .col-sm-4, .col-sm-8 {
            margin-bottom: 0.5rem;
        }
    }
</style>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="../index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="../wire-transfers.php">Wire Transfers</a></li>
        <li class="breadcrumb-item"><a href="view.php?id=<?php echo $transfer['id']; ?>">Transfer #<?php echo $transfer['id']; ?></a></li>
        <li class="breadcrumb-item active" aria-current="page">Edit</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Edit Form -->
<form method="POST" action="">
    
    <!-- Wire Transfer Details -->
    <div class="row g-3">
        <!-- Transfer Information Card -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>
                        Transfer Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Transfer ID:</label>
                        </div>
                        <div class="col-sm-8">
                            <span class="fw-bold">#<?php echo $transfer['id']; ?></span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Amount: <span class="text-danger">*</span></label>
                        </div>
                        <div class="col-sm-8">
                            <div class="input-group">
                                <span class="input-group-text"><?php echo getCurrencySymbol($transfer['currency'] ?? 'USD'); ?></span>
                                <input type="number" name="amount" class="form-control" value="<?php echo $transfer['amount']; ?>" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Status:</label>
                        </div>
                        <div class="col-sm-8">
                            <select name="status" class="form-select">
                                <option value="pending" <?php echo $transfer['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="processing" <?php echo $transfer['status'] === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                <option value="completed" <?php echo $transfer['status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="failed" <?php echo $transfer['status'] === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                <option value="cancelled" <?php echo $transfer['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Type:</label>
                        </div>
                        <div class="col-sm-8">
                            <span class="badge" style="background: var(--primary-color); color: white;">
                                <?php echo ucfirst(str_replace('_', ' ', $transfer['transfer_type'])); ?>
                            </span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Currency:</label>
                        </div>
                        <div class="col-sm-8">
                            <select name="currency" class="form-select">
                                <option value="USD" <?php echo ($transfer['currency'] ?? 'USD') === 'USD' ? 'selected' : ''; ?>>USD</option>
                                <option value="EUR" <?php echo ($transfer['currency'] ?? '') === 'EUR' ? 'selected' : ''; ?>>EUR</option>
                                <option value="GBP" <?php echo ($transfer['currency'] ?? '') === 'GBP' ? 'selected' : ''; ?>>GBP</option>
                                <option value="CAD" <?php echo ($transfer['currency'] ?? '') === 'CAD' ? 'selected' : ''; ?>>CAD</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Created:</label>
                        </div>
                        <div class="col-sm-8">
                            <?php echo date('M d, Y \a\t g:i A', strtotime($transfer['created_at'])); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sender & Recipient Information Card -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        Sender & Recipient
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Sender Information -->
                    <div class="mb-4">
                        <h6 class="fw-bold text-primary mb-2">Sender</h6>
                        <div class="row mb-2">
                            <div class="col-sm-4">
                                <label class="form-label mb-0 fw-bold">Name:</label>
                            </div>
                            <div class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; font-size: 0.7rem;">
                                        <?php echo strtoupper(substr($transfer['first_name'] ?? 'U', 0, 1) . substr($transfer['last_name'] ?? 'U', 0, 1)); ?>
                                    </div>
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars(($transfer['first_name'] ?? 'Unknown') . ' ' . ($transfer['last_name'] ?? 'User')); ?></div>
                                        <small class="text-muted">@<?php echo htmlspecialchars($transfer['username'] ?? 'unknown'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-sm-4">
                                <label class="form-label mb-0 fw-bold">Account:</label>
                            </div>
                            <div class="col-sm-8">
                                <code class="small"><?php echo htmlspecialchars($transfer['account_number'] ?? 'N/A'); ?></code>
                            </div>
                        </div>
                    </div>

                    <!-- Recipient Information -->
                    <div>
                        <h6 class="fw-bold text-success mb-2">Recipient</h6>
                        <div class="row mb-2">
                            <div class="col-sm-4">
                                <label class="form-label mb-0 fw-bold">Name: <span class="text-danger">*</span></label>
                            </div>
                            <div class="col-sm-8">
                                <input type="text" name="recipient_name" class="form-control" value="<?php echo htmlspecialchars($transfer['recipient_name'] ?? ''); ?>" placeholder="Enter recipient name" required>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-sm-4">
                                <label class="form-label mb-0 fw-bold">Account: <span class="text-danger">*</span></label>
                            </div>
                            <div class="col-sm-8">
                                <input type="text" name="recipient_account" class="form-control" value="<?php echo htmlspecialchars($transfer['recipient_account'] ?? ''); ?>" placeholder="Enter account number" required>
                            </div>
                        </div>

                        <div class="row mb-2">
                            <div class="col-sm-4">
                                <label class="form-label mb-0 fw-bold">Bank:</label>
                            </div>
                            <div class="col-sm-8">
                                <input type="text" name="bank_name" class="form-control" value="<?php echo htmlspecialchars($transfer['bank_name'] ?? ''); ?>" placeholder="Enter bank name">
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Timeline -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <strong>Created:</strong><br>
                                <?php echo date('M d, Y \a\t g:i A', strtotime($transfer['created_at'])); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2">
                                <strong>Last Updated:</strong><br>
                                <?php
                                $updated_at = $transfer['updated_at'] ?? $transfer['created_at'];
                                echo !empty($updated_at) ? date('M d, Y \a\t g:i A', strtotime($updated_at)) : 'Never';
                                ?>
                                <?php if (!empty($transfer['updated_at']) && $transfer['updated_at'] !== $transfer['created_at']): ?>
                                    <small class="badge bg-warning ms-1">Modified</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Transfer Details -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Transfer Details
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Purpose of Payment</label>
                            <input type="text" name="purpose_of_payment" class="form-control" value="<?php echo htmlspecialchars($transfer['purpose_of_payment'] ?? ''); ?>" placeholder="Enter purpose of payment">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Description</label>
                            <textarea name="description" class="form-control" rows="3" placeholder="Enter transfer description..."><?php echo htmlspecialchars($transfer['description'] ?? ''); ?></textarea>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">Admin Notes</label>
                        <textarea name="admin_notes" class="form-control" rows="2" placeholder="Internal admin notes (not visible to user)..."><?php echo htmlspecialchars($transfer['admin_notes'] ?? ''); ?></textarea>
                        <small class="form-text text-muted">Internal notes for administrative purposes only.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>





    <!-- Timeline Information -->
    <div class="card border-0 shadow-sm mt-4">
        <div class="card-header bg-light border-0">
            <h5 class="card-title mb-0">
                <i class="fas fa-clock text-warning me-2"></i>
                Timeline Information
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-success rounded-circle p-2 me-3">
                            <i class="fas fa-plus text-white"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Created</h6>
                            <p class="text-muted mb-0">
                                <?php echo date('M d, Y \a\t g:i A', strtotime($transfer['created_at'])); ?>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <div class="bg-info rounded-circle p-2 me-3">
                            <i class="fas fa-edit text-white"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Last Updated</h6>
                            <p class="text-muted mb-0">
                                <?php echo !empty($transfer['updated_at']) ? date('M d, Y \a\t g:i A', strtotime($transfer['updated_at'])) : 'Never'; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wire Transfer Fields -->
    <?php
    // Get configured fields grouped by field_group
    $fields_result = $db->query("SELECT * FROM wire_transfer_fields WHERE is_active = 1 ORDER BY field_group, display_order ASC");
    $configured_fields = [];
    while ($field = $fields_result->fetch_assoc()) {
        $configured_fields[$field['field_group']][] = $field;
    }

    // Get existing transfer data from both JSON and static columns
    $wire_transfer_data = json_decode($transfer['wire_transfer_data'] ?? '{}', true) ?: [];
    
    // Merge static column data with JSON data (JSON takes precedence)
    $all_transfer_data = array_merge([
        'beneficiary_account_number' => $transfer['recipient_account'] ?? '',
        'beneficiary_account_name' => $transfer['recipient_name'] ?? '',
        'bank_name' => $transfer['bank_name'] ?? '',
        'swift_code' => $transfer['swift_code'] ?? '',
        'routing_code' => $transfer['routing_code'] ?? '',
        'iban' => $transfer['iban'] ?? '',
        'bank_address' => $transfer['bank_address'] ?? '',
        'bank_city' => $transfer['bank_city'] ?? '',
        'bank_country' => $transfer['bank_country'] ?? '',
        'beneficiary_address' => $transfer['beneficiary_address'] ?? '',
    ], $wire_transfer_data);

    // Group icons and titles
    $group_config = [
        'beneficiary' => ['icon' => 'fas fa-user', 'title' => 'Beneficiary Information'],
        'bank' => ['icon' => 'fas fa-university', 'title' => 'Bank Information'],
        'additional' => ['icon' => 'fas fa-plus-circle', 'title' => 'Additional Information']
    ];

    if (!empty($configured_fields)): 
        foreach ($configured_fields as $group => $fields): ?>
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="<?php echo $group_config[$group]['icon'] ?? 'fas fa-cogs'; ?> me-2"></i>
                        <?php echo $group_config[$group]['title'] ?? ucfirst($group) . ' Information'; ?>
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($fields as $field): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <?php echo htmlspecialchars($field['field_label']); ?>
                                <?php if ($field['is_required']): ?>
                                <span class="text-danger">*</span>
                                <?php endif; ?>
                            </label>
                            
                            <?php if ($field['field_type'] === 'textarea'): ?>
                            <textarea 
                                name="<?php echo htmlspecialchars($field['field_name']); ?>" 
                                class="form-control" 
                                rows="3"
                                placeholder="<?php echo htmlspecialchars($field['placeholder'] ?? ''); ?>"
                                <?php echo $field['is_required'] ? 'required' : ''; ?>
                            ><?php echo htmlspecialchars($all_transfer_data[$field['field_name']] ?? ''); ?></textarea>
                            
                            <?php elseif ($field['field_type'] === 'select'): ?>
                            <select 
                                name="<?php echo htmlspecialchars($field['field_name']); ?>" 
                                class="form-select"
                                <?php echo $field['is_required'] ? 'required' : ''; ?>
                            >
                                <option value="">Select <?php echo htmlspecialchars($field['field_label']); ?></option>
                                <?php 
                                $options = explode(',', $field['field_options'] ?? '');
                                foreach ($options as $option): 
                                    $option = trim($option);
                                    if (!empty($option)):
                                ?>
                                <option value="<?php echo htmlspecialchars($option); ?>" 
                                        <?php echo ($all_transfer_data[$field['field_name']] ?? '') === $option ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($option); ?>
                                </option>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </select>
                            
                            <?php else: ?>
                            <input 
                                type="<?php echo htmlspecialchars($field['field_type']); ?>" 
                                name="<?php echo htmlspecialchars($field['field_name']); ?>" 
                                class="form-control" 
                                value="<?php echo htmlspecialchars($all_transfer_data[$field['field_name']] ?? ''); ?>"
                                placeholder="<?php echo htmlspecialchars($field['placeholder'] ?? ''); ?>"
                                <?php echo $field['is_required'] ? 'required' : ''; ?>
                            >
                            <?php endif; ?>
                            
                            <?php if (!empty($field['help_text'])): ?>
                            <small class="form-text text-muted"><?php echo htmlspecialchars($field['help_text']); ?></small>
                            <?php endif; ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
        <?php endforeach; 
    endif; ?>
    
    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-save me-1"></i>
                                Save Changes
                            </button>
                            <a href="view.php?id=<?php echo $transfer['id']; ?>" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-eye me-1"></i>
                                View Transfer
                            </a>
                        </div>
                        <div>
                            <a href="../wire-transfers.php" class="btn btn-outline-dark">
                                <i class="fas fa-arrow-left me-1"></i>
                                Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<?php include '../includes/admin-footer.php'; ?>
