<?php
require_once '../../config/config.php';
requireAdmin();

$page_title = 'Wire Transfer Details';

// Get transfer ID from URL
$transfer_id = intval($_GET['id'] ?? 0);

if (!$transfer_id) {
    header('Location: ../wire-transfers.php');
    exit;
}

try {
    $db = getDB();
    
    // Get wire transfer details with user information
    $query = "SELECT t.*,
              sender.first_name, sender.last_name, sender.username, sender.account_number,
              recipient.first_name as recipient_first_name, recipient.last_name as recipient_last_name
              FROM transfers t
              LEFT JOIN accounts sender ON t.sender_id = sender.id
              LEFT JOIN accounts recipient ON t.recipient_id = recipient.id
              WHERE t.id = ? AND t.transfer_type = 'international'";
    
    $result = $db->query($query, [$transfer_id]);
    $transfer = $result->fetch_assoc();

    if (!$transfer) {
        header('Location: ../wire-transfers.php?error=Wire transfer not found');
        exit;
    }
    
} catch (Exception $e) {
    $error = "Failed to load wire transfer: " . $e->getMessage();
}

include '../includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="../index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="../wire-transfers.php">Wire Transfers</a></li>
        <li class="breadcrumb-item active" aria-current="page">Transfer #<?php echo $transfer['id']; ?></li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Wire Transfer Details -->
<div class="row g-3">
    <!-- Transfer Information Card -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-globe me-2"></i>
                    International Transfer Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Transfer ID:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="fw-bold">#<?php echo $transfer['id']; ?></span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Amount:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="fw-bold text-success fs-5"><?php echo formatCurrency($transfer['amount'], $transfer['currency'] ?? 'USD'); ?></span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Status:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge" style="background: var(--primary-color); color: white;">
                            <?php echo ucfirst($transfer['status']); ?>
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Type:</label>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge" style="background: var(--primary-color); color: white;">
                            <?php echo ucfirst(str_replace('_', ' ', $transfer['transfer_type'])); ?>
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Currency:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php echo $transfer['currency'] ?? 'USD'; ?>
                    </div>
                </div>

                <div class="row mb-2">
                    <div class="col-sm-4">
                        <label class="form-label mb-0 fw-bold">Created:</label>
                    </div>
                    <div class="col-sm-8">
                        <?php echo date('M d, Y \a\t g:i A', strtotime($transfer['created_at'])); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sender & Recipient Information Card -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    Sender & Recipient
                </h3>
            </div>
            <div class="card-body">
                <!-- Sender Information -->
                <div class="mb-4">
                    <h6 class="fw-bold text-primary mb-2">Sender</h6>
                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Name:</label>
                        </div>
                        <div class="col-sm-8">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm me-2" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; font-size: 0.7rem;">
                                    <?php echo strtoupper(substr($transfer['first_name'] ?? 'U', 0, 1) . substr($transfer['last_name'] ?? 'U', 0, 1)); ?>
                                </div>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars(($transfer['first_name'] ?? 'Unknown') . ' ' . ($transfer['last_name'] ?? 'User')); ?></div>
                                    <small class="text-muted">@<?php echo htmlspecialchars($transfer['username'] ?? 'unknown'); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Account:</label>
                        </div>
                        <div class="col-sm-8">
                            <code class="small"><?php echo htmlspecialchars($transfer['account_number'] ?? 'N/A'); ?></code>
                        </div>
                    </div>
                </div>

                <!-- Recipient Information -->
                <div>
                    <h6 class="fw-bold text-success mb-2">Recipient</h6>
                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Name:</label>
                        </div>
                        <div class="col-sm-8">
                            <div class="fw-bold"><?php echo htmlspecialchars($transfer['recipient_name'] ?? 'Unknown'); ?></div>
                        </div>
                    </div>

                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Account:</label>
                        </div>
                        <div class="col-sm-8">
                            <code class="small"><?php echo htmlspecialchars($transfer['recipient_account'] ?? 'N/A'); ?></code>
                        </div>
                    </div>

                    <?php if ($transfer['bank_name']): ?>
                    <div class="row mb-2">
                        <div class="col-sm-4">
                            <label class="form-label mb-0 fw-bold">Bank:</label>
                        </div>
                        <div class="col-sm-8">
                            <?php echo htmlspecialchars($transfer['bank_name']); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <hr>

                <!-- Timeline -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-2">
                            <strong>Created:</strong><br>
                            <?php echo date('M d, Y \a\t g:i A', strtotime($transfer['created_at'])); ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2">
                            <strong>Last Updated:</strong><br>
                            <?php
                            $updated_at = $transfer['updated_at'] ?? $transfer['created_at'];
                            echo date('M d, Y \a\t g:i A', strtotime($updated_at));
                            ?>
                            <?php if (($transfer['updated_at'] ?? $transfer['created_at']) !== $transfer['created_at']): ?>
                                <small class="badge bg-warning ms-1">Modified</small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Dynamic Wire Transfer Fields - All User Submitted Data -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-database me-2"></i>
                    Complete Wire Transfer Data
                </h3>
            </div>
            <div class="card-body">
                <?php
                // Get all configured wire transfer fields
                $fields_sql = "SELECT * FROM wire_transfer_fields WHERE is_active = 1 ORDER BY field_group, display_order";
                $fields_result = $db->query($fields_sql);
                $wire_fields = [];
                while ($field = $fields_result->fetch_assoc()) {
                    $wire_fields[$field['field_group']][] = $field;
                }

                // Parse wire transfer data JSON
                $wire_data = [];
                if ($transfer['wire_transfer_data']) {
                    $wire_data = json_decode($transfer['wire_transfer_data'], true) ?: [];
                }

                // Add standard transfer fields to wire data for display
                $standard_fields = [
                    'beneficiary_account_number' => $transfer['recipient_account'],
                    'beneficiary_account_name' => $transfer['recipient_name'],
                    'bank_name' => $transfer['bank_name'],
                    'swift_code' => $transfer['swift_code'],
                    'routing_code' => $transfer['routing_code'],
                    'iban' => $transfer['iban'],
                    'bank_address' => $transfer['bank_address'],
                    'bank_city' => $transfer['bank_city'],
                    'bank_country' => $transfer['bank_country'],
                    'beneficiary_address' => $transfer['beneficiary_address'],
                    'purpose_of_payment' => $transfer['purpose_of_payment']
                ];

                foreach ($standard_fields as $key => $value) {
                    if ($value) {
                        $wire_data[$key] = $value;
                    }
                }

                $displayed_fields = [];

                // Display configured fields by group
                if (!empty($wire_fields)):
                    foreach ($wire_fields as $group => $group_fields):
                        if (!empty($group_fields)):
                ?>
                <div class="mb-4">
                    <h5 class="text-primary border-bottom pb-2 mb-3">
                        <i class="fas fa-folder me-2"></i>
                        <?php echo ucwords(str_replace('_', ' ', $group)); ?> Information
                    </h5>
                    <div class="row">
                        <?php foreach ($group_fields as $field): ?>
                            <?php
                            $field_value = $wire_data[$field['field_name']] ?? '';
                            if ($field_value):
                                $displayed_fields[] = $field['field_name'];
                            ?>
                            <div class="col-md-6 mb-3">
                                <div class="row">
                                    <div class="col-sm-5">
                                        <label class="form-label mb-0 fw-bold">
                                            <?php echo htmlspecialchars($field['field_label']); ?>:
                                            <?php if ($field['is_required']): ?>
                                                <span class="text-danger">*</span>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                    <div class="col-sm-7">
                                        <?php if ($field['field_type'] === 'email'): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($field_value); ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($field_value); ?>
                                            </a>
                                        <?php elseif ($field['field_type'] === 'url'): ?>
                                            <a href="<?php echo htmlspecialchars($field_value); ?>" target="_blank" class="text-decoration-none">
                                                <?php echo htmlspecialchars($field_value); ?>
                                            </a>
                                        <?php elseif ($field['field_type'] === 'tel'): ?>
                                            <a href="tel:<?php echo htmlspecialchars($field_value); ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($field_value); ?>
                                            </a>
                                        <?php elseif (in_array($field['field_name'], ['swift_code', 'routing_code', 'iban', 'beneficiary_account_number'])): ?>
                                            <code class="small"><?php echo htmlspecialchars($field_value); ?></code>
                                        <?php else: ?>
                                            <?php echo nl2br(htmlspecialchars($field_value)); ?>
                                        <?php endif; ?>

                                        <?php if ($field['help_text']): ?>
                                            <small class="form-text text-muted d-block">
                                                <?php echo htmlspecialchars($field['help_text']); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php
                        endif;
                    endforeach;
                endif;

                // Display any additional fields not in configuration
                $additional_fields = [];
                foreach ($wire_data as $key => $value) {
                    if (!in_array($key, $displayed_fields) &&
                        $value !== null &&
                        $value !== '' &&
                        !in_array($key, ['id', 'created_at', 'updated_at', 'transfer_id'])) {
                        $additional_fields[$key] = $value;
                    }
                }

                if (!empty($additional_fields)):
                ?>
                <div class="mb-4">
                    <h5 class="text-warning border-bottom pb-2 mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Additional Fields (From Transaction Data)
                    </h5>
                    <div class="row">
                        <?php foreach ($additional_fields as $field_name => $field_value): ?>
                        <div class="col-md-6 mb-3">
                            <div class="row">
                                <div class="col-sm-5">
                                    <label class="form-label mb-0 fw-bold">
                                        <?php echo ucwords(str_replace('_', ' ', $field_name)); ?>:
                                    </label>
                                </div>
                                <div class="col-sm-7">
                                    <?php echo nl2br(htmlspecialchars($field_value)); ?>
                                    <small class="form-text text-muted d-block">
                                        This field was submitted by the user but is not in the current field configuration
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (empty($wire_fields) && empty($additional_fields)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Additional Wire Transfer Data</h5>
                    <p class="text-muted">No custom fields or additional data found for this wire transfer.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($transfer['description']): ?>
<!-- Description Card -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title mb-0">
                    <i class="fas fa-comment-alt me-2"></i>
                    Description
                </h3>
            </div>
            <div class="card-body">
                <p class="mb-0"><?php echo nl2br(htmlspecialchars($transfer['description'])); ?></p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Action Buttons -->
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex gap-2">
            <a href="../wire-transfers.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Wire Transfers
            </a>
            <a href="edit.php?id=<?php echo $transfer['id']; ?>" class="btn" style="background: var(--primary-color); color: white;">
                <i class="fas fa-edit me-2"></i>
                Edit Transfer
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print me-2"></i>
                Print Details
            </button>
        </div>
    </div>
</div>

<?php include '../includes/admin-footer.php'; ?>
