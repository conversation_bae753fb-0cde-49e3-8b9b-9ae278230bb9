<?php
/**
 * Check Current Theme Color in Database
 */

require_once __DIR__ . '/config/database.php';

try {
    $db = getDB();
    
    // Get current theme color
    $result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key = 'theme_color'");
    
    if ($result && $row = $result->fetch_assoc()) {
        echo "Current theme_color: " . $row['setting_value'] . "\n";
    } else {
        echo "No theme_color found in database\n";
    }
    
    // Get all appearance-related settings
    echo "\nAll appearance settings:\n";
    $all_settings = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key LIKE '%color%' OR setting_key LIKE '%theme%'");
    
    while ($setting = $all_settings->fetch_assoc()) {
        echo $setting['setting_key'] . ": " . $setting['setting_value'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>