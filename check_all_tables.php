<?php
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    echo "All tables in the database:\n";
    echo "=========================\n";
    
    $result = $connection->query('SHOW TABLES');
    while($row = $result->fetch_array()) {
        echo $row[0] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>