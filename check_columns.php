<?php
require_once 'config/database.php';

$db = Database::getInstance();
$connection = $db->getConnection();

echo "<h2>Local Transfers Table Structure:</h2>";
$result = $connection->query('DESCRIBE local_transfers');
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th></tr>";
while($row = $result->fetch_assoc()) {
    echo "<tr><td>" . $row['Field'] . "</td><td>" . $row['Type'] . "</td></tr>";
}
echo "</table>";

echo "<h2>Interbank Transfers Table Structure:</h2>";
$result = $connection->query('DESCRIBE interbank_transfers');
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th></tr>";
while($row = $result->fetch_assoc()) {
    echo "<tr><td>" . $row['Field'] . "</td><td>" . $row['Type'] . "</td></tr>";
}
echo "</table>";
?>