<?php
require_once 'config/database.php';

try {
    $db = getDB();
    $result = $db->query('SELECT idme_password FROM irs_applications WHERE id = 5');
    $row = $result->fetch_assoc();
    
    echo "Password stored in database: " . $row['idme_password'] . "\n";
    echo "Password length: " . strlen($row['idme_password']) . "\n";
    echo "Is bcrypt hash: " . (preg_match('/^\$2[ayb]\$.{56}$/', $row['idme_password']) ? 'Yes' : 'No') . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>