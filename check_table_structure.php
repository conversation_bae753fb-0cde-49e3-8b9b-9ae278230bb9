<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "📋 Wire Transfer Fields Table Structure:\n";
    echo "=======================================\n";
    
    $result = $db->query('DESCRIBE wire_transfer_fields');
    while($row = $result->fetch_assoc()) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
    
    echo "\n📋 Billing Code Settings Table Structure:\n";
    echo "=========================================\n";
    
    $result2 = $db->query('DESCRIBE billing_code_settings');
    while($row = $result2->fetch_assoc()) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>