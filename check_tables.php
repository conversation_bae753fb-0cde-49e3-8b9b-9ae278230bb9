<?php
require_once 'config/config.php';

try {
    $db = getDB();
    $result = $db->query('SHOW TABLES');
    
    echo "<h2>Database Tables:</h2>";
    echo "<ul>";
    while($row = $result->fetch_array()) {
        echo "<li>" . $row[0] . "</li>";
    }
    echo "</ul>";
    
    // Check if local_transfers table exists
    $check_local = $db->query("SHOW TABLES LIKE 'local_transfers'");
    if ($check_local->num_rows > 0) {
        echo "<h3>local_transfers table structure:</h3>";
        $desc = $db->query("DESCRIBE local_transfers");
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while($row = $desc->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>local_transfers table does not exist</p>";
    }
    
    // Check if interbank_transfers table exists
    $check_interbank = $db->query("SHOW TABLES LIKE 'interbank_transfers'");
    if ($check_interbank->num_rows > 0) {
        echo "<h3>interbank_transfers table structure:</h3>";
        $desc = $db->query("DESCRIBE interbank_transfers");
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while($row = $desc->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>interbank_transfers table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>