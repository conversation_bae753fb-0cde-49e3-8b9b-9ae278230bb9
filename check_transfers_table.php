<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    // Check the structure of transfers table
    echo "📋 Transfers table structure:\n";
    $columns = $db->query("SHOW COLUMNS FROM transfers");
    while ($col = $columns->fetch_assoc()) {
        echo "  - " . $col['Field'] . " (" . $col['Type'] . ") " . ($col['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . "\n";
    }
    
    // Check for any transfers
    echo "\n🔄 Checking transfers:\n";
    $transfers = $db->query("SELECT COUNT(*) as count FROM transfers");
    $count = $transfers->fetch_assoc();
    echo "Total transfers: " . $count['count'] . "\n";
    
    if ($count['count'] > 0) {
        // Show sample transfers
        $sample = $db->query("SELECT id, sender_id, recipient_id, amount, status, created_at FROM transfers LIMIT 3");
        echo "\n📋 Sample transfers:\n";
        while ($transfer = $sample->fetch_assoc()) {
            echo "  ID: " . $transfer['id'] . " | Sender: " . $transfer['sender_id'] . " | Recipient: " . $transfer['recipient_id'] . " | Amount: $" . $transfer['amount'] . " | Status: " . $transfer['status'] . " | Date: " . $transfer['created_at'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>