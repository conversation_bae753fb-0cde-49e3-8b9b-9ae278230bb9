<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    // Check for international wire transfers
    $wire_transfers = $db->query("SELECT COUNT(*) as count FROM transfers WHERE transfer_type = 'international'");
    $count = $wire_transfers->fetch_assoc();
    echo "🔄 International wire transfers in database: " . $count['count'] . "\n";
    
    if ($count['count'] > 0) {
        // Show sample wire transfers
        $sample = $db->query("SELECT id, amount, status, processing_status, bank_name, swift_code, created_at FROM transfers WHERE transfer_type = 'international' LIMIT 3");
        echo "\n📋 Sample wire transfers:\n";
        while ($transfer = $sample->fetch_assoc()) {
            echo "  ID: " . $transfer['id'] . " | Amount: $" . $transfer['amount'] . " | Status: " . $transfer['status'] . " | Processing: " . $transfer['processing_status'] . " | Bank: " . $transfer['bank_name'] . " | Date: " . $transfer['created_at'] . "\n";
        }
    } else {
        echo "❌ No international wire transfers found. Let's create a sample one.\n";
        
        // Create a sample wire transfer
        $stmt = $db->prepare("INSERT INTO transfers (transaction_id, sender_id, recipient_id, recipient_account, recipient_name, amount, currency, transfer_type, status, processing_status, swift_code, routing_code, bank_name, bank_address, bank_city, bank_country, beneficiary_address, purpose_of_payment, wire_transfer_data, created_at) VALUES (?, ?, ?, ?, ?, ?, 'USD', 'international', 'pending', 'processing', ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        
        $transaction_id = 'WT' . time();
        $sender_id = 2; // Sample user
        $recipient_id = 1; // Admin as recipient for testing
        $recipient_account = '**********';
        $recipient_name = 'John Doe';
        $amount = 1500.00;
        $swift_code = 'CHASUS33';
        $routing_code = '*********';
        $bank_name = 'Chase Bank';
        $bank_address = '456 Bank St';
        $bank_city = 'New York';
        $bank_country = 'United States';
        $beneficiary_address = '123 Main St, New York, NY 10001';
        $purpose = 'Business payment';
        
        $wire_data = json_encode([
            'beneficiary_account_number' => $recipient_account,
            'beneficiary_account_name' => $recipient_name,
            'beneficiary_address' => $beneficiary_address,
            'bank_name' => $bank_name,
            'bank_address' => $bank_address,
            'swift_code' => $swift_code,
            'routing_number' => $routing_code,
            'purpose_of_transfer' => $purpose,
            'reference_number' => 'REF' . time(),
            'correspondent_bank' => 'JP Morgan Chase',
            'intermediary_bank' => 'Wells Fargo',
            'special_instructions' => 'Urgent transfer for business operations'
        ]);
        
        $stmt->bind_param("siissssssssss", $transaction_id, $sender_id, $recipient_id, $recipient_account, $recipient_name, $amount, $swift_code, $routing_code, $bank_name, $bank_address, $bank_city, $bank_country, $beneficiary_address, $purpose, $wire_data);
        
        if ($stmt->execute()) {
            $new_id = $db->insert_id;
            echo "✅ Created sample wire transfer with ID: " . $new_id . "\n";
        } else {
            echo "❌ Failed to create sample wire transfer: " . $stmt->error . "\n";
        }
    }
    
    // Check users table for sample data
    echo "\n👥 Checking users:\n";
    $users = $db->query("SELECT id, username, email FROM users LIMIT 3");
    while ($user = $users->fetch_assoc()) {
        echo "  ID: " . $user['id'] . " | Username: " . $user['username'] . " | Email: " . $user['email'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>