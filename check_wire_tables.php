<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    // Check wire_transfer_fields table
    $result = $db->query("SHOW TABLES LIKE 'wire_transfer_fields'");
    if ($result->num_rows > 0) {
        echo "✅ wire_transfer_fields table exists\n";
        
        $fields = $db->query("SELECT COUNT(*) as count FROM wire_transfer_fields");
        $count = $fields->fetch_assoc();
        echo "📊 Number of configured fields: " . $count['count'] . "\n";
        
        $active_fields = $db->query("SELECT COUNT(*) as count FROM wire_transfer_fields WHERE is_active = 1");
        $active_count = $active_fields->fetch_assoc();
        echo "✅ Active fields: " . $active_count['count'] . "\n";
        
        // Show some sample fields
        $sample_fields = $db->query("SELECT field_name, field_label, field_group FROM wire_transfer_fields WHERE is_active = 1 LIMIT 5");
        echo "\n📋 Sample fields:\n";
        while ($field = $sample_fields->fetch_assoc()) {
            echo "  - " . $field['field_name'] . " (" . $field['field_group'] . "): " . $field['field_label'] . "\n";
        }
    } else {
        echo "❌ wire_transfer_fields table does not exist\n";
    }
    
    // Check billing code tables
    echo "\n🔐 Checking billing code tables:\n";
    
    $billing_settings = $db->query("SHOW TABLES LIKE 'billing_code_settings'");
    if ($billing_settings->num_rows > 0) {
        echo "✅ billing_code_settings table exists\n";
    } else {
        echo "❌ billing_code_settings table does not exist\n";
    }
    
    $user_billing = $db->query("SHOW TABLES LIKE 'user_billing_codes'");
    if ($user_billing->num_rows > 0) {
        echo "✅ user_billing_codes table exists\n";
    } else {
        echo "❌ user_billing_codes table does not exist\n";
    }
    
    // Check transfers table for wire transfer columns
    echo "\n🔄 Checking transfers table for wire transfer columns:\n";
    $columns = $db->query("SHOW COLUMNS FROM transfers LIKE '%wire%'");
    if ($columns->num_rows > 0) {
        echo "✅ Wire transfer columns found in transfers table:\n";
        while ($col = $columns->fetch_assoc()) {
            echo "  - " . $col['Field'] . " (" . $col['Type'] . ")\n";
        }
    } else {
        echo "❌ No wire transfer columns found in transfers table\n";
    }
    
    // Check for processing_status column
    $status_col = $db->query("SHOW COLUMNS FROM transfers LIKE 'processing_status'");
    if ($status_col->num_rows > 0) {
        echo "✅ processing_status column exists\n";
    } else {
        echo "❌ processing_status column does not exist\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>