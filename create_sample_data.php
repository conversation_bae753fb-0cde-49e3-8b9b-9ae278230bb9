<?php
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    // Get the first user ID from accounts table
    $result = $connection->query("SELECT id FROM accounts LIMIT 1");
    $user = $result->fetch_assoc();
    $user_id = $user['id'];
    
    echo "Creating sample transfer data for user ID: $user_id\n";
    
    // Create a sample local transfer
    $stmt = $connection->prepare("
        INSERT INTO local_transfers 
        (sender_id, beneficiary_account_name, beneficiary_account_number, beneficiary_bank_name, amount, narration, transaction_id, status, created_at) 
        VALUES (?, '<PERSON>', '**********', 'Test Bank', 100.00, 'Test local transfer', 'TXN001', 'completed', NOW())
    ");
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $local_transfer_id = $connection->insert_id;
    echo "✓ Created local transfer with ID: $local_transfer_id\n";
    
    // Get a second user for interbank transfer (if exists)
    $result = $connection->query("SELECT id FROM accounts WHERE id != $user_id LIMIT 1");
    $recipient = $result->fetch_assoc();
    
    if ($recipient) {
        $recipient_id = $recipient['id'];
        
        // Create a sample interbank transfer
        $stmt = $connection->prepare("
            INSERT INTO interbank_transfers 
            (sender_id, recipient_id, amount, narration, transaction_id, status, created_at) 
            VALUES (?, ?, 250.00, 'Test interbank transfer', 'TXN002', 'completed', NOW())
        ");
        $stmt->bind_param('ii', $user_id, $recipient_id);
        $stmt->execute();
        $interbank_transfer_id = $connection->insert_id;
        echo "✓ Created interbank transfer with ID: $interbank_transfer_id\n";
    } else {
        echo "⚠ No second user found for interbank transfer\n";
    }
    
    echo "\n✅ Sample data created successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>