-- Add new banking fields to irs_applications table
-- This script adds support for existing vs external bank account selection

ALTER TABLE irs_applications 
ADD COLUMN banking_option ENUM('existing', 'external') DEFAULT 'existing' AFTER routing_number,
ADD COLUMN bank_name VARCHAR(100) NULL AFTER banking_option,
ADD COLUMN account_holder_name VARCHAR(100) NULL AFTER bank_name;

-- Update existing records to have default banking_option
UPDATE irs_applications SET banking_option = 'existing' WHERE banking_option IS NULL;