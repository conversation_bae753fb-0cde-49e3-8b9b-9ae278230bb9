-- Fix Member Since Date Issue
-- Change accounts.created_at from TIMESTAMP to DATETIME to support dates before 1970
-- This allows admins to set member since dates like 1967

USE online_banking;

-- Backup the current data first (optional but recommended)
-- CREATE TABLE accounts_backup AS SELECT * FROM accounts;

-- Change created_at column from TIMESTAMP to DATETIME
ALTER TABLE accounts 
MODIFY COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP;

-- Change updated_at column from TIMESTAMP to DATETIME for consistency
ALTER TABLE accounts 
MODIFY COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Change last_login column from TIMESTAMP to DATETIME for consistency
ALTER TABLE accounts 
MODIFY COLUMN last_login DATETIME NULL;

-- Verify the changes
DESCRIBE accounts;
