<?php
/**
 * Database Migration Script
 * Fix Member Since Date Issue by changing TIMESTAMP to DATETIME
 */

require_once __DIR__ . '/../config/config.php';

try {
    $db = getDB();
    
    echo "Starting database migration to fix member since date issue...\n";
    
    // Change created_at column from TIMESTAMP to DATETIME
    echo "Changing created_at column to DATETIME...\n";
    $db->query("ALTER TABLE accounts MODIFY COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP");
    
    // Change updated_at column from TIMESTAMP to DATETIME for consistency
    echo "Changing updated_at column to DATETIME...\n";
    $db->query("ALTER TABLE accounts MODIFY COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
    
    // Change last_login column from TIMESTAMP to DATETIME for consistency
    echo "Changing last_login column to DATETIME...\n";
    $db->query("ALTER TABLE accounts MODIFY COLUMN last_login DATETIME NULL");
    
    echo "Migration completed successfully!\n";
    echo "You can now set member since dates before 1970 (like 1967).\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
}
?>
