# Transfer System Implementation Guide

## Quick Reference

This guide provides step-by-step implementation details for developers working with the transfer system.

## Database Schema Setup

### Required Tables

#### 1. Local Transfers Table
```sql
CREATE TABLE IF NOT EXISTS local_transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    sender_id INT NOT NULL,
    sender_account_type VARCHAR(20) DEFAULT 'main',
    beneficiary_account_number VARCHAR(20) NOT NULL,
    beneficiary_account_name VARCHAR(100) NOT NULL,
    beneficiary_bank_name VARCHAR(100) NOT NULL,
    routing_code VARCHAR(20),
    account_type VARCHAR(20),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    transfer_fee DECIMAL(10,2) DEFAULT 0.00,
    narration TEXT,
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
    otp_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (sender_id) REFERENCES accounts(id) ON DELETE CASCADE,
    INDEX idx_sender_id (sender_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

#### 2. Inter-Bank Transfers Table
```sql
CREATE TABLE IF NOT EXISTS interbank_transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    sender_account_type VARCHAR(20) DEFAULT 'main',
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    transfer_fee DECIMAL(10,2) DEFAULT 0.00,
    narration TEXT,
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (sender_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES accounts(id) ON DELETE CASCADE,
    INDEX idx_sender_id (sender_id),
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

#### 3. Wire Transfer Enhancements (Existing transfers table)
```sql
-- Add wire transfer specific columns to existing transfers table
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS swift_code VARCHAR(20) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS routing_code VARCHAR(50) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS iban VARCHAR(50) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_name VARCHAR(150) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_address TEXT DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_city VARCHAR(100) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS bank_country VARCHAR(50) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS beneficiary_address TEXT DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS purpose_of_payment VARCHAR(200) DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS wire_transfer_data JSON DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS billing_codes_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS billing_verification_data JSON DEFAULT NULL;
ALTER TABLE transfers ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NULL DEFAULT NULL;
```

## PHP Implementation

### 1. Local Transfer Processing

**File**: `user/transfers/process-local-transfer-simple.php`

```php
<?php
// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Please login first']);
    exit();
}

// Include database connection
require_once '../../config/config.php';

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get form data
    $source_account = $_POST['source_account'] ?? 'main';
    $beneficiary_account = $_POST['beneficiary_account'] ?? '';
    $beneficiary_name = $_POST['beneficiary_name'] ?? '';
    $beneficiary_bank = $_POST['beneficiary_bank'] ?? '';
    $routing_code = $_POST['routing_code'] ?? '';
    $account_type = $_POST['account_type'] ?? '';
    $amount = floatval($_POST['amount'] ?? 0);
    $narration = $_POST['narration'] ?? 'Local Bank Transfer';
    $otp_code = $_POST['otp_code'] ?? '';
    
    // Validation
    if ($amount <= 0) {
        throw new Exception('Invalid transfer amount');
    }
    
    if (empty($beneficiary_account) || empty($beneficiary_name) || empty($beneficiary_bank)) {
        throw new Exception('Please fill all required fields');
    }
    
    // Get user data
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Check OTP requirement
    $otp_query = "SELECT COALESCE(uss.otp_enabled, 1) as otp_enabled 
                  FROM accounts a 
                  LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                  WHERE a.id = ?";
    $otp_result = $db->query($otp_query, [$user_id]);
    $otp_setting = $otp_result->fetch_assoc();
    $otp_required = $otp_setting ? $otp_setting['otp_enabled'] : 1;
    
    // Verify OTP if required
    if ($otp_required && !empty($otp_code)) {
        $otp_verify_query = "SELECT * FROM user_otps 
                            WHERE user_id = ? AND otp_code = ? 
                            AND is_used = 0 AND expires_at > NOW()";
        $otp_verify_result = $db->query($otp_verify_query, [$user_id, $otp_code]);
        
        if ($otp_verify_result->num_rows === 0) {
            throw new Exception('Invalid or expired OTP');
        }
    }
    
    // Check balance
    $available_balance = floatval($user['balance']);
    $transfer_fee = 0; // No fees for local transfers
    $total_debit = $amount + $transfer_fee;
    
    if ($total_debit > $available_balance) {
        throw new Exception('Insufficient balance for this transfer');
    }
    
    // Start database transaction
    $db->begin_transaction();
    
    try {
        // Generate unique transfer reference
        $transfer_reference = 'LBT' . date('Ymd') . sprintf('%06d', mt_rand(100000, 999999));
        
        // Insert transfer record
        $insert_transfer_sql = "INSERT INTO local_transfers (
            transaction_id, sender_id, sender_account_type,
            beneficiary_account_number, beneficiary_account_name, beneficiary_bank_name,
            routing_code, account_type, amount, currency, transfer_fee,
            narration, status, otp_verified
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $otp_was_verified = $otp_required && !empty($otp_code) ? 1 : 0;
        
        $transfer_id = $db->insert($insert_transfer_sql, [
            $transfer_reference, $user_id, $source_account,
            $beneficiary_account, $beneficiary_name, $beneficiary_bank,
            $routing_code ?: null, $account_type ?: null, $amount, $user['currency'],
            $transfer_fee, $narration ?: null, 'completed', $otp_was_verified
        ]);
        
        // Update account balance
        $update_balance_sql = "UPDATE accounts SET balance = balance - ? WHERE id = ?";
        $db->query($update_balance_sql, [$total_debit, $user_id]);
        
        // Log transaction
        $transaction_description = "Local bank transfer to {$beneficiary_name} at {$beneficiary_bank}";
        $debit_sql = "INSERT INTO transactions (user_id, transaction_type, amount, currency, description, reference_number, category, status, created_at)
                      VALUES (?, 'debit', ?, ?, ?, ?, 'transfer', 'completed', NOW())";
        $db->query($debit_sql, [$user_id, $amount, $user['currency'], $transaction_description, $transfer_reference]);
        
        // Mark OTP as used if it was provided
        if ($otp_required && !empty($otp_code)) {
            $mark_otp_used_sql = "UPDATE user_otps SET is_used = 1, used = 1, used_at = NOW() WHERE user_id = ? AND otp_code = ?";
            $db->query($mark_otp_used_sql, [$user_id, $otp_code]);
        }
        
        // Commit transaction
        $db->commit();
        
        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Local bank transfer completed successfully',
            'transfer_id' => $transfer_id,
            'transaction_id' => $transfer_reference,
            'amount' => $amount,
            'currency' => $user['currency'],
            'fee' => $transfer_fee,
            'total_debit' => $total_debit,
            'recipient' => $beneficiary_name,
            'recipient_account' => $beneficiary_account,
            'bank_name' => $beneficiary_bank,
            'new_balance' => $available_balance - $total_debit,
            'receipt_email_sent' => false
        ]);
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
```

### 2. Inter-Bank Transfer Processing

**File**: `user/transfers/process-interbank-transfer-v2.php`

```php
<?php
// Similar structure to local transfer but with recipient validation
// Key differences:
// - Uses interbank_transfers table
// - Validates recipient exists in accounts table
// - Updates both sender and recipient balances
// - No OTP requirement
// - Creates credit transaction for recipient

// Example recipient validation:
$recipient_query = "SELECT * FROM accounts WHERE account_number = ? AND is_admin = 0";
$recipient_result = $db->query($recipient_query, [$beneficiary_account]);
$recipient = $recipient_result->fetch_assoc();

if (!$recipient) {
    throw new Exception('Recipient account not found');
}

// Credit recipient account
$credit_recipient_sql = "UPDATE accounts SET balance = balance + ? WHERE id = ?";
$db->query($credit_recipient_sql, [$amount, $recipient['id']]);

// Log credit transaction for recipient
$credit_sql = "INSERT INTO transactions (user_id, transaction_type, amount, currency, description, reference_number, category, status, created_at)
               VALUES (?, 'credit', ?, ?, ?, ?, 'transfer', 'completed', NOW())";
$db->query($credit_sql, [$recipient['id'], $amount, $user['currency'], $transaction_description, $transfer_reference]);
?>
```

## JavaScript Implementation

### 1. Main Transfer JavaScript

**File**: `user/transfers/transfers.js`

```javascript
// Global variables
let currentTransferType = null;
let transferData = {};
let otpModal = null;
let successModal = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeTransferPage();
});

function initializeTransferPage() {
    console.log('💰 Transfer page loaded');
    
    // Initialize Bootstrap components
    initializeBootstrapComponents();
    
    // Set default transfer type
    currentTransferType = 'local-bank';
    
    // Setup event listeners
    setupEventListeners();
    
    // Initialize form validation
    setupFormValidation();
}

function initializeBootstrapComponents() {
    if (typeof bootstrap !== 'undefined') {
        otpModal = new bootstrap.Modal(document.getElementById('otpModal'));
        successModal = new bootstrap.Modal(document.getElementById('successModal'));
    }
}

function setupEventListeners() {
    // Transfer type switching
    document.querySelectorAll('.transfer-type-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            switchTransferType(this.dataset.type);
        });
    });
    
    // Form submission
    document.getElementById('transferForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleTransferSubmission();
    });
    
    // OTP verification
    document.getElementById('verifyOtpBtn').addEventListener('click', verifyOTPAndProcess);
}

function switchTransferType(type) {
    currentTransferType = type;
    
    // Update UI based on transfer type
    updateTransferTypeUI(type);
    
    // Reset form
    resetTransferForm();
}

function handleTransferSubmission() {
    if (!validateTransferForm()) {
        return;
    }
    
    // Collect transfer data
    collectTransferData();
    
    // Check if OTP is required
    if (currentTransferType === 'local-bank' && window.userOTPEnabled) {
        showOTPModal();
    } else {
        processTransfer();
    }
}

function processTransfer() {
    const processingUrl = currentTransferType === 'local-bank' 
        ? 'process-local-transfer-simple.php'
        : 'process-interbank-transfer-v2.php';
    
    // Show loading state
    showTransferProgress(true);
    
    // Prepare form data
    const formData = new FormData();
    Object.keys(transferData).forEach(key => {
        formData.append(key, transferData[key]);
    });
    
    fetch(processingUrl, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(text => {
        console.log('🔍 Raw response:', text);
        
        if (!text.trim()) {
            throw new Error('Empty response from server');
        }
        
        try {
            return JSON.parse(text);
        } catch (e) {
            console.error('Invalid JSON response:', text);
            throw new Error('Invalid response format from server');
        }
    })
    .then(data => {
        if (data.success) {
            showTransferSuccess(data);
        } else {
            throw new Error(data.error || 'Transfer failed');
        }
    })
    .catch(error => {
        console.error('Transfer failed:', error);
        showErrorMessage(error.message);
    })
    .finally(() => {
        showTransferProgress(false);
    });
}

function showTransferSuccess(data) {
    console.log('🎉 Transfer successful:', data);
    
    // Update success message
    const message = `Your ${currentTransferType.replace('-', ' ')} transfer of ${transferData.currency} ${transferData.amount.toFixed(2)} to ${transferData.beneficiary_name} has been processed successfully.`;
    document.getElementById('successMessage').textContent = message;
    
    // Store receipt data
    window.transferReceiptData = data;
    
    // Show success modal
    successModal.show();
    
    // Reset form after success
    setTimeout(() => {
        resetTransferForm();
    }, 1000);
}
```

## Modal Implementation

### Success Modal HTML Structure

```html
<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="successModalLabel">
                    <i class="fas fa-check-circle me-2"></i>Transfer Successful
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                </div>
                <div class="alert alert-success">
                    <p id="successMessage" class="mb-0"></p>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Transfer Details</h6>
                        <div id="transferDetails"></div>
                    </div>
                    <div class="col-md-6">
                        <h6>Receipt Options</h6>
                        <div id="receiptOptions">
                            <p id="receiptEmailStatus" class="text-muted mb-2"></p>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="downloadReceipt()">
                                <i class="fas fa-download me-1"></i>Download PDF Receipt
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="closeSuccessModal()">Continue</button>
            </div>
        </div>
    </div>
</div>
```

## Testing Implementation

### Test File Example

```php
<?php
// test-transfer-system.php
require_once 'config/config.php';

echo "<h1>Transfer System Test</h1>";

// Test database connections
try {
    $db = getDB();
    echo "<p>✅ Database connection successful</p>";
    
    // Test table existence
    $tables = ['local_transfers', 'interbank_transfers', 'transfers', 'accounts'];
    foreach ($tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "<p>✅ Table '$table' exists</p>";
        } else {
            echo "<p>❌ Table '$table' missing</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test transfer processing endpoints
$endpoints = [
    'user/transfers/process-local-transfer-simple.php',
    'user/transfers/process-interbank-transfer-v2.php',
    'user/wire-transfers/process-wire-transfer.php'
];

foreach ($endpoints as $endpoint) {
    if (file_exists($endpoint)) {
        echo "<p>✅ Endpoint '$endpoint' exists</p>";
    } else {
        echo "<p>❌ Endpoint '$endpoint' missing</p>";
    }
}
?>
```

This implementation guide provides the essential code structure and examples needed to understand and maintain the transfer system.
