# Transfer System Documentation

## Overview

The online banking system supports three main types of money transfers:

1. **Local Bank Transfers** - Transfers to external bank accounts within the same country
2. **Inter-Bank Transfers** - Transfers between accounts within the same banking system
3. **Wire Transfers** - International wire transfers with enhanced security

Each transfer type has its own dedicated processing system, database tables, and user interface flows.

## System Architecture

### Database Structure

#### Main Tables

1. **`transfers`** - Main transfers table for wire transfers and legacy transfers
   - Used primarily for wire transfers
   - Contains wire-specific fields like SWIFT codes, IBAN, bank details
   - Enhanced with JSON fields for complex wire transfer data

2. **`local_transfers`** - Dedicated table for local bank transfers
   - Stores external bank transfer details
   - Includes beneficiary bank information
   - Supports OTP verification tracking

3. **`interbank_transfers`** - Dedicated table for inter-bank transfers
   - Handles transfers between internal accounts
   - No OTP requirement (internal transfers)
   - Simplified processing flow

4. **`transactions`** - General transaction logging
   - Records all debit/credit transactions
   - Used for account balance updates
   - Provides audit trail

### File Structure

```
user/transfers/
├── index.php                           # Main transfer page
├── transfers.js                        # Main transfer JavaScript
├── transfers.css                       # Transfer styling
├── interbank-transfers.js              # Inter-bank specific logic
├── process-local-transfer-simple.php   # Local transfer processor
├── process-interbank-transfer-v2.php   # Inter-bank processor
└── generate-transfer-otp.php           # OTP generation

user/wire-transfers/
├── index.php                           # Wire transfer page
├── wire-transfers.js                   # Wire transfer JavaScript
├── wire-transfers.css                  # Wire transfer styling
├── process-wire-transfer.php           # Wire transfer processor
├── verify-billing-code.php             # Billing code verification
└── generate-wire-otp.php               # Wire OTP generation
```

## Transfer Types

### 1. Local Bank Transfers

**Purpose**: Send money to external bank accounts within the same country

**Key Features**:
- OTP verification (if enabled for user)
- External bank account validation
- Routing code support
- Account type specification (checking/savings)

**Database Table**: `local_transfers`

**Table Structure**:
```sql
CREATE TABLE local_transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    sender_id INT NOT NULL,
    sender_account_type VARCHAR(20),
    beneficiary_account_number VARCHAR(20) NOT NULL,
    beneficiary_account_name VARCHAR(100) NOT NULL,
    beneficiary_bank_name VARCHAR(100) NOT NULL,
    routing_code VARCHAR(20),
    account_type VARCHAR(20),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    transfer_fee DECIMAL(10,2) DEFAULT 0.00,
    narration TEXT,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
    otp_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Processing Flow**:
1. User fills transfer form
2. Form validation (account format, amount, etc.)
3. OTP generation and verification (if enabled)
4. Transfer processing via `process-local-transfer-simple.php`
5. Database insertion into `local_transfers` table
6. Account balance deduction
7. Transaction logging
8. Success modal display

**Reference Prefix**: `LBT` (Local Bank Transfer) + date + random number

### 2. Inter-Bank Transfers

**Purpose**: Transfer money between accounts within the same banking system

**Key Features**:
- No OTP requirement (internal transfers)
- Automatic recipient validation
- Instant processing
- No transfer fees

**Database Table**: `interbank_transfers`

**Table Structure**:
```sql
CREATE TABLE interbank_transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    sender_account_type VARCHAR(20),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    transfer_fee DECIMAL(10,2) DEFAULT 0.00,
    narration TEXT,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL
);
```

**Processing Flow**:
1. User selects recipient from internal accounts
2. Form validation
3. Direct processing (no OTP required)
4. Transfer processing via `process-interbank-transfer-v2.php`
5. Database insertion into `interbank_transfers` table
6. Balance updates for both sender and recipient
7. Transaction logging for both accounts
8. Success modal display

**Reference Prefix**: `IBT` (Inter-Bank Transfer) + date + random number

### 3. Wire Transfers

**Purpose**: International wire transfers with enhanced security

**Key Features**:
- Billing code verification system
- OTP verification
- SWIFT code and IBAN support
- Comprehensive bank details
- Enhanced security measures
- Progress bar animations
- Detailed receipt generation

**Database Table**: `transfers` (with wire-specific columns)

**Enhanced Table Structure**:
```sql
-- Base transfers table with wire transfer enhancements
ALTER TABLE transfers ADD COLUMN swift_code VARCHAR(20);
ALTER TABLE transfers ADD COLUMN routing_code VARCHAR(50);
ALTER TABLE transfers ADD COLUMN iban VARCHAR(50);
ALTER TABLE transfers ADD COLUMN bank_name VARCHAR(150);
ALTER TABLE transfers ADD COLUMN bank_address TEXT;
ALTER TABLE transfers ADD COLUMN bank_city VARCHAR(100);
ALTER TABLE transfers ADD COLUMN bank_country VARCHAR(50);
ALTER TABLE transfers ADD COLUMN beneficiary_address TEXT;
ALTER TABLE transfers ADD COLUMN purpose_of_payment VARCHAR(200);
ALTER TABLE transfers ADD COLUMN wire_transfer_data JSON;
ALTER TABLE transfers ADD COLUMN billing_codes_verified BOOLEAN DEFAULT FALSE;
```

**Processing Flow**:
1. User fills comprehensive wire transfer form
2. Initial security loading animation
3. Billing code verification (if user has billing codes)
   - Progressive verification of each billing code
   - Real-time progress indicators
4. OTP generation and verification
5. Final transfer processing with progress animation
6. Database insertion into `transfers` table
7. Account balance deduction
8. Enhanced success modal with receipt
9. Optional PDF receipt generation

**Reference Prefix**: Wire transfers use the main transfers table

## Modal and Success Flows

### Local Bank Transfer Success Modal

**File**: `user/transfers/transfers.js` - `showTransferSuccess()`

**Features**:
- Transfer confirmation message
- Amount and recipient details
- Receipt email status
- PDF receipt download option
- Continue button to close modal

**Modal Elements**:
- Success icon and message
- Transfer details summary
- Receipt download button
- Close/continue button

### Inter-Bank Transfer Success Modal

**File**: `user/transfers/interbank-transfers.js` - Similar to local transfers

**Features**:
- Instant confirmation (no pending status)
- Internal transfer confirmation
- Recipient account details
- Balance update confirmation

### Wire Transfer Success Modal

**File**: `user/wire-transfers/wire-transfers.js` - `showWireTransferSuccessModal()`

**Features**:
- Banking receipt-style design
- Comprehensive transfer details
- Wire-specific information (SWIFT, IBAN)
- Professional styling with primary color theme
- Print receipt option
- Enhanced visual design

**Modal Elements**:
- Large success checkmark
- "Transfer Submitted Successfully" message
- Detailed receipt format
- Bank and beneficiary information
- Print receipt button
- Continue button

## JavaScript Architecture

### Main Transfer JavaScript (`transfers.js`)

**Key Functions**:
- `initializeTransferPage()` - Page initialization
- `switchTransferType()` - Handle transfer type switching
- `validateTransferForm()` - Form validation
- `collectTransferData()` - Data collection
- `showOTPModal()` - OTP verification
- `processTransfer()` - Main transfer processing
- `showTransferSuccess()` - Success modal display

### Inter-Bank Transfer JavaScript (`interbank-transfers.js`)

**Key Functions**:
- `initializeInterbankTransfers()` - Module initialization
- `processInterbankTransfer()` - Inter-bank processing
- `validateInterbankAccount()` - Account validation
- `updateInterbankTransferSummary()` - Summary updates

### Wire Transfer JavaScript (`wire-transfers.js`)

**Key Functions**:
- `initializeWireTransfers()` - Page initialization
- `showInitialSecurityLoading()` - Initial loading animation
- `startBillingCodeVerification()` - Billing code flow
- `showBillingCodeVerificationProgress()` - Progress animations
- `processWireTransfer()` - Main wire processing
- `showWireTransferSuccessModal()` - Enhanced success modal

## Security Features

### OTP System

**Local Bank Transfers**:
- OTP required if enabled in user settings
- Generated via `generate-transfer-otp.php`
- Verified during transfer processing
- Marked as used after successful verification

**Inter-Bank Transfers**:
- No OTP required (internal transfers)
- Considered secure within the banking system

**Wire Transfers**:
- Always requires OTP
- Generated via `generate-wire-otp.php`
- Additional billing code verification

### Billing Code System (Wire Transfers)

**Purpose**: Enhanced security for high-value international transfers

**Features**:
- Multiple billing codes per user
- Progressive verification with animations
- Position-based code verification
- Verification logging and audit trail

**Database Table**: `billing_code_verifications`

## Error Handling

### Common Error Scenarios

1. **Insufficient Balance**
   - Checked before processing
   - Clear error message displayed
   - Transfer blocked

2. **Invalid Account Numbers**
   - Format validation
   - Real-time validation feedback
   - Prevents form submission

3. **OTP Verification Failures**
   - Clear error messages
   - Retry mechanism
   - OTP regeneration option

4. **Network/Server Errors**
   - Robust error handling
   - User-friendly error messages
   - Graceful degradation

### Error Display Methods

- Field-level validation errors
- Modal error messages
- Alert notifications
- Console logging for debugging

## Testing and Debugging

### Test Files

- `test-both-transfers.php` - Tests both local and inter-bank transfers
- `test-interbank-modal.php` - Inter-bank modal testing
- `final-transfer-test.php` - Comprehensive transfer testing

### Debug Features

- Console logging throughout JavaScript
- PHP error logging
- Database transaction logging
- Transfer reference tracking

## Future Enhancements

### Planned Improvements

1. **Real-time Transfer Status**
   - WebSocket integration
   - Live status updates
   - Push notifications

2. **Enhanced Receipt System**
   - Email receipt automation
   - SMS notifications
   - Digital receipt storage

3. **Advanced Security**
   - Biometric verification
   - Device fingerprinting
   - Risk-based authentication

4. **Mobile Optimization**
   - Progressive Web App features
   - Touch-optimized interfaces
   - Offline capability

## Maintenance Notes

### Regular Maintenance Tasks

1. **Database Cleanup**
   - Archive old transfer records
   - Clean up expired OTP codes
   - Optimize table indexes

2. **Security Updates**
   - Review billing code effectiveness
   - Update OTP generation algorithms
   - Monitor for suspicious patterns

3. **Performance Monitoring**
   - Transfer processing times
   - Database query optimization
   - JavaScript performance

### Troubleshooting Guide

**Common Issues**:
1. Modal not displaying - Check Bootstrap initialization
2. OTP not generating - Verify database connections
3. Transfer failing - Check account balances and validation
4. Success modal styling - Verify CSS variable definitions

## Detailed Flow Diagrams

### Local Bank Transfer Flow

```
User Input → Form Validation → OTP Check → OTP Modal → Transfer Processing → Success Modal
     ↓              ↓              ↓           ↓              ↓                ↓
  Fill Form    Validate Data   Check User   Generate &    Process via      Show Receipt
  (Amount,     (Account #,     OTP Setting  Verify OTP    PHP Processor    & Confirmation
  Account,     Amount, etc.)      ↓            ↓              ↓                ↓
  Bank Info)        ↓         If Enabled   OTP Required   Insert into      Reset Form
                 Pass/Fail       ↓            ↓         local_transfers    & Redirect
                    ↓         Show Modal   Verify Code       ↓
                 Continue         ↓            ↓         Update Balance
                              Enter OTP    Success/Fail      ↓
                                 ↓            ↓         Log Transaction
                              Submit      Continue/Retry      ↓
                                             ↓           Return Success
                                          Process           Data
```

### Inter-Bank Transfer Flow

```
User Input → Form Validation → Direct Processing → Success Modal
     ↓              ↓                 ↓                ↓
  Fill Form    Validate Data    Process via        Show Receipt
  (Amount,     (Account #,      PHP Processor      & Confirmation
  Recipient)   Amount, etc.)         ↓                ↓
                    ↓           Insert into         Reset Form
                 Pass/Fail    interbank_transfers
                    ↓               ↓
                 Continue      Update Balances
                                   ↓
                              Log Transactions
                                   ↓
                              Return Success
                                  Data
```

### Wire Transfer Flow

```
User Input → Form Validation → Security Loading → Billing Codes → OTP → Processing → Success
     ↓              ↓                ↓               ↓          ↓         ↓          ↓
  Fill Form    Validate Data    Show Progress    Check User   Generate  Process    Enhanced
  (Amount,     (SWIFT, IBAN,    Animation       Billing      & Verify  via PHP    Receipt
  Bank Info,   Amount, etc.)         ↓          Codes        OTP       Processor  Modal
  Purpose)          ↓           2-3 Second           ↓          ↓         ↓          ↓
                 Pass/Fail      Loading         If Available Enter OTP Insert     Show Wire
                    ↓              ↓               ↓          Code      into       Receipt
                 Continue      Continue        Verify Each     ↓      transfers   with Print
                                              Code with    Verify       ↓        Option
                                              Progress    Success/   Update        ↓
                                                 ↓        Fail      Balance    Continue
                                              Success       ↓          ↓       Button
                                                 ↓      Continue   Log Trans.
                                              Continue      ↓          ↓
                                                         Show OTP  Return
                                                         Modal    Success
```

## Code Examples

### Local Transfer Processing Example

```php
// process-local-transfer-simple.php
$transfer_reference = 'LBT' . date('Ymd') . sprintf('%06d', mt_rand(100000, 999999));

$insert_transfer_sql = "INSERT INTO local_transfers (
    transaction_id, sender_id, sender_account_type,
    beneficiary_account_number, beneficiary_account_name, beneficiary_bank_name,
    routing_code, account_type, amount, currency, transfer_fee,
    narration, status, otp_verified
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$transfer_id = $db->insert($insert_transfer_sql, [
    $transfer_reference, $user_id, $source_account,
    $beneficiary_account, $beneficiary_name, $beneficiary_bank,
    $routing_code, $account_type, $amount, $currency,
    $transfer_fee, $narration, 'completed', $otp_verified
]);
```

### Success Modal JavaScript Example

```javascript
// transfers.js - showTransferSuccess()
function showTransferSuccess(data) {
    console.log('🎉 Transfer successful:', data);

    const message = `Your ${currentTransferType.replace('-', ' ')} transfer of ${transferData.currency} ${transferData.amount.toFixed(2)} to ${transferData.beneficiary_name} has been processed successfully.`;
    document.getElementById('successMessage').textContent = message;

    // Store receipt data for download
    window.transferReceiptData = data;

    // Show success modal
    successModal.show();

    // Reset form after success
    setTimeout(() => {
        resetTransferForm();
    }, 1000);
}
```

### Wire Transfer Success Modal Example

```javascript
// wire-transfers.js - showWireTransferSuccessModal()
function showWireTransferSuccessModal(result) {
    const modalHtml = `
        <div id="wireSuccessModal" class="receipt-modal">
            <div class="modal-overlay" onclick="closeWireSuccessModal()"></div>
            <div class="receipt-container">
                <div class="receipt-body">
                    <div style="text-center; margin-bottom: 2rem;">
                        <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--primary-color);"></i>
                        <h4 style="color: var(--primary-color);">Transfer Submitted Successfully</h4>
                        <p>Your international wire transfer is now pending review</p>
                    </div>
                    <!-- Receipt details here -->
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}
```

## Configuration and Settings

### User OTP Settings

Users can enable/disable OTP for local bank transfers:

```sql
-- Check user OTP setting
SELECT COALESCE(uss.otp_enabled, 1) as otp_enabled
FROM accounts a
LEFT JOIN user_security_settings uss ON a.id = uss.user_id
WHERE a.id = ?
```

### Transfer Limits and Fees

```php
// Transfer fee calculation (example)
$transfer_fee = 0; // Most transfers are free
$total_debit = $amount + $transfer_fee;

// Balance validation
if ($total_debit > $available_balance) {
    throw new Exception('Insufficient balance for this transfer');
}
```

### Billing Code Configuration (Wire Transfers)

```sql
-- Check user billing codes
SELECT COUNT(*) as billing_code_count
FROM user_billing_codes
WHERE user_id = ? AND status = 'active'
```

## API Responses

### Successful Transfer Response

```json
{
    "success": true,
    "message": "Transfer completed successfully",
    "transfer_id": 123,
    "transaction_id": "LBT20250812123456",
    "amount": 1000.00,
    "currency": "USD",
    "fee": 0.00,
    "total_debit": 1000.00,
    "recipient": "John Doe",
    "recipient_account": "**********",
    "new_balance": 4000.00,
    "receipt_email_sent": false
}
```

### Error Response

```json
{
    "success": false,
    "error": "Insufficient balance for this transfer",
    "error_code": "INSUFFICIENT_BALANCE"
}
```

## Performance Considerations

### Database Optimization

1. **Indexes**: Ensure proper indexing on frequently queried columns
   - `user_id` for all transfer tables
   - `transaction_id` for reference lookups
   - `created_at` for date-based queries
   - `status` for status filtering

2. **Query Optimization**: Use prepared statements and avoid N+1 queries

3. **Connection Pooling**: Implement database connection pooling for high traffic

### Frontend Performance

1. **JavaScript Optimization**:
   - Minimize DOM manipulations
   - Use event delegation
   - Implement debouncing for validation

2. **CSS Optimization**:
   - Use CSS variables for theming
   - Minimize reflows and repaints
   - Optimize animations

3. **Network Optimization**:
   - Compress responses
   - Implement caching strategies
   - Use CDN for static assets

This documentation provides a comprehensive overview of the transfer system architecture, implementation details, and operational procedures.
