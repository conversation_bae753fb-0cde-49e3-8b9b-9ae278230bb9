# Transfer System Quick Reference

## 🎯 System Overview

The online banking system supports **3 transfer types** with separate processing flows:

| Transfer Type | Database Table | OTP Required | Processing File | Reference Prefix |
|---------------|----------------|--------------|-----------------|------------------|
| **Local Bank** | `local_transfers` | ✅ (if enabled) | `process-local-transfer-simple.php` | `LBT` |
| **Inter-Bank** | `interbank_transfers` | ❌ (internal) | `process-interbank-transfer-v2.php` | `IBT` |
| **Wire Transfer** | `transfers` | ✅ (always) | `process-wire-transfer.php` | Uses transfers table |

## 🗂️ Key Files

### Frontend Files
```
user/transfers/
├── index.php                    # Main transfer page
├── transfers.js                 # Main transfer logic
├── interbank-transfers.js       # Inter-bank specific logic
└── transfers.css               # Transfer styling

user/wire-transfers/
├── index.php                    # Wire transfer page
├── wire-transfers.js            # Wire transfer logic
└── wire-transfers.css          # Wire transfer styling
```

### Backend Files
```
user/transfers/
├── process-local-transfer-simple.php      # Local transfer processor
├── process-interbank-transfer-v2.php      # Inter-bank processor
└── generate-transfer-otp.php              # OTP generation

user/wire-transfers/
├── process-wire-transfer.php              # Wire transfer processor
├── verify-billing-code.php                # Billing code verification
└── generate-wire-otp.php                  # Wire OTP generation
```

## 🔄 Processing Flows

### Local Bank Transfer
```
Form → Validation → OTP (if enabled) → Processing → Success Modal
```

### Inter-Bank Transfer
```
Form → Validation → Direct Processing → Success Modal
```

### Wire Transfer
```
Form → Validation → Security Loading → Billing Codes → OTP → Processing → Success Modal
```

## 💾 Database Schema

### Local Transfers Table
```sql
local_transfers (
    id, transaction_id, sender_id, sender_account_type,
    beneficiary_account_number, beneficiary_account_name, beneficiary_bank_name,
    routing_code, account_type, amount, currency, transfer_fee,
    narration, status, otp_verified, created_at, updated_at
)
```

### Inter-Bank Transfers Table
```sql
interbank_transfers (
    id, transaction_id, sender_id, recipient_id, sender_account_type,
    amount, currency, transfer_fee, narration, status,
    created_at, completed_at
)
```

### Wire Transfers (Enhanced transfers table)
```sql
transfers (
    -- Base fields --
    id, transaction_id, sender_id, recipient_id, amount, currency, status,
    -- Wire-specific fields --
    swift_code, routing_code, iban, bank_name, bank_address, bank_city, bank_country,
    beneficiary_address, purpose_of_payment, wire_transfer_data,
    billing_codes_verified, billing_verification_data
)
```

## 🎨 Modal Implementations

### Local/Inter-Bank Success Modal
- **File**: `transfers.js` → `showTransferSuccess()`
- **Features**: Transfer confirmation, receipt download, continue button
- **Style**: Bootstrap modal with success theme

### Wire Transfer Success Modal
- **File**: `wire-transfers.js` → `showWireTransferSuccessModal()`
- **Features**: Banking receipt style, comprehensive details, print option
- **Style**: Custom modal with primary color theme

## 🔐 Security Features

### OTP System
- **Local Transfers**: Optional (user setting)
- **Inter-Bank**: Not required (internal)
- **Wire Transfers**: Always required

### Billing Codes (Wire Transfers)
- Progressive verification with animations
- Position-based code validation
- Enhanced security for high-value transfers

## 📝 API Responses

### Success Response
```json
{
    "success": true,
    "message": "Transfer completed successfully",
    "transfer_id": 123,
    "transaction_id": "LBT20250812123456",
    "amount": 1000.00,
    "currency": "USD",
    "recipient": "John Doe",
    "new_balance": 4000.00
}
```

### Error Response
```json
{
    "success": false,
    "error": "Insufficient balance for this transfer"
}
```

## 🛠️ Common Tasks

### Adding New Transfer Type
1. Create new database table
2. Add processing PHP file
3. Create JavaScript module
4. Update main transfer page
5. Implement success modal

### Modifying Success Modal
1. Update JavaScript function (`showTransferSuccess()` or `showWireTransferSuccessModal()`)
2. Modify modal HTML structure
3. Update CSS styling if needed
4. Test modal display and functionality

### Debugging Transfer Issues
1. Check browser console for JavaScript errors
2. Verify PHP error logs
3. Check database transaction logs
4. Validate form data and API responses

## 🧪 Testing

### Test Files
- `test-both-transfers.php` - Tests local and inter-bank transfers
- `test-interbank-modal.php` - Inter-bank modal testing
- `final-transfer-test.php` - Comprehensive testing

### Manual Testing Checklist
- [ ] Form validation works
- [ ] OTP generation and verification
- [ ] Transfer processing completes
- [ ] Database records created
- [ ] Account balances updated
- [ ] Success modal displays
- [ ] Receipt generation works

## 🚨 Common Issues

### Modal Not Displaying
- Check Bootstrap initialization
- Verify modal HTML structure
- Check JavaScript console for errors

### Transfer Processing Fails
- Verify database connections
- Check account balances
- Validate form data
- Review PHP error logs

### OTP Issues
- Check OTP generation endpoint
- Verify database OTP records
- Validate OTP expiration logic

## 📊 Performance Tips

### Database Optimization
- Index frequently queried columns (`user_id`, `transaction_id`, `status`)
- Use prepared statements
- Implement connection pooling

### Frontend Optimization
- Minimize DOM manipulations
- Use event delegation
- Implement form validation debouncing

## 🔧 Configuration

### User Settings
```sql
-- Check user OTP setting
SELECT COALESCE(uss.otp_enabled, 1) as otp_enabled 
FROM accounts a 
LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
WHERE a.id = ?
```

### Transfer Limits
```php
// Example transfer validation
if ($amount > $user['daily_transfer_limit']) {
    throw new Exception('Transfer amount exceeds daily limit');
}
```

## 📚 Documentation Files

1. **`TRANSFER_SYSTEM_DOCUMENTATION.md`** - Comprehensive system documentation
2. **`TRANSFER_IMPLEMENTATION_GUIDE.md`** - Technical implementation details
3. **`TRANSFER_SYSTEM_SUMMARY.md`** - This quick reference guide

## 🎯 Key Takeaways

1. **Separation of Concerns**: Each transfer type has dedicated tables and processing files
2. **Security First**: OTP and billing code verification for sensitive transfers
3. **User Experience**: Progressive loading and professional success modals
4. **Maintainability**: Clear file structure and comprehensive documentation
5. **Scalability**: Modular design allows easy addition of new transfer types

---

**Need Help?** Refer to the detailed documentation files or check the test files for working examples.
