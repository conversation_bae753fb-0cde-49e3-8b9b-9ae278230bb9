<?php
/**
 * Enable Billing Code System
 */

require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "🔧 Enabling Billing Code System\n";
    echo "===============================\n\n";
    
    // Enable billing code system
    $update_enabled = "UPDATE billing_code_settings SET setting_value = '1' WHERE setting_key = 'is_enabled'";
    $db->query($update_enabled);
    echo "✅ Billing code system enabled\n";
    
    // Verify the changes
    $check_enabled = $db->query("SELECT setting_value FROM billing_code_settings WHERE setting_key = 'is_enabled'");
    $check_length = $db->query("SELECT setting_value FROM billing_code_settings WHERE setting_key = 'code_length'");
    
    if ($check_enabled->num_rows > 0) {
        $enabled_value = $check_enabled->fetch_assoc()['setting_value'];
        echo "📊 Billing system enabled: " . ($enabled_value == '1' ? 'Yes' : 'No') . "\n";
    }
    
    if ($check_length->num_rows > 0) {
        $length_value = $check_length->fetch_assoc()['setting_value'];
        echo "🔢 Code length: " . $length_value . "\n";
    }
    
    echo "\n🎉 Billing Code System is now active!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>