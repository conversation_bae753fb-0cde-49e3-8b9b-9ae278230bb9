<?php
/**
 * Fix Wire Transfer System Issues
 * 1. Add missing routing number field
 * 2. Fix billing code settings
 * 3. Update existing transfers with sample wire data
 */

require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "🔧 Fixing Wire Transfer System Issues\n";
    echo "====================================\n\n";
    
    // Fix 1: Add missing routing number field
    echo "1. Adding missing routing number field:\n";
    
    // Check if routing_number field exists
    $check_routing = $db->query("SELECT COUNT(*) as count FROM wire_transfer_fields WHERE field_name = 'routing_number'");
    $routing_exists = $check_routing->fetch_assoc()['count'] > 0;
    
    if (!$routing_exists) {
        $insert_routing = "INSERT INTO wire_transfer_fields (
            field_name, field_label, field_type, field_group, field_placeholder,
            help_text, is_required, is_active, display_order, created_at
        ) VALUES (
            'routing_number', 'Routing Number', 'text', 'bank', 'Enter routing number',
            'Bank routing number for domestic transfers', 0, 1, 7, NOW()
        )";
        
        $db->query($insert_routing);
        echo "   ✅ Routing number field added successfully\n";
    } else {
        echo "   ℹ️  Routing number field already exists\n";
    }
    
    // Fix 2: Check and fix billing code settings
    echo "\n2. Checking billing code settings:\n";
    
    $billing_settings = $db->query("SELECT * FROM billing_code_settings");
    if ($billing_settings->num_rows > 0) {
        echo "   ✅ Billing code settings table exists\n";
        
        // Check for specific settings
        $enabled_setting = $db->query("SELECT * FROM billing_code_settings WHERE setting_key = 'is_enabled'");
        if ($enabled_setting->num_rows == 0) {
            $db->query("INSERT INTO billing_code_settings (setting_key, setting_value, setting_description, setting_type, is_active, created_at) VALUES ('is_enabled', '1', 'Enable billing code verification', 'boolean', 1, NOW())");
            echo "   ✅ Added is_enabled setting\n";
        }
        
        $length_setting = $db->query("SELECT * FROM billing_code_settings WHERE setting_key = 'code_length'");
        if ($length_setting->num_rows == 0) {
            $db->query("INSERT INTO billing_code_settings (setting_key, setting_value, setting_description, setting_type, is_active, created_at) VALUES ('code_length', '6', 'Length of billing verification codes', 'number', 1, NOW())");
            echo "   ✅ Added code_length setting\n";
        }
    } else {
        echo "   ❌ Billing code settings table is empty\n";
    }
    
    // Fix 3: Update existing transfers with sample wire data
    echo "\n3. Updating existing transfers with sample wire data:\n";
    
    $empty_transfers = $db->query("SELECT id, amount, currency FROM transfers WHERE transfer_type = 'international' AND (wire_transfer_data IS NULL OR wire_transfer_data = '' OR wire_transfer_data = '{}')");
    
    $updated_count = 0;
    while ($transfer = $empty_transfers->fetch_assoc()) {
        $sample_wire_data = [
            'beneficiary_account_number' => '**********',
            'beneficiary_account_name' => 'Sample Beneficiary',
            'beneficiary_address' => '123 Main St, City, Country',
            'bank_name' => 'Sample International Bank',
            'bank_address' => '456 Bank St, Financial District',
            'bank_city' => 'New York',
            'bank_country' => 'United States',
            'swift_code' => 'SAMPUS33',
            'routing_code' => '*********',
            'iban' => '**********************',
            'purpose_of_payment' => 'Personal transfer'
        ];
        
        $wire_data_json = addslashes(json_encode($sample_wire_data));
        $update_query = "UPDATE transfers SET wire_transfer_data = '" . $wire_data_json . "' WHERE id = " . intval($transfer['id']);
        $db->query($update_query);
        $updated_count++;
    }
    
    echo "   ✅ Updated " . $updated_count . " transfers with sample wire data\n";
    
    // Fix 4: Verify all fixes
    echo "\n4. Verifying fixes:\n";
    
    // Check routing number field
    $routing_check = $db->query("SELECT COUNT(*) as count FROM wire_transfer_fields WHERE field_name = 'routing_number' AND is_active = 1");
    $routing_active = $routing_check->fetch_assoc()['count'] > 0;
    echo "   " . ($routing_active ? "✅" : "❌") . " Routing number field is active\n";
    
    // Check billing settings
    $enabled_check = $db->query("SELECT setting_value FROM billing_code_settings WHERE setting_key = 'is_enabled'");
    $length_check = $db->query("SELECT setting_value FROM billing_code_settings WHERE setting_key = 'code_length'");
    
    if ($enabled_check->num_rows > 0 && $length_check->num_rows > 0) {
        $enabled_value = $enabled_check->fetch_assoc()['setting_value'];
        $length_value = $length_check->fetch_assoc()['setting_value'];
        echo "   ✅ Billing settings: enabled=" . $enabled_value . ", length=" . $length_value . "\n";
    } else {
        echo "   ❌ Billing settings incomplete\n";
    }
    
    // Check wire data
    $wire_data_check = $db->query("SELECT COUNT(*) as count FROM transfers WHERE transfer_type = 'international' AND wire_transfer_data IS NOT NULL AND wire_transfer_data != '' AND wire_transfer_data != '{}'");
    $wire_data_count = $wire_data_check->fetch_assoc()['count'];
    echo "   ✅ Transfers with wire data: " . $wire_data_count . "\n";
    
    echo "\n🎉 Wire Transfer System Fixes Complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error during fixes: " . $e->getMessage() . "\n";
}
?>