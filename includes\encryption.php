<?php
/**
 * Encryption utilities for sensitive data
 * Used for reversible encryption of passwords and sensitive information
 */

class PasswordEncryption {
    private static $encryption_key = 'OnlineBankingSecretKey2024!@#$%^&*()_+{}|:<>?[]\;\',./~`';
    private static $cipher_method = 'AES-256-CBC';
    
    /**
     * Encrypt a password or sensitive string
     * @param string $data The data to encrypt
     * @return string The encrypted data with IV prepended
     */
    public static function encrypt($data) {
        if (empty($data)) {
            return '';
        }
        
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length(self::$cipher_method));
        $encrypted = openssl_encrypt($data, self::$cipher_method, self::$encryption_key, 0, $iv);
        
        // Prepend IV to encrypted data
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt a password or sensitive string
     * @param string $encrypted_data The encrypted data with IV prepended
     * @return string The decrypted data
     */
    public static function decrypt($encrypted_data) {
        if (empty($encrypted_data)) {
            return '';
        }
        
        try {
            $data = base64_decode($encrypted_data);
            $iv_length = openssl_cipher_iv_length(self::$cipher_method);
            $iv = substr($data, 0, $iv_length);
            $encrypted = substr($data, $iv_length);
            
            $decrypted = openssl_decrypt($encrypted, self::$cipher_method, self::$encryption_key, 0, $iv);
            return $decrypted !== false ? $decrypted : '';
        } catch (Exception $e) {
            return '';
        }
    }
    
    /**
     * Check if a string is encrypted (base64 encoded with proper length)
     * @param string $data The data to check
     * @return bool True if data appears to be encrypted
     */
    public static function isEncrypted($data) {
        if (empty($data)) {
            return false;
        }
        
        // Check if it's base64 encoded and has reasonable length for encrypted data
        $decoded = base64_decode($data, true);
        return $decoded !== false && strlen($decoded) > openssl_cipher_iv_length(self::$cipher_method);
    }
    
    /**
     * Check if a string is a bcrypt hash
     * @param string $data The data to check
     * @return bool True if data appears to be a bcrypt hash
     */
    public static function isBcryptHash($data) {
        return preg_match('/^\$2[ayb]\$.{56}$/', $data);
    }
}

/**
 * Simple encryption functions for ID.me passwords (flat and easy encryption)
 */
function encryptIdmePassword($password) {
    // Simple encryption: ROT13 + Base64 encoding
    return base64_encode(str_rot13($password));
}

function decryptIdmePassword($encrypted_password) {
    // Simple decryption: Base64 decode + ROT13
    $decoded = base64_decode($encrypted_password);
    return $decoded ? str_rot13($decoded) : $encrypted_password;
}

function isIdmePasswordEncrypted($password) {
    // Check if it's base64 encoded and contains only ROT13 characters
    $decoded = base64_decode($password, true);
    if ($decoded === false || base64_encode($decoded) !== $password) {
        return false;
    }
    
    // Additional check: simple encrypted passwords should be shorter and contain readable characters after ROT13
    // Complex AES encryption produces much longer base64 strings
    return strlen($password) < 100 && ctype_print($decoded);
}

/**
 * Helper functions for backward compatibility
 */
function encryptPassword($password) {
    return PasswordEncryption::encrypt($password);
}

function decryptPassword($encrypted_password) {
    return PasswordEncryption::decrypt($encrypted_password);
}

function isPasswordEncrypted($password) {
    return PasswordEncryption::isEncrypted($password);
}

function isPasswordHashed($password) {
    return PasswordEncryption::isBcryptHash($password);
}
?>