# Cleanup Plan (Draft)

This plan identifies non-project or duplicate files and proposes safe actions. No deletions have been performed yet.

## Candidates

- backup/
  - index.php.backup, verify-billing-code.php.backup, wire-transfers.css.backup, wire-transfers.js.backup
  - navigation/sidebar.php.backup
  - Action: Keep for now; move to archive/ or delete after confirming not referenced.

- production.zip (root and install/)
  - Action: Replace with fresh builds; move to releases/ or delete after archiving.

- test files in root
  - Found: test/simple_delete_test_root.php
  - Action: Keep under test/ only. Ensure no additional root-level test files exist.

- logs/*.log
  - Action: Keep (runtime). Optionally rotate/purge older logs on schedule.

- md/delete/*
  - Some documents reference old dependency lists (e.g., PHPUnit/Mockery) no longer installed.
  - Action: Update docs; no code impact.

- production/vendor/* (duplicate of root vendor)
  - Action: Acceptable for packaged production copy. Consider building production from source instead of committing vendor twice.

## Safe Execution Steps

1. Confirm no active includes or references to files in backup/ by searching the codebase.
2. Move backup/ to archive/backup_YYYYMMDD/ for 30 days.
3. Remove production.zip artifacts after confirming a new package can be generated.
4. Ensure all tests live under /test; remove duplicates at root.
5. Replace hardcoded emailUrl() base with config URL in both config/email_templates.php and production copy.

## Risk Notes

- Deleting backup/ prematurely may remove emergency restore points.
- Keep at least one known good production.zip in an external storage.

