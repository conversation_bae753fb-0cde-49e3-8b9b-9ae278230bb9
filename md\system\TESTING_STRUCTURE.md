# Testing Structure

All tests are centralized under the /test directory. There should be no test scripts at the repository root.

## Layout

- test/smoke/ — environment and configuration checks
- test/flow/ — admin and user flow smoke tests and link verification
- test/database/ — DB-specific utilities and checks (existing)
- test/debug/ — ad-hoc debug scripts (existing)
- test/install/ — setup/install tests (existing)

## How to Run

- Browser (recommended on MAMP): http://localhost/online_banking/test/run_all_smoke.php
- CLI (if php is on PATH):
  - php test/smoke/environment_smoke.php
  - php test/flow/admin_flow_smoke.php
  - php test/flow/user_flow_smoke.php
  - php test/flow/link_verifier.php
  - php test/run_all_smoke.php

## Notes

- The smoke tests verify file existence, PHP syntax, and basic DB connectivity.
- For end-to-end functional testing in a browser session (with authentication), use the existing test pages in /test and the admin/user UIs.

