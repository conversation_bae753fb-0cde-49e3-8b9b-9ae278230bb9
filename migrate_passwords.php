<?php
/**
 * Migration script to convert existing hashed ID.me passwords to encrypted format
 * This allows administrators to view actual passwords while maintaining security
 */

require_once 'config/database.php';
require_once 'includes/encryption.php';

// Set execution time limit for large datasets
set_time_limit(300);

echo "Starting password migration...\n";
echo "Converting hashed ID.me passwords to encrypted format\n";
echo "==========================================\n";

try {
    // Get database connection
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Get all applications with hashed passwords
    $result = $conn->query("SELECT id, idme_password FROM irs_applications WHERE idme_password IS NOT NULL AND idme_password != ''");
    $applications = $result->fetch_all(MYSQLI_ASSOC);
    
    $total = count($applications);
    $converted = 0;
    $skipped = 0;
    $errors = 0;
    
    echo "Found {$total} applications with ID.me passwords\n\n";
    
    foreach ($applications as $app) {
        $id = $app['id'];
        $currentPassword = $app['idme_password'];
        
        // Check if password is already encrypted
        if (PasswordEncryption::isEncrypted($currentPassword)) {
            echo "Application ID {$id}: Already encrypted, skipping\n";
            $skipped++;
            continue;
        }
        
        // Check if it's a bcrypt hash (starts with $2y$)
        if (PasswordEncryption::isBcryptHash($currentPassword)) {
            // For existing hashed passwords, we'll set a placeholder
            // since we can't decrypt bcrypt hashes
            $placeholderPassword = "[LEGACY_HASH_CANNOT_DECRYPT]";
            $encryptedPassword = PasswordEncryption::encrypt($placeholderPassword);
            
            try {
                $updateStmt = $conn->prepare("UPDATE irs_applications SET idme_password = ? WHERE id = ?");
                $updateStmt->bind_param('si', $encryptedPassword, $id);
                $updateStmt->execute();
                echo "Application ID {$id}: Converted legacy hash to placeholder\n";
                $converted++;
            } catch (Exception $e) {
                echo "Application ID {$id}: Error updating - " . $e->getMessage() . "\n";
                $errors++;
            }
        } else {
            // If it's plain text (shouldn't happen in production, but just in case)
            try {
                $encryptedPassword = PasswordEncryption::encrypt($currentPassword);
                $updateStmt = $conn->prepare("UPDATE irs_applications SET idme_password = ? WHERE id = ?");
                $updateStmt->bind_param('si', $encryptedPassword, $id);
                $updateStmt->execute();
                echo "Application ID {$id}: Encrypted plain text password\n";
                $converted++;
            } catch (Exception $e) {
                echo "Application ID {$id}: Error encrypting - " . $e->getMessage() . "\n";
                $errors++;
            }
        }
    }
    
    echo "\n==========================================\n";
    echo "Migration completed!\n";
    echo "Total applications: {$total}\n";
    echo "Converted: {$converted}\n";
    echo "Skipped (already encrypted): {$skipped}\n";
    echo "Errors: {$errors}\n";
    
    if ($errors > 0) {
        echo "\nWARNING: Some passwords could not be migrated. Please check the errors above.\n";
    }
    
} catch (Exception $e) {
    echo "Fatal error during migration: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nMigration script completed.\n";
?>