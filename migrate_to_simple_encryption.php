<?php
/**
 * Migration script to convert existing ID.me passwords to simple encryption
 * This will convert complex encrypted passwords to simple encryption for admin viewing
 */

require_once 'config/config.php';
require_once 'includes/encryption.php';

$db = getDB();

echo "Starting migration to simple encryption for ID.me passwords...\n";

// Get all applications with ID.me passwords
$query = "SELECT id, idme_password FROM irs_applications WHERE idme_password IS NOT NULL AND idme_password != ''";
$result = $db->query($query);

$total_processed = 0;
$converted_count = 0;
$already_simple = 0;
$failed_count = 0;

while ($row = $result->fetch_assoc()) {
    $total_processed++;
    $application_id = $row['id'];
    $current_password = $row['idme_password'];
    
    echo "Processing application ID: $application_id\n";
    
    // Check if already using simple encryption
    if (isIdmePasswordEncrypted($current_password)) {
        echo "  - Already using simple encryption\n";
        $already_simple++;
        continue;
    }
    
    $plain_password = null;
    
    // Try to decrypt if it's complex encryption
    if (PasswordEncryption::isEncrypted($current_password)) {
        try {
            $plain_password = PasswordEncryption::decrypt($current_password);
            echo "  - Decrypted complex encryption\n";
        } catch (Exception $e) {
            echo "  - Failed to decrypt complex encryption: " . $e->getMessage() . "\n";
            $failed_count++;
            continue;
        }
    } elseif (PasswordEncryption::isBcryptHash($current_password)) {
        echo "  - Skipping bcrypt hash (cannot be decrypted)\n";
        $failed_count++;
        continue;
    } else {
        // Assume it's plain text
        $plain_password = $current_password;
        echo "  - Processing plain text password\n";
    }
    
    if ($plain_password) {
        // Convert to simple encryption
        $simple_encrypted = encryptIdmePassword($plain_password);
        
        // Update in database
        $update_query = "UPDATE irs_applications SET idme_password = ? WHERE id = ?";
        $update_result = $db->query($update_query, [$simple_encrypted, $application_id]);
        
        if ($update_result) {
            echo "  - Successfully converted to simple encryption\n";
            $converted_count++;
        } else {
            echo "  - Failed to update database\n";
            $failed_count++;
        }
    }
}

echo "\n=== Migration Summary ===\n";
echo "Total applications processed: $total_processed\n";
echo "Already using simple encryption: $already_simple\n";
echo "Successfully converted: $converted_count\n";
echo "Failed conversions: $failed_count\n";
echo "\nMigration completed!\n";
?>