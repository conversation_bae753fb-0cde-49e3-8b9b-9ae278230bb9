<?php
/**
 * Centralized Email Template System for Online Banking Platform
 * Provides consistent branding and formatting across all email communications
 */

/**
 * Master Email Template Function
 * Creates the base HTML structure with consistent banking branding
 */
function generateMasterEmailTemplate($title, $header_text, $content, $footer_note = '', $cta_button = null) {
    // Include super admin settings for dynamic contact info
    require_once __DIR__ . '/super_admin_settings.php';
    $contact_info = getEmailContactInfo();

    $bank_name = $contact_info['site_name'];
    $current_year = date('Y');

    // Get dynamic colors from super admin settings
    try {
        require_once __DIR__ . '/database.php';
        $db = getDB();
        $color_result = $db->query("SELECT setting_key, setting_value FROM super_admin_settings WHERE setting_key IN ('theme_color', 'secondary_color')");

        $colors = [];
        while ($row = $color_result->fetch_assoc()) {
            $colors[$row['setting_key']] = $row['setting_value'];
        }

        $primary_color = $colors['theme_color'] ?? '#206bc4'; // Professional banking blue
        $secondary_color = $colors['secondary_color'] ?? '#6c757d';
    } catch (Exception $e) {
        // Fallback to default colors if database query fails
        $primary_color = '#1e40af';
        $secondary_color = '#3b82f6';
    }

    $success_color = '#059669';
    $warning_color = '#d97706';
    $danger_color = '#dc2626';
    
    // CTA Button HTML
    $cta_html = '';
    if ($cta_button && isset($cta_button['text']) && isset($cta_button['url'])) {
        $button_color = $cta_button['color'] ?? $primary_color;
        $cta_html = "
        <div style='text-align: center; margin: 30px 0;'>
            <a href='{$cta_button['url']}' 
               style='background: {$button_color}; color: white; padding: 15px 30px; text-decoration: none; 
                      border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;
                      box-shadow: 0 4px 6px rgba(0,0,0,0.1); transition: all 0.3s ease;'>
                {$cta_button['text']}
            </a>
        </div>";
    }
    
    return "
    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <meta http-equiv='X-UA-Compatible' content='ie=edge'>
        <title>{$title} - {$bank_name}</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                line-height: 1.6; color: #374151; background-color: #f9fafb; margin: 0; padding: 0;
            }
            .email-container { 
                max-width: 600px; margin: 0 auto; background-color: #ffffff; 
                box-shadow: 0 10px 25px rgba(0,0,0,0.1); border-radius: 12px; overflow: hidden;
            }
            .header { 
                background: linear-gradient(135deg, {$primary_color} 0%, {$secondary_color} 100%); 
                color: white; padding: 40px 30px; text-align: center; position: relative;
            }
            .header::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>') repeat;
            }
            .header-content { position: relative; z-index: 1; }
            .bank-logo {
                font-size: 24px; font-weight: 600; margin-bottom: 15px;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .header-title {
                font-size: 20px; font-weight: 500; margin-bottom: 0px;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .content { padding: 40px 30px; }
            .content h2 { color: #1f2937; margin-bottom: 20px; font-size: 20px; font-weight: 600; }
            .content h3 { color: #374151; margin: 25px 0 15px 0; font-size: 18px; font-weight: 500; }
            .content p { margin-bottom: 16px; color: #4b5563; line-height: 1.7; }
            .content ul, .content ol { margin: 16px 0; padding-left: 24px; color: #4b5563; }
            .content li { margin-bottom: 8px; line-height: 1.6; }
            
            .info-box {
                background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
                border: 1px solid #bfdbfe; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.08);
                position: relative; overflow: hidden;
            }
            .info-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, {$primary_color} 0%, #60a5fa 100%);
            }
            .success-box {
                background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
                border: 1px solid #a7f3d0; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(5, 150, 105, 0.08);
                position: relative; overflow: hidden;
            }
            .success-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, {$success_color} 0%, #34d399 100%);
            }
            .warning-box {
                background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
                border: 1px solid #fed7aa; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(217, 119, 6, 0.08);
                position: relative; overflow: hidden;
            }
            .warning-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, {$warning_color} 0%, #fbbf24 100%);
            }
            .danger-box {
                background: linear-gradient(135deg, #fef2f2 0%, #fef1f1 100%);
                border: 1px solid #fecaca; border-radius: 12px;
                padding: 24px; margin: 20px 0;
                box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
                position: relative; overflow: hidden;
            }
            .danger-box::before {
                content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px;
                background: linear-gradient(90deg, {$danger_color} 0%, #f87171 100%);
            }
            
            .account-details { 
                background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; 
                padding: 24px; margin: 24px 0; 
            }
            .detail-row { 
                display: flex; justify-content: space-between; align-items: center; 
                padding: 12px 0; border-bottom: 1px solid #e5e7eb; 
            }
            .detail-row:last-child { border-bottom: none; }
            .detail-label { font-weight: 500; color: #6b7280; }
            .detail-value { font-weight: 600; color: #1f2937; }
            .highlight { 
                background: {$primary_color}; color: white; padding: 4px 12px; 
                border-radius: 6px; font-weight: 600; font-size: 14px;
            }
            
            .footer { 
                background: #f8fafc; padding: 30px; text-align: center; 
                border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px; 
            }
            .footer-links { margin: 20px 0; }
            .footer-links a { 
                color: {$primary_color}; text-decoration: none; margin: 0 15px; 
                font-weight: 500; 
            }
            .footer-links a:hover { text-decoration: underline; }
            
            @media (max-width: 600px) {
                .email-container { margin: 0; border-radius: 0; }
                .header, .content, .footer { padding: 30px 20px; }
                .detail-row { flex-direction: column; align-items: flex-start; }
                .detail-label { margin-bottom: 4px; }
                .bank-logo { font-size: 24px; }
                .header-title { font-size: 20px; }
            }
        </style>
    </head>
    <body>
        <div style='background-color: #f9fafb; padding: 20px 0; min-height: 100vh;'>
            <div class='email-container'>
                <div class='header'>
                    <div class='header-content'>
                        <div class='bank-logo'>{$bank_name}</div>
                        <div class='header-title'>{$header_text}</div>
                    </div>
                </div>
                
                <div class='content'>
                    {$content}
                    {$cta_html}
                </div>
                
                <div class='footer'>
                    <div style='margin-bottom: 20px;'>
                        <strong>{$bank_name}</strong><br>
                        {$contact_info['email_footer_text']}
                    </div>

                    <div class='footer-links'>
                        <a href='{$contact_info['site_url']}'>Online Banking</a>
                        <a href='{$contact_info['help_center_url']}'>Support Center</a>
                        <a href='mailto:{$contact_info['support_email']}'>Contact Us</a>
                        <a href='mailto:{$contact_info['security_email']}'>Security</a>
                    </div>
                    
                    " . ($footer_note ? "<div style='margin: 20px 0; padding: 15px; background: #f1f5f9; border-radius: 6px; font-style: italic;'>{$footer_note}</div>" : "") . "
                    
                    <div style='margin-top: 20px; font-size: 12px; color: #9ca3af;'>
                        <p>This email was sent from a secure, monitored system. Please do not reply to this email.</p>
                        <p>&copy; {$current_year} {$bank_name}. All rights reserved.</p>
                        <p>If you have questions, please contact our support team.</p>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>";
}

/**
 * Account Management Templates
 */

// Pending Approval Email Template
function generatePendingApprovalEmailTemplate($user_data) {
    // Get dynamic contact information
    require_once __DIR__ . '/super_admin_settings.php';
    $contact_info = getEmailContactInfo();

    $content = "
    <div class='info-box'>
        <h2 style='color: #3b82f6; margin: 0 0 15px 0;'>Registration Received</h2>
        <p style='margin: 0; font-size: 16px;'>Thank you for registering with {$contact_info['site_name']}. Your account is pending admin approval.</p>
    </div>

    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Name:</span>
            <span class='detail-value'>{$user_data['first_name']} {$user_data['last_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Username:</span>
            <span class='detail-value'>{$user_data['username']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Email:</span>
            <span class='detail-value'>{$user_data['email']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Status:</span>
            <span class='detail-value' style='color: #d97706; font-weight: 600;'>Pending Approval</span>
        </div>
    </div>

    <div class='warning-box'>
        <h3 style='color: #d97706; margin: 0 0 15px 0;'>What's Next?</h3>
        <ul style='margin: 0; color: #92400e;'>
            <li>Admin review within 24-48 hours</li>
            <li>Email notification upon approval</li>
            <li>Cannot log in until activated</li>
        </ul>
    </div>

    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>Need Help?</h3>
        <p style='margin: 0; color: #1e3a8a;'>Contact support: {$contact_info['support_email']}</p>
    </div>";

    return generateMasterEmailTemplate(
        'Registration Pending Approval',
        'Registration Received',
        $content,
        'This email confirms your registration. Please keep it for your records.',
        null
    );
}

// Welcome Email Template
function generateWelcomeEmailTemplate($user_data) {
    // Get dynamic contact information
    require_once __DIR__ . '/super_admin_settings.php';
    $contact_info = getEmailContactInfo();

    $content = "
    <div class='success-box'>
        <h2 style='color: #059669; margin: 0 0 15px 0;'>Welcome, {$user_data['first_name']}!</h2>
        <p style='margin: 0; font-size: 16px;'>Your account has been successfully created and is ready to use.</p>
    </div>
    
    <h3>📋 Your Account Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account Holder:</span>
            <span class='detail-value'>{$user_data['first_name']} {$user_data['last_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Number:</span>
            <span class='detail-value highlight'>{$user_data['account_number']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Type:</span>
            <span class='detail-value'>" . ucfirst($user_data['account_type']) . "</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Currency:</span>
            <span class='detail-value'>{$user_data['currency']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Current Balance:</span>
            <span class='detail-value'>$" . number_format($user_data['balance'], 2) . "</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Username:</span>
            <span class='detail-value'>{$user_data['username']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Status:</span>
            <span class='detail-value' style='color: #059669;'>✅ " . ucfirst($user_data['status']) . "</span>
        </div>
    </div>
    
    <div class='warning-box'>
        <h3 style='color: #d97706; margin: 0 0 15px 0;'>🔐 Security Information</h3>
        <ul style='margin: 0; color: #92400e;'>
            <li>Your password has been set as provided during registration</li>
            <li>Please log in and change your password if needed</li>
            <li>Two-factor authentication (OTP) is enabled for your security</li>
            <li>Never share your login credentials with anyone</li>
            <li>Always log out when using shared computers</li>
        </ul>
    </div>
    
    <h3>🎯 Getting Started</h3>
    <ol>
        <li><strong>Log in</strong> to your account using your username and password</li>
        <li><strong>Complete your profile</strong> by adding additional information</li>
        <li><strong>Explore features</strong> like transfers, virtual cards, and more</li>
        <li><strong>Set up alerts</strong> to stay informed about your account activity</li>
        <li><strong>Contact support</strong> if you need any assistance</li>
    </ol>
    
    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>📞 Need Help?</h3>
        <p style='margin: 0; color: #1e3a8a;'>Our support team is here to help you get started:</p>
        <ul style='margin: 10px 0 0 0; color: #1e3a8a;'>
            <li>📧 Email: {$contact_info['support_email']}</li>
            <li>📞 Phone: {$contact_info['support_phone']} (24/7)</li>
            <li>💬 Live chat available on our website</li>
            <li>🌐 Visit our help center for FAQs</li>
        </ul>
    </div>";
    
    $cta_button = [
        'text' => '🚀 Access Your Account',
        'url' => emailUrl('login.php'),
        'color' => '#059669'
    ];
    
    return generateMasterEmailTemplate(
        'Welcome to Your New Account',
        'Welcome to Your New Account!',
        $content,
        'This email contains important account information. Please keep it for your records.',
        $cta_button
    );
}

// Account Suspension Template
function generateAccountSuspensionEmailTemplate($user_data, $reason) {
    // Get dynamic contact information
    require_once __DIR__ . '/super_admin_settings.php';
    $contact_info = getEmailContactInfo();
    $content = "
    <div class='danger-box'>
        <h2 style='color: #dc2626; margin: 0 0 15px 0;'>⚠️ Account Temporarily Suspended</h2>
        <p style='margin: 0; font-size: 16px; color: #991b1b;'>Your account has been temporarily suspended for security reasons.</p>
    </div>
    
    <h3>📋 Account Information</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account Holder:</span>
            <span class='detail-value'>{$user_data['first_name']} {$user_data['last_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Number:</span>
            <span class='detail-value'>{$user_data['account_number']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Suspension Date:</span>
            <span class='detail-value'>" . date('F j, Y \a\t g:i A') . "</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Reason:</span>
            <span class='detail-value' style='color: #dc2626;'>{$reason}</span>
        </div>
    </div>
    
    <h3>🔍 What This Means</h3>
    <ul>
        <li>Your account access has been temporarily restricted</li>
        <li>All transactions are currently on hold</li>
        <li>Your funds remain safe and secure</li>
        <li>This is a precautionary measure to protect your account</li>
    </ul>
    
    <h3>📞 Next Steps</h3>
    <p>To reactivate your account, please contact our security team immediately:</p>
    <div class='info-box'>
        <ul style='margin: 0; color: #1e3a8a;'>
            <li>📞 Security Hotline: {$contact_info['security_phone']}</li>
            <li>📧 Email: {$contact_info['security_email']}</li>
            <li>🕒 Available: 24/7</li>
        </ul>
    </div>";
    
    $cta_button = [
        'text' => '📞 Contact Security Team',
        'url' => 'tel:' . $contact_info['security_phone'],
        'color' => '#dc2626'
    ];
    
    return generateMasterEmailTemplate(
        'Account Security Alert',
        'Account Temporarily Suspended',
        $content,
        'This is an automated security notification. Please contact us immediately if you did not expect this action.',
        $cta_button
    );
}
// Account Reactivation Template
function generateAccountReactivationEmailTemplate($user_data) {
    $content = "
    <div class='success-box'>
        <h2 style='color: #059669; margin: 0 0 15px 0;'>✅ Account Reactivated Successfully</h2>
        <p style='margin: 0; font-size: 16px;'>Great news! Your account has been reactivated and is now fully operational.</p>
    </div>

    <h3>📋 Account Status</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account Number:</span>
            <span class='detail-value'>{$user_data['account_number']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Reactivation Date:</span>
            <span class='detail-value'>" . date('F j, Y \a\t g:i A') . "</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Current Status:</span>
            <span class='detail-value' style='color: #059669;'>✅ Active</span>
        </div>
    </div>

    <h3>🎯 You Can Now</h3>
    <ul>
        <li>Access your online banking account</li>
        <li>Make transfers and payments</li>
        <li>View account statements</li>
        <li>Use all banking services</li>
    </ul>

    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>🔐 Security Reminder</h3>
        <p style='margin: 0; color: #1e3a8a;'>For your security, please review your account activity and update your password if needed.</p>
    </div>";

    $cta_button = [
        'text' => '🚀 Access Your Account',
        'url' => emailUrl('login.php'),
        'color' => '#059669'
    ];

    return generateMasterEmailTemplate(
        'Account Reactivated',
        'Your Account is Now Active',
        $content,
        'Welcome back! Your account is ready for use.',
        $cta_button
    );
}

/**
 * Authentication Templates
 */

// OTP Verification Template
function generateOTPEmailTemplate($user_data, $otp_code, $expires_in = 10) {
    $content = "
    <div class='info-box'>
        <h2 style='color: #1e40af; margin: 0 0 15px 0;'>🔐 Login Verification</h2>
        <p style='margin: 0; font-size: 16px;'>Please use the verification code below to complete your login.</p>
    </div>

    <div style='text-align: center; margin: 30px 0;'>
        <div style='background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white;
                    padding: 25px; border-radius: 12px; display: inline-block; box-shadow: 0 4px 12px rgba(30,64,175,0.2);'>
            <div style='font-size: 14px; opacity: 0.9; margin-bottom: 10px;'>Verification Code</div>
            <div style='font-size: 32px; font-weight: 700; letter-spacing: 6px; font-family: monospace;'>{$otp_code}</div>
            <div style='font-size: 12px; opacity: 0.8; margin-top: 10px;'>Expires in {$expires_in} minutes</div>
        </div>
    </div>

    <div class='warning-box'>
        <h3 style='color: #d97706; margin: 0 0 15px 0;'>🔒 Security Notice</h3>
        <ul style='margin: 0; color: #92400e;'>
            <li>This code is valid for {$expires_in} minutes only</li>
            <li>Never share this code with anyone</li>
            <li>If you didn't request this login, please contact support immediately</li>
        </ul>
    </div>

    <div class='warning-box'>
        <h3 style='color: #d97706; margin: 0 0 15px 0;'>🛡️ Security Notice</h3>
        <ul style='margin: 0; color: #92400e;'>
            <li>This code is valid for {$expires_in} minutes only</li>
            <li>Never share this code with anyone</li>
            <li>If you didn't request this login, contact us immediately</li>
            <li>Our team will never ask for your verification code</li>
        </ul>
    </div>";

    return generateMasterEmailTemplate(
        'Login Verification Code',
        'Secure Login Verification',
        $content,
        'This verification code was generated for your security. Do not share it with anyone.'
    );
}

// PIN Reset Template
function generatePINResetEmailTemplate($user_data, $reset_token) {
    $content = "
    <div class='warning-box'>
        <h2 style='color: #d97706; margin: 0 0 15px 0;'>🔑 PIN Reset Request</h2>
        <p style='margin: 0; font-size: 16px;'>A request has been made to reset your account PIN.</p>
    </div>

    <h3>📋 Reset Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account:</span>
            <span class='detail-value'>{$user_data['account_number']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Request Time:</span>
            <span class='detail-value'>" . date('F j, Y \a\t g:i A') . "</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Valid Until:</span>
            <span class='detail-value'>" . date('F j, Y \a\t g:i A', strtotime('+24 hours')) . "</span>
        </div>
    </div>

    <h3>🔐 To Reset Your PIN</h3>
    <ol>
        <li>Click the secure reset link below</li>
        <li>Verify your identity</li>
        <li>Create a new 4-digit PIN</li>
        <li>Confirm your new PIN</li>
    </ol>

    <div class='danger-box'>
        <h3 style='color: #dc2626; margin: 0 0 15px 0;'>⚠️ Important Security Information</h3>
        <ul style='margin: 0; color: #991b1b;'>
            <li>This link expires in 24 hours</li>
            <li>If you didn't request this reset, contact us immediately</li>
            <li>Never share your PIN with anyone</li>
            <li>Choose a PIN that's not easily guessable</li>
        </ul>
    </div>";

    $cta_button = [
        'text' => '🔑 Reset My PIN',
        'url' => emailUrl("auth/reset-pin.php?token={$reset_token}"),
        'color' => '#d97706'
    ];

    return generateMasterEmailTemplate(
        'PIN Reset Request',
        'Reset Your Account PIN',
        $content,
        'This link is valid for 24 hours. If you did not request this reset, please contact our security team.',
        $cta_button
    );
}

/**
 * Transaction Templates
 */

// Credit Alert Template
function generateCreditAlertEmailTemplate($user_data, $transaction_data) {
    $content = "
    <div class='success-box'>
        <h2 style='color: #059669; margin: 0 0 15px 0;'>💰 Credit Alert - Money Received</h2>
        <p style='margin: 0; font-size: 16px;'>Your account has been credited successfully.</p>
    </div>

    <h3>💳 Transaction Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Amount:</span>
            <span class='detail-value' style='color: #059669; font-size: 18px; font-weight: 700;'>
                +{$transaction_data['currency']} " . number_format($transaction_data['amount'], 2) . "
            </span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Transaction ID:</span>
            <span class='detail-value'>{$transaction_data['transaction_id']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Date & Time:</span>
            <span class='detail-value'>{$transaction_data['date_time']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>From:</span>
            <span class='detail-value'>{$transaction_data['sender_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Reference:</span>
            <span class='detail-value'>{$transaction_data['reference']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>New Balance:</span>
            <span class='detail-value highlight'>{$transaction_data['currency']} " . number_format($transaction_data['new_balance'], 2) . "</span>
        </div>
    </div>

    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>📊 Account Summary</h3>
        <p style='margin: 0; color: #1e3a8a;'>
            Account: {$user_data['account_number']}<br>
            Available Balance: {$transaction_data['currency']} " . number_format($transaction_data['new_balance'], 2) . "
        </p>
    </div>";

    $cta_button = [
        'text' => '📱 View Full Statement',
        'url' => emailUrl('dashboard/transactions.php'),
        'color' => '#059669'
    ];

    return generateMasterEmailTemplate(
        'Credit Alert',
        'Money Received Successfully',
        $content,
        'This is an automated transaction alert for your security.',
        $cta_button
    );
}

// Debit Alert Template
function generateDebitAlertEmailTemplate($user_data, $transaction_data) {
    $content = "
    <div class='warning-box'>
        <h2 style='color: #d97706; margin: 0 0 15px 0;'>💸 Debit Alert - Money Sent</h2>
        <p style='margin: 0; font-size: 16px;'>Your account has been debited successfully.</p>
    </div>

    <h3>💳 Transaction Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Amount:</span>
            <span class='detail-value' style='color: #d97706; font-size: 18px; font-weight: 700;'>
                -{$transaction_data['currency']} " . number_format($transaction_data['amount'], 2) . "
            </span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Transaction ID:</span>
            <span class='detail-value'>{$transaction_data['transaction_id']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Date & Time:</span>
            <span class='detail-value'>{$transaction_data['date_time']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>To:</span>
            <span class='detail-value'>{$transaction_data['recipient_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Reference:</span>
            <span class='detail-value'>{$transaction_data['reference']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>New Balance:</span>
            <span class='detail-value highlight'>{$transaction_data['currency']} " . number_format($transaction_data['new_balance'], 2) . "</span>
        </div>
    </div>

    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>📊 Account Summary</h3>
        <p style='margin: 0; color: #1e3a8a;'>
            Account: {$user_data['account_number']}<br>
            Available Balance: {$transaction_data['currency']} " . number_format($transaction_data['new_balance'], 2) . "
        </p>
    </div>";

    $cta_button = [
        'text' => '📱 View Transaction Details',
        'url' => emailUrl('dashboard/transactions.php'),
        'color' => '#d97706'
    ];

    return generateMasterEmailTemplate(
        'Debit Alert',
        'Money Sent Successfully',
        $content,
        'This is an automated transaction alert for your security.',
        $cta_button
    );
}

// Failed Transaction Template
function generateFailedTransactionEmailTemplate($user_data, $transaction_data, $failure_reason) {
    $content = "
    <div class='danger-box'>
        <h2 style='color: #dc2626; margin: 0 0 15px 0;'>❌ Transaction Failed</h2>
        <p style='margin: 0; font-size: 16px;'>Your recent transaction could not be completed.</p>
    </div>

    <h3>💳 Transaction Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Amount:</span>
            <span class='detail-value'>{$transaction_data['currency']} " . number_format($transaction_data['amount'], 2) . "</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Transaction ID:</span>
            <span class='detail-value'>{$transaction_data['transaction_id']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Attempted Time:</span>
            <span class='detail-value'>{$transaction_data['date_time']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Recipient:</span>
            <span class='detail-value'>{$transaction_data['recipient_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Failure Reason:</span>
            <span class='detail-value' style='color: #dc2626;'>{$failure_reason}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Balance:</span>
            <span class='detail-value'>{$transaction_data['currency']} " . number_format($user_data['balance'], 2) . " (Unchanged)</span>
        </div>
    </div>

    <h3>🔧 What to Do Next</h3>
    <ul>
        <li>Check your account balance and transaction limits</li>
        <li>Verify recipient account details</li>
        <li>Ensure sufficient funds are available</li>
        <li>Try the transaction again or contact support</li>
    </ul>

    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>💡 Need Help?</h3>
        <p style='margin: 0; color: #1e3a8a;'>If you continue to experience issues, our support team is ready to assist you 24/7.</p>
    </div>";

    $cta_button = [
        'text' => '🔄 Try Again',
        'url' => emailUrl('transfers/send-money.php'),
        'color' => '#dc2626'
    ];

    return generateMasterEmailTemplate(
        'Transaction Failed',
        'Transaction Could Not Be Completed',
        $content,
        'No money was deducted from your account due to this failed transaction.',
        $cta_button
    );
}

/**
 * Security Templates
 */

// Login Alert Template
function generateLoginAlertEmailTemplate($user_data, $login_details) {
    $content = "
    <div class='info-box'>
        <h2 style='color: #1e40af; margin: 0 0 15px 0;'>🔐 New Login Detected</h2>
        <p style='margin: 0; font-size: 16px;'>A new login to your account has been detected.</p>
    </div>

    <h3>📋 Login Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account:</span>
            <span class='detail-value'>{$user_data['first_name']} {$user_data['last_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Login Time:</span>
            <span class='detail-value'>{$login_details['timestamp']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>IP Address:</span>
            <span class='detail-value'>{$login_details['ip_address']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Device:</span>
            <span class='detail-value'>{$login_details['device']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Location:</span>
            <span class='detail-value'>{$login_details['location']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Browser:</span>
            <span class='detail-value'>{$login_details['browser']}</span>
        </div>
    </div>

    <div class='success-box'>
        <h3 style='color: #059669; margin: 0 0 15px 0;'>✅ Was This You?</h3>
        <p style='margin: 0; color: #065f46;'>If you recognize this login, no action is needed. Your account remains secure.</p>
    </div>

    <div class='danger-box'>
        <h3 style='color: #dc2626; margin: 0 0 15px 0;'>⚠️ Didn't Recognize This Login?</h3>
        <ul style='margin: 0; color: #991b1b;'>
            <li>Change your password immediately</li>
            <li>Review your recent account activity</li>
            <li>Contact our security team</li>
            <li>Consider enabling additional security features</li>
        </ul>
    </div>";

    $cta_button = [
        'text' => '🔒 Secure My Account',
        'url' => emailUrl('security/account-security.php'),
        'color' => '#dc2626'
    ];

    return generateMasterEmailTemplate(
        'Login Alert',
        'New Account Login Detected',
        $content,
        'This is an automated security notification to keep your account safe.',
        $cta_button
    );
}

// Suspicious Activity Template
function generateSuspiciousActivityEmailTemplate($user_data, $activity_details) {
    $content = "
    <div class='danger-box'>
        <h2 style='color: #dc2626; margin: 0 0 15px 0;'>🚨 Suspicious Activity Detected</h2>
        <p style='margin: 0; font-size: 16px;'>We've detected unusual activity on your account that requires your attention.</p>
    </div>

    <h3>🔍 Activity Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Activity Type:</span>
            <span class='detail-value' style='color: #dc2626;'>{$activity_details['type']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Detection Time:</span>
            <span class='detail-value'>{$activity_details['timestamp']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>IP Address:</span>
            <span class='detail-value'>{$activity_details['ip_address']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Location:</span>
            <span class='detail-value'>{$activity_details['location']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Risk Level:</span>
            <span class='detail-value' style='color: #dc2626; font-weight: 700;'>{$activity_details['risk_level']}</span>
        </div>
    </div>

    <h3>🛡️ Immediate Actions Taken</h3>
    <ul>
        <li>Account access has been temporarily restricted</li>
        <li>All pending transactions have been paused</li>
        <li>Security monitoring has been increased</li>
        <li>This incident has been logged for investigation</li>
    </ul>

    <h3>📞 What You Need to Do</h3>
    <ol>
        <li><strong>Contact our security team immediately</strong></li>
        <li><strong>Verify your identity</strong></li>
        <li><strong>Review recent account activity</strong></li>
        <li><strong>Update your security settings</strong></li>
    </ol>

    <div class='warning-box'>
        <h3 style='color: #d97706; margin: 0 0 15px 0;'>⏰ Time Sensitive</h3>
        <p style='margin: 0; color: #92400e;'>Please contact us within 24 hours to restore full account access. Your account security is our top priority.</p>
    </div>";

    $cta_button = [
        'text' => '🚨 Contact Security Team',
        'url' => 'tel:1-800-SECURITY',
        'color' => '#dc2626'
    ];

    return generateMasterEmailTemplate(
        'Security Alert',
        'Suspicious Activity Detected',
        $content,
        'This is a critical security alert. Please take immediate action to secure your account.',
        $cta_button
    );
}

/**
 * Administrative Templates
 */

// KYC Status Update Template
function generateKYCStatusEmailTemplate($user_data, $kyc_status, $notes = '') {
    $status_colors = [
        'verified' => '#059669',
        'pending' => '#d97706',
        'rejected' => '#dc2626',
        'under_review' => '#3b82f6'
    ];

    $status_icons = [
        'verified' => '✅',
        'pending' => '⏳',
        'rejected' => '❌',
        'under_review' => '🔍'
    ];

    $color = $status_colors[$kyc_status] ?? '#6b7280';
    $icon = $status_icons[$kyc_status] ?? '📋';

    $content = "
    <div class='" . ($kyc_status === 'verified' ? 'success' : ($kyc_status === 'rejected' ? 'danger' : 'warning')) . "-box'>
        <h2 style='color: {$color}; margin: 0 0 15px 0;'>{$icon} KYC Status Update</h2>
        <p style='margin: 0; font-size: 16px;'>Your Know Your Customer (KYC) verification status has been updated.</p>
    </div>

    <h3>📋 Verification Details</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account:</span>
            <span class='detail-value'>{$user_data['account_number']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Holder:</span>
            <span class='detail-value'>{$user_data['first_name']} {$user_data['last_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Previous Status:</span>
            <span class='detail-value'>Pending Review</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>New Status:</span>
            <span class='detail-value' style='color: {$color}; font-weight: 700;'>{$icon} " . ucfirst(str_replace('_', ' ', $kyc_status)) . "</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Update Date:</span>
            <span class='detail-value'>" . date('F j, Y \a\t g:i A') . "</span>
        </div>
    </div>";

    if ($kyc_status === 'verified') {
        $content .= "
        <div class='success-box'>
            <h3 style='color: #059669; margin: 0 0 15px 0;'>🎉 Congratulations!</h3>
            <p style='margin: 0; color: #065f46;'>Your identity has been successfully verified. You now have full access to all banking services.</p>
        </div>

        <h3>🎯 What You Can Now Do</h3>
        <ul>
            <li>Make unlimited transfers and payments</li>
            <li>Access premium banking features</li>
            <li>Apply for loans and credit products</li>
            <li>Increase your transaction limits</li>
            <li>Use international transfer services</li>
        </ul>";

        $cta_button = [
            'text' => '🚀 Explore All Features',
            'url' => emailUrl('dashboard/index.php'),
            'color' => '#059669'
        ];
    } elseif ($kyc_status === 'rejected') {
        $content .= "
        <div class='danger-box'>
            <h3 style='color: #dc2626; margin: 0 0 15px 0;'>📋 Additional Information Required</h3>
            <p style='margin: 0; color: #991b1b;'>We were unable to verify your identity with the provided documents.</p>
        </div>

        <h3>📄 Next Steps</h3>
        <ol>
            <li>Review the feedback provided below</li>
            <li>Gather the required documents</li>
            <li>Resubmit your verification documents</li>
            <li>Wait for our team to review your submission</li>
        </ol>";

        if ($notes) {
            $content .= "
            <div class='warning-box'>
                <h3 style='color: #d97706; margin: 0 0 15px 0;'>💬 Feedback from Our Team</h3>
                <p style='margin: 0; color: #92400e;'>{$notes}</p>
            </div>";
        }

        $cta_button = [
            'text' => '📄 Resubmit Documents',
            'url' => emailUrl('kyc/upload-documents.php'),
            'color' => '#dc2626'
        ];
    } else {
        $content .= "
        <div class='info-box'>
            <h3 style='color: #1e40af; margin: 0 0 15px 0;'>⏳ Review in Progress</h3>
            <p style='margin: 0; color: #1e3a8a;'>Our team is currently reviewing your submitted documents. We'll notify you once the review is complete.</p>
        </div>

        <h3>📋 What Happens Next</h3>
        <ul>
            <li>Our compliance team will review your documents</li>
            <li>Additional information may be requested if needed</li>
            <li>You'll receive an email once the review is complete</li>
            <li>The process typically takes 1-3 business days</li>
        </ul>";

        $cta_button = [
            'text' => '📊 Check Status',
            'url' => emailUrl('kyc/status.php'),
            'color' => '#3b82f6'
        ];
    }

    return generateMasterEmailTemplate(
        'KYC Status Update',
        'Verification Status Updated',
        $content,
        'This is an important update regarding your account verification status.',
        $cta_button
    );
}

// Document Request Template
function generateDocumentRequestEmailTemplate($user_data, $required_documents, $deadline) {
    $content = "
    <div class='warning-box'>
        <h2 style='color: #d97706; margin: 0 0 15px 0;'>📄 Additional Documents Required</h2>
        <p style='margin: 0; font-size: 16px;'>We need additional documentation to complete your account verification.</p>
    </div>

    <h3>📋 Account Information</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account:</span>
            <span class='detail-value'>{$user_data['account_number']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Holder:</span>
            <span class='detail-value'>{$user_data['first_name']} {$user_data['last_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Request Date:</span>
            <span class='detail-value'>" . date('F j, Y') . "</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Deadline:</span>
            <span class='detail-value' style='color: #d97706; font-weight: 700;'>{$deadline}</span>
        </div>
    </div>

    <h3>📄 Required Documents</h3>
    <ul>";

    foreach ($required_documents as $document) {
        $content .= "<li><strong>{$document['name']}</strong> - {$document['description']}</li>";
    }

    $content .= "
    </ul>

    <h3>📤 How to Submit</h3>
    <ol>
        <li>Log in to your online banking account</li>
        <li>Navigate to the Document Upload section</li>
        <li>Upload clear, high-quality images or PDFs</li>
        <li>Ensure all information is clearly visible</li>
        <li>Submit for review</li>
    </ol>

    <div class='danger-box'>
        <h3 style='color: #dc2626; margin: 0 0 15px 0;'>⚠️ Important Notice</h3>
        <ul style='margin: 0; color: #991b1b;'>
            <li>Documents must be submitted by {$deadline}</li>
            <li>Failure to provide documents may result in account restrictions</li>
            <li>All documents must be current and valid</li>
            <li>Images must be clear and legible</li>
        </ul>
    </div>";

    $cta_button = [
        'text' => '📤 Upload Documents',
        'url' => emailUrl('kyc/upload-documents.php'),
        'color' => '#d97706'
    ];

    return generateMasterEmailTemplate(
        'Documents Required',
        'Additional Documentation Needed',
        $content,
        'Please submit the required documents by the specified deadline to avoid account restrictions.',
        $cta_button
    );
}

// General Notification Template
function generateGeneralNotificationEmailTemplate($user_data, $notification_title, $notification_content, $notification_type = 'info', $cta = null) {
    $type_styles = [
        'info' => ['box' => 'info-box', 'color' => '#1e40af', 'icon' => 'ℹ️'],
        'success' => ['box' => 'success-box', 'color' => '#059669', 'icon' => '✅'],
        'warning' => ['box' => 'warning-box', 'color' => '#d97706', 'icon' => '⚠️'],
        'danger' => ['box' => 'danger-box', 'color' => '#dc2626', 'icon' => '🚨']
    ];

    $style = $type_styles[$notification_type] ?? $type_styles['info'];

    $content = "
    <div class='{$style['box']}'>
        <h2 style='color: {$style['color']}; margin: 0 0 15px 0;'>{$style['icon']} {$notification_title}</h2>
        <p style='margin: 0; font-size: 16px;'>{$notification_content}</p>
    </div>

    <h3>📋 Account Information</h3>
    <div class='account-details'>
        <div class='detail-row'>
            <span class='detail-label'>Account:</span>
            <span class='detail-value'>{$user_data['account_number']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Account Holder:</span>
            <span class='detail-value'>{$user_data['first_name']} {$user_data['last_name']}</span>
        </div>
        <div class='detail-row'>
            <span class='detail-label'>Notification Date:</span>
            <span class='detail-value'>" . date('F j, Y \a\t g:i A') . "</span>
        </div>
    </div>

    <div class='info-box'>
        <h3 style='color: #1e40af; margin: 0 0 15px 0;'>📞 Need Assistance?</h3>
        <p style='margin: 0; color: #1e3a8a;'>If you have any questions about this notification, our support team is here to help.</p>
    </div>";

    $cta_button = $cta;

    return generateMasterEmailTemplate(
        $notification_title,
        $notification_title,
        $content,
        'This is an important notification regarding your account.',
        $cta_button
    );
}

/**
 * Helper Functions
 */

// URL helper function for email templates
function emailUrl($path) {
    $base_url = 'http://localhost/online_banking/';
    return $base_url . ltrim($path, '/');
}

// Send email using template system
function sendTemplateEmail($to, $template_html, $subject) {
    return sendEmailSMTP($to, $subject, $template_html, true);
}
?>
