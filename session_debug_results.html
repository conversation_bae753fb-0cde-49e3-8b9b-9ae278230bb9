<!DOCTYPE html>
<html>
<head>
    <title>Session Debug Information</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
        .debug-info { background: #e8f4fd; padding: 10px; margin: 5px 0; }
        .debug-warning { background: #fff3cd; padding: 10px; margin: 5px 0; }
        .debug-error { background: #f8d7da; padding: 10px; margin: 5px 0; }
        pre { background: #f4f4f4; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Session Debug Information</h1>
    
    <div class="debug-section">
        <h2>Current Time & Session Timeout</h2>
        <div class="debug-info">
            <strong>Current Time:</strong> 2025-08-12 12:17:59<br>
            <strong>Current Timestamp:</strong> 1755001079<br>
            <strong>SESSION_TIMEOUT:</strong> 1800 seconds (30 minutes)<br>
        </div>
    </div>

    <div class="debug-section">
        <h2>Session Status</h2>
        <div class="debug-info">
            <strong>Session ID:</strong> e08d96cf4523d3296eb3f4d3327706b5<br>
            <strong>Session Status:</strong> ACTIVE<br>
            <strong>Session Save Path:</strong> C:\MAMP\bin\php\sessions\<br>
        </div>
    </div>

    <div class="debug-section">
        <h2>Session Variables</h2>
        <pre>Array
(
)
</pre>
    </div>

    <div class="debug-section">
        <h2>Authentication Status</h2>
                <div class="debug-info">
            <strong>isLoggedIn():</strong> FALSE<br>
            <strong>isAdminLoggedIn():</strong> FALSE<br>
        </div>
    </div>

    <div class="debug-section">
        <h2>Session Timeout Check</h2>
        <div class='debug-warning'>No last_activity timestamp found in session.</div>    </div>

    <div class="debug-section">
        <h2>Server Information</h2>
        <div class="debug-info">
            <strong>PHP Version:</strong> 8.3.1<br>
            <strong>Server Time:</strong> 2025-08-12 12:17:59<br>
            <strong>Timezone:</strong> UTC<br>
            <strong>User Agent:</strong> Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.22621.4249<br>
            <strong>Remote IP:</strong> ::1<br>
        </div>
    </div>

    <div class="debug-section">
        <h2>Test Session Update</h2>
        <p>This page access should update the last_activity timestamp.</p>
        <p><a href="debug_session.php">Refresh this page</a> to see updated timestamps.</p>
        <p><a href="transfers.php">Go to Transfers</a> | <a href="transfers/view.php?id=1&type=inter-bank">Go to Transfer View</a></p>
    </div>

    <div class="debug-section">
        <h2>Recommendations</h2>
        <div class='debug-warning'>last_activity is not set in session. This could cause immediate timeouts.</div>    </div>
</body>
</html>