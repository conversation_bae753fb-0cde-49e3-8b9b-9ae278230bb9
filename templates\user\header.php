<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Get site configuration
$site_name = 'PremierBank Pro';
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Get the base URL for proper linking
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . '://' . $host . '/online_banking';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - ' : ''; ?><?php echo htmlspecialchars($site_name); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo $base_url; ?>/assets/favicon.ico">
    
    <!-- Bootstrap CSS - Multiple CDN fallbacks -->
    <link href="https://unpkg.com/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='https://cdn.skypack.dev/bootstrap@5.3.0/dist/css/bootstrap.min.css';">

    <!-- Font Awesome - Multiple CDN fallbacks -->
    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.4.0/css/all.min.css"
          onerror="this.onerror=null;this.href='https://cdn.skypack.dev/@fortawesome/fontawesome-free@6.4.0/css/all.min.css';">

    <!-- Local fallback CSS -->
    <style>
        /* Emergency fallback styles if all CDNs fail */
        .btn { padding: 0.375rem 0.75rem; margin: 0.25rem; border: 1px solid #ccc; background: #f8f9fa; }
        .btn-primary { background: #0d6efd; color: white; border-color: #0d6efd; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        .row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
        .col { flex: 1; padding: 0 15px; }
    </style>

    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo $base_url; ?>/assets/css/user-dashboard.css">
    <link rel="stylesheet" href="<?php echo $base_url; ?>/assets/css/components.css">
    
    <!-- Additional CSS if specified -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link rel="stylesheet" href="<?php echo $base_url; ?>/assets/css/<?php echo $css_file; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Custom page styles -->
    <?php if (isset($custom_styles)): ?>
        <style><?php echo $custom_styles; ?></style>
    <?php endif; ?>
</head>
<body class="user-dashboard">
    <div class="dashboard-wrapper">
