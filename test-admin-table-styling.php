<?php
/**
 * Test Admin Table Styling
 * Verify that the transfer type and status badges are styled correctly
 */

require_once 'config/config.php';

echo "<h1>🎨 Admin Table Styling Test</h1>";

try {
    $db = getDB();
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🎯 CSS Variable System</h2>";
    echo "<p>The admin transfers table now uses the dynamic CSS variable system for consistent styling:</p>";
    echo "<ul>";
    echo "<li><strong>--primary-color:</strong> Main brand color (default: #206bc4)</li>";
    echo "<li><strong>--primary-dark:</strong> Darker variant for contrast</li>";
    echo "<li><strong>--success-color:</strong> Success status color</li>";
    echo "<li><strong>--warning-color:</strong> Warning/pending status color</li>";
    echo "<li><strong>--danger-color:</strong> Error/failed status color</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>✅ Updated Badge Styling</h2>";
    
    echo "<h3>Transfer Type Badges:</h3>";
    echo "<div style='margin: 10px 0;'>";
    echo "<span class='badge' style='background: var(--primary-color); color: white; font-weight: 600; margin-right: 10px;'>Local Bank</span>";
    echo "<span class='badge' style='background: var(--primary-dark); color: white; font-weight: 600;'>Inter-Bank</span>";
    echo "</div>";
    
    echo "<h3>Status Badges:</h3>";
    echo "<div style='margin: 10px 0;'>";
    echo "<span class='badge' style='background: var(--success-color); color: white; font-weight: 600; margin-right: 10px;'>Completed</span>";
    echo "<span class='badge' style='background: var(--warning-color); color: white; font-weight: 600; margin-right: 10px;'>Pending</span>";
    echo "<span class='badge' style='background: var(--danger-color); color: white; font-weight: 600; margin-right: 10px;'>Failed</span>";
    echo "<span class='badge' style='background: var(--primary-color); color: white; font-weight: 600;'>Cancelled</span>";
    echo "</div>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🔧 Styling Changes Made</h2>";
    echo "<ul>";
    echo "<li><strong>✅ Transfer Type Badges:</strong>";
    echo "<ul>";
    echo "<li><strong>Local Bank:</strong> Uses <code>var(--primary-color)</code> background</li>";
    echo "<li><strong>Inter-Bank:</strong> Uses <code>var(--primary-dark)</code> background for distinction</li>";
    echo "<li><strong>Both:</strong> White text with font-weight: 600 for better readability</li>";
    echo "</ul></li>";
    echo "<li><strong>✅ Status Badges:</strong>";
    echo "<ul>";
    echo "<li><strong>Completed:</strong> Uses <code>var(--success-color)</code> background</li>";
    echo "<li><strong>Pending:</strong> Uses <code>var(--warning-color)</code> background</li>";
    echo "<li><strong>Failed:</strong> Uses <code>var(--danger-color)</code> background</li>";
    echo "<li><strong>Cancelled:</strong> Uses <code>var(--primary-color)</code> background</li>";
    echo "<li><strong>All:</strong> White text with font-weight: 600 for consistency</li>";
    echo "</ul></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>📊 Sample Table Preview</h2>";
    echo "<p>Here's how the badges will look in the actual admin transfers table:</p>";
    
    // Get some sample transfers to show the styling
    $sample_sql = "
        (SELECT lt.id, lt.transaction_id, lt.beneficiary_account_name as recipient_name, 
                lt.amount, lt.currency, lt.status, lt.created_at, 'local-bank' as transfer_type,
                u.first_name, u.last_name
         FROM local_transfers lt
         LEFT JOIN accounts u ON lt.sender_id = u.id
         LIMIT 2)
        UNION ALL
        (SELECT it.id, it.transaction_id, CONCAT(r.first_name, ' ', r.last_name) as recipient_name,
                it.amount, it.currency, it.status, it.created_at, 'inter-bank' as transfer_type,
                u.first_name, u.last_name
         FROM interbank_transfers it
         LEFT JOIN accounts u ON it.sender_id = u.id
         LEFT JOIN accounts r ON it.recipient_id = r.id
         LIMIT 2)
        ORDER BY created_at DESC
        LIMIT 4";
    
    $sample_result = $db->query($sample_sql);
    
    if ($sample_result && $sample_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 14px;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th style='padding: 10px;'>ID</th>";
        echo "<th style='padding: 10px;'>Sender</th>";
        echo "<th style='padding: 10px;'>Recipient</th>";
        echo "<th style='padding: 10px;'>Type</th>";
        echo "<th style='padding: 10px;'>Amount</th>";
        echo "<th style='padding: 10px;'>Status</th>";
        echo "<th style='padding: 10px;'>Date</th>";
        echo "</tr>";
        
        while ($row = $sample_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars(($row['first_name'] ?? 'Unknown') . ' ' . ($row['last_name'] ?? 'User')) . "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($row['recipient_name'] ?? 'Unknown') . "</td>";
            echo "<td style='padding: 10px;'>";
            
            // Apply the new styling
            if ($row['transfer_type'] === 'local-bank') {
                echo "<span class='badge' style='background: var(--primary-color); color: white; font-weight: 600;'>Local Bank</span>";
            } else {
                echo "<span class='badge' style='background: var(--primary-dark); color: white; font-weight: 600;'>Inter-Bank</span>";
            }
            
            echo "</td>";
            echo "<td style='padding: 10px;'>" . htmlspecialchars($row['amount']) . " " . htmlspecialchars($row['currency']) . "</td>";
            echo "<td style='padding: 10px;'>";
            
            // Apply the new status styling
            switch ($row['status']) {
                case 'completed':
                    echo "<span class='badge' style='background: var(--success-color); color: white; font-weight: 600;'>Completed</span>";
                    break;
                case 'pending':
                    echo "<span class='badge' style='background: var(--warning-color); color: white; font-weight: 600;'>Pending</span>";
                    break;
                case 'failed':
                    echo "<span class='badge' style='background: var(--danger-color); color: white; font-weight: 600;'>Failed</span>";
                    break;
                case 'cancelled':
                    echo "<span class='badge' style='background: var(--primary-color); color: white; font-weight: 600;'>Cancelled</span>";
                    break;
                default:
                    echo "<span class='badge bg-secondary'>Unknown</span>";
            }
            
            echo "</td>";
            echo "<td style='padding: 10px;'>" . date('M j, Y', strtotime($row['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No transfers found for preview</p>";
    }
    echo "</div>";
    
    echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🔍 Testing Instructions</h2>";
    echo "<ol>";
    echo "<li><strong>Visit the admin transfers page:</strong> <a href='admin/transfers.php' target='_blank' style='color: #007bff; font-weight: bold;'>admin/transfers.php</a></li>";
    echo "<li><strong>Check transfer type badges:</strong> Local Bank should be primary color, Inter-Bank should be darker</li>";
    echo "<li><strong>Check status badges:</strong> All should use the CSS variable colors</li>";
    echo "<li><strong>Verify consistency:</strong> All badges should have white text and bold font weight</li>";
    echo "<li><strong>Test responsiveness:</strong> Badges should look good on different screen sizes</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>⚠️ Important Notes</h2>";
    echo "<ul>";
    echo "<li><strong>Dynamic Colors:</strong> Badge colors will automatically adapt if admin changes the primary color theme</li>";
    echo "<li><strong>Consistent Styling:</strong> All badges now use the same font-weight and text color for uniformity</li>";
    echo "<li><strong>Visual Distinction:</strong> Local Bank and Inter-Bank transfers have different shades for easy identification</li>";
    echo "<li><strong>Status Clarity:</strong> Each status has its own semantic color (success, warning, danger, primary)</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<!-- Include dynamic CSS variables for proper styling -->
<style>
<?php
try {
    require_once 'config/dynamic-css.php';
    echo generateDynamicCSS();
} catch (Exception $e) {
    // Fallback CSS
    echo ":root { 
        --primary-color: #206bc4; 
        --primary-dark: #164a73; 
        --success-color: #10b981; 
        --warning-color: #f59e0b; 
        --danger-color: #ef4444; 
    }";
}
?>

body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

table {
    font-size: 14px;
}

th {
    font-weight: bold;
}

h1 {
    color: #333;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 10px;
}

h2 {
    color: #555;
    margin-top: 0;
}

h3 {
    color: #666;
    margin-bottom: 10px;
}

a {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

ul li {
    margin-bottom: 5px;
}

.badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    display: inline-block;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    color: #e83e8c;
}
</style>
