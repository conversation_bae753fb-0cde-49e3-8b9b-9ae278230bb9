<?php
/**
 * Test Dynamic Edit Page Functionality
 * Verify that the edit page works correctly with both transfer types
 */

require_once 'config/config.php';

echo "<h1>🧪 Dynamic Edit Page Test</h1>";

try {
    $db = getDB();
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>📊 Available Transfers for Testing</h2>";
    
    // Get recent transfers from both tables
    echo "<h3>Local Bank Transfers</h3>";
    $local_sql = "SELECT id, transaction_id, beneficiary_account_name, amount, currency, status, created_at 
                  FROM local_transfers 
                  ORDER BY created_at DESC 
                  LIMIT 5";
    $local_result = $db->query($local_sql);
    
    if ($local_result && $local_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e3f2fd;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Transaction ID</th>";
        echo "<th style='padding: 8px;'>Beneficiary</th>";
        echo "<th style='padding: 8px;'>Amount</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Edit Link</th>";
        echo "</tr>";
        
        while ($row = $local_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['transaction_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['beneficiary_account_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['amount']) . " " . htmlspecialchars($row['currency']) . "</td>";
            echo "<td style='padding: 8px;'><span style='background: " . ($row['status'] === 'completed' ? '#d4edda' : '#fff3cd') . "; padding: 2px 6px; border-radius: 3px;'>" . htmlspecialchars($row['status']) . "</span></td>";
            echo "<td style='padding: 8px;'><a href='admin/transfers/edit.php?id=" . $row['id'] . "&type=local-bank' target='_blank' style='color: #007bff;'>Edit Local Transfer</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No local transfers found</p>";
    }
    
    echo "<h3>Inter-Bank Transfers</h3>";
    $interbank_sql = "SELECT it.id, it.transaction_id, it.amount, it.currency, it.status, it.created_at,
                             CONCAT(r.first_name, ' ', r.last_name) as recipient_name
                      FROM interbank_transfers it
                      LEFT JOIN accounts r ON it.recipient_id = r.id
                      ORDER BY it.created_at DESC 
                      LIMIT 5";
    $interbank_result = $db->query($interbank_sql);
    
    if ($interbank_result && $interbank_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f3e5f5;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Transaction ID</th>";
        echo "<th style='padding: 8px;'>Recipient</th>";
        echo "<th style='padding: 8px;'>Amount</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Edit Link</th>";
        echo "</tr>";
        
        while ($row = $interbank_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['transaction_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['recipient_name'] ?? 'Unknown') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['amount']) . " " . htmlspecialchars($row['currency']) . "</td>";
            echo "<td style='padding: 8px;'><span style='background: " . ($row['status'] === 'completed' ? '#d4edda' : '#fff3cd') . "; padding: 2px 6px; border-radius: 3px;'>" . htmlspecialchars($row['status']) . "</span></td>";
            echo "<td style='padding: 8px;'><a href='admin/transfers/edit.php?id=" . $row['id'] . "&type=inter-bank' target='_blank' style='color: #007bff;'>Edit Inter-Bank Transfer</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No inter-bank transfers found</p>";
    }
    echo "</div>";
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>✅ Dynamic Edit Features</h2>";
    echo "<ul>";
    echo "<li><strong>✅ Transfer Type Detection:</strong> Automatically detects local-bank vs inter-bank from URL parameter</li>";
    echo "<li><strong>✅ Dynamic Field Display:</strong> Shows different fields based on transfer type</li>";
    echo "<li><strong>✅ Database-Driven Forms:</strong> All fields populated from actual database data</li>";
    echo "<li><strong>✅ Smart Field Updates:</strong> Only updates fields that are submitted</li>";
    echo "<li><strong>✅ Type-Specific Validation:</strong> Different validation rules for each transfer type</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🔧 Edit Page Features</h2>";
    
    echo "<h3>Local Bank Transfer Edit Features:</h3>";
    echo "<ul>";
    echo "<li>✅ Editable beneficiary account name</li>";
    echo "<li>✅ Editable beneficiary account number</li>";
    echo "<li>✅ Editable beneficiary bank name</li>";
    echo "<li>✅ Editable routing code</li>";
    echo "<li>✅ Editable account type (checking/savings/business)</li>";
    echo "<li>✅ Editable amount, currency, status, fee</li>";
    echo "<li>✅ Editable narration/description</li>";
    echo "<li>✅ Shows OTP verification status</li>";
    echo "</ul>";
    
    echo "<h3>Inter-Bank Transfer Edit Features:</h3>";
    echo "<ul>";
    echo "<li>✅ Read-only recipient information (linked to accounts)</li>";
    echo "<li>✅ Shows recipient user details with avatar</li>";
    echo "<li>✅ Editable amount, currency, status, fee</li>";
    echo "<li>✅ Editable narration/description</li>";
    echo "<li>✅ Clear indication that recipient cannot be changed</li>";
    echo "</ul>";
    
    echo "<h3>Common Features:</h3>";
    echo "<ul>";
    echo "<li>✅ Read-only transfer ID and transaction reference</li>";
    echo "<li>✅ Sender information display with avatar</li>";
    echo "<li>✅ Transfer type badge</li>";
    echo "<li>✅ Created/updated/completed timestamps</li>";
    echo "<li>✅ Client-side validation</li>";
    echo "<li>✅ Confirmation before saving</li>";
    echo "<li>✅ Proper navigation links with transfer type</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🧪 Testing Instructions</h2>";
    echo "<ol>";
    echo "<li><strong>Click on any 'Edit' link above</strong> to test the dynamic edit page</li>";
    echo "<li><strong>Verify correct fields are shown</strong> based on transfer type</li>";
    echo "<li><strong>Try editing different fields</strong> and saving changes</li>";
    echo "<li><strong>Check that updates are saved</strong> to the correct database table</li>";
    echo "<li><strong>Test validation</strong> by entering invalid data</li>";
    echo "<li><strong>Verify navigation</strong> between edit, view, and list pages</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>⚠️ Important Notes</h2>";
    echo "<ul>";
    echo "<li><strong>Backup Data:</strong> Always backup before testing edit functionality</li>";
    echo "<li><strong>Inter-Bank Recipients:</strong> Cannot change recipient details as they're linked to user accounts</li>";
    echo "<li><strong>Status Changes:</strong> Be careful when changing transfer status as it affects user balances</li>";
    echo "<li><strong>Amount Changes:</strong> Changing amounts may require manual balance adjustments</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test database field mapping
    echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🗃️ Database Field Mapping</h2>";
    
    echo "<h3>Local Transfers Table Fields:</h3>";
    $local_fields_sql = "DESCRIBE local_transfers";
    $local_fields_result = $db->query($local_fields_sql);
    
    if ($local_fields_result) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 12px;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th style='padding: 5px;'>Field</th>";
        echo "<th style='padding: 5px;'>Type</th>";
        echo "<th style='padding: 5px;'>Null</th>";
        echo "<th style='padding: 5px;'>Key</th>";
        echo "<th style='padding: 5px;'>Default</th>";
        echo "<th style='padding: 5px;'>Editable</th>";
        echo "</tr>";
        
        while ($field = $local_fields_result->fetch_assoc()) {
            $editable = in_array($field['Field'], [
                'amount', 'currency', 'status', 'transfer_fee', 'narration',
                'beneficiary_account_name', 'beneficiary_account_number', 
                'beneficiary_bank_name', 'routing_code', 'account_type'
            ]) ? 'Yes' : 'No';
            
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Field']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Type']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Null']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Key']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Default'] ?? 'NULL') . "</td>";
            echo "<td style='padding: 5px; background: " . ($editable === 'Yes' ? '#d4edda' : '#f8d7da') . ";'>" . $editable . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>Inter-Bank Transfers Table Fields:</h3>";
    $interbank_fields_sql = "DESCRIBE interbank_transfers";
    $interbank_fields_result = $db->query($interbank_fields_sql);
    
    if ($interbank_fields_result) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 12px;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th style='padding: 5px;'>Field</th>";
        echo "<th style='padding: 5px;'>Type</th>";
        echo "<th style='padding: 5px;'>Null</th>";
        echo "<th style='padding: 5px;'>Key</th>";
        echo "<th style='padding: 5px;'>Default</th>";
        echo "<th style='padding: 5px;'>Editable</th>";
        echo "</tr>";
        
        while ($field = $interbank_fields_result->fetch_assoc()) {
            $editable = in_array($field['Field'], [
                'amount', 'currency', 'status', 'transfer_fee', 'narration'
            ]) ? 'Yes' : 'No';
            
            echo "<tr>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Field']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Type']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Null']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Key']) . "</td>";
            echo "<td style='padding: 5px;'>" . htmlspecialchars($field['Default'] ?? 'NULL') . "</td>";
            echo "<td style='padding: 5px; background: " . ($editable === 'Yes' ? '#d4edda' : '#f8d7da') . ";'>" . $editable . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

table {
    font-size: 14px;
}

th {
    font-weight: bold;
}

h1 {
    color: #333;
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

h2 {
    color: #555;
    margin-top: 0;
}

h3 {
    color: #666;
    margin-bottom: 10px;
}

a {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
