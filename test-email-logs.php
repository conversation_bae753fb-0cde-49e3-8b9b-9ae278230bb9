<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "<h2>Email Logs Database Analysis</h2>";
    
    // Check if email_logs table exists
    $table_check = $db->query("SHOW TABLES LIKE 'email_logs'");
    if ($table_check->num_rows == 0) {
        echo "<p style='color: red;'>ERROR: email_logs table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ email_logs table exists</p>";
    
    // Get table structure
    echo "<h3>Table Structure:</h3>";
    $structure = $db->query("DESCRIBE email_logs");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Get total count
    $total_result = $db->query("SELECT COUNT(*) as total FROM email_logs");
    $total = $total_result->fetch_assoc()['total'];
    echo "<h3>Total Records: {$total}</h3>";
    
    if ($total > 0) {
        // Get count by email type
        echo "<h3>Email Types Distribution:</h3>";
        $types_result = $db->query("SELECT email_type, COUNT(*) as count FROM email_logs GROUP BY email_type ORDER BY count DESC");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Email Type</th><th>Count</th></tr>";
        while ($row = $types_result->fetch_assoc()) {
            echo "<tr><td>{$row['email_type']}</td><td>{$row['count']}</td></tr>";
        }
        echo "</table>";
        
        // Get count by status
        echo "<h3>Status Distribution:</h3>";
        $status_result = $db->query("SELECT status, COUNT(*) as count FROM email_logs GROUP BY status ORDER BY count DESC");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Status</th><th>Count</th></tr>";
        while ($row = $status_result->fetch_assoc()) {
            echo "<tr><td>{$row['status']}</td><td>{$row['count']}</td></tr>";
        }
        echo "</table>";
        
        // Show recent OTP emails
        echo "<h3>Recent OTP Emails (Last 10):</h3>";
        $otp_result = $db->query("SELECT * FROM email_logs WHERE email_type = 'otp' ORDER BY sent_at DESC LIMIT 10");
        if ($otp_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Recipient</th><th>Subject</th><th>Status</th><th>Sent At</th></tr>";
            while ($row = $otp_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['recipient_email']}</td>";
                echo "<td>{$row['subject']}</td>";
                echo "<td>{$row['status']}</td>";
                echo "<td>{$row['sent_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>No OTP emails found in the database.</p>";
        }
        
        // Show recent emails of all types
        echo "<h3>Recent Emails (Last 10):</h3>";
        $recent_result = $db->query("SELECT * FROM email_logs ORDER BY sent_at DESC LIMIT 10");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Type</th><th>Recipient</th><th>Subject</th><th>Status</th><th>Sent At</th></tr>";
        while ($row = $recent_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['email_type']}</td>";
            echo "<td>{$row['recipient_email']}</td>";
            echo "<td>{$row['subject']}</td>";
            echo "<td>{$row['status']}</td>";
            echo "<td>{$row['sent_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>No email records found in the database.</p>";
        echo "<p>This could mean:</p>";
        echo "<ul>";
        echo "<li>No emails have been sent yet</li>";
        echo "<li>Email logging is not properly configured</li>";
        echo "<li>The email system is not working</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>