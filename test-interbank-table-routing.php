<?php
/**
 * Test Inter-Bank Transfer Table Routing
 * Verify that inter-bank transfers go to interbank_transfers table
 */

require_once 'config/config.php';

echo "<h1>🧪 Inter-Bank Transfer Table Routing Test</h1>";

try {
    $db = getDB();
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>📊 Database Table Status</h2>";
    
    // Check if tables exist
    $tables_to_check = ['local_transfers', 'interbank_transfers', 'transfers'];
    
    foreach ($tables_to_check as $table) {
        $check_sql = "SHOW TABLES LIKE '$table'";
        $result = $db->query($check_sql);
        
        if ($result && $result->num_rows > 0) {
            echo "<p>✅ <strong>$table</strong> table exists</p>";
            
            // Get record count
            $count_sql = "SELECT COUNT(*) as count FROM $table";
            $count_result = $db->query($count_sql);
            $count = $count_result->fetch_assoc()['count'];
            echo "<p>&nbsp;&nbsp;&nbsp;📈 Records: $count</p>";
            
        } else {
            echo "<p>❌ <strong>$table</strong> table missing</p>";
        }
    }
    echo "</div>";
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>🔍 Recent Transfer Records</h2>";
    
    // Show recent local transfers
    echo "<h3>Local Bank Transfers (local_transfers table)</h3>";
    $local_sql = "SELECT transaction_id, sender_id, beneficiary_account_name, amount, currency, status, created_at 
                  FROM local_transfers 
                  ORDER BY created_at DESC 
                  LIMIT 5";
    $local_result = $db->query($local_sql);
    
    if ($local_result && $local_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th style='padding: 8px;'>Transaction ID</th>";
        echo "<th style='padding: 8px;'>Sender ID</th>";
        echo "<th style='padding: 8px;'>Recipient</th>";
        echo "<th style='padding: 8px;'>Amount</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";
        
        while ($row = $local_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['transaction_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['sender_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['beneficiary_account_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['amount']) . " " . htmlspecialchars($row['currency']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['status']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No local transfers found</p>";
    }
    
    // Show recent inter-bank transfers
    echo "<h3>Inter-Bank Transfers (interbank_transfers table)</h3>";
    $interbank_sql = "SELECT it.transaction_id, it.sender_id, it.recipient_id, it.amount, it.currency, it.status, it.created_at,
                             CONCAT(r.first_name, ' ', r.last_name) as recipient_name
                      FROM interbank_transfers it
                      LEFT JOIN accounts r ON it.recipient_id = r.id
                      ORDER BY it.created_at DESC 
                      LIMIT 5";
    $interbank_result = $db->query($interbank_sql);
    
    if ($interbank_result && $interbank_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th style='padding: 8px;'>Transaction ID</th>";
        echo "<th style='padding: 8px;'>Sender ID</th>";
        echo "<th style='padding: 8px;'>Recipient ID</th>";
        echo "<th style='padding: 8px;'>Recipient Name</th>";
        echo "<th style='padding: 8px;'>Amount</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Created</th>";
        echo "</tr>";
        
        while ($row = $interbank_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['transaction_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['sender_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['recipient_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['recipient_name'] ?? 'Unknown') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['amount']) . " " . htmlspecialchars($row['currency']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['status']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No inter-bank transfers found</p>";
    }
    echo "</div>";
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>✅ Admin Transfers Page Query Test</h2>";
    
    // Test the UNION query from admin/transfers.php
    $union_sql = "
        (SELECT lt.id, lt.transaction_id, lt.sender_id, NULL as recipient_id,
                lt.beneficiary_account_name as recipient_name, lt.beneficiary_account_number as recipient_account,
                lt.amount, lt.currency, lt.transfer_fee, lt.narration, lt.status, lt.created_at,
                'local-bank' as transfer_type,
                u.first_name, u.last_name, u.email, u.account_number as sender_account,
                NULL as recipient_first_name, NULL as recipient_last_name
         FROM local_transfers lt
         LEFT JOIN accounts u ON lt.sender_id = u.id)
        UNION ALL
        (SELECT it.id, it.transaction_id, it.sender_id, it.recipient_id,
                CONCAT(r.first_name, ' ', r.last_name) as recipient_name, r.account_number as recipient_account,
                it.amount, it.currency, it.transfer_fee, it.narration, it.status, it.created_at,
                'inter-bank' as transfer_type,
                u.first_name, u.last_name, u.email, u.account_number as sender_account,
                r.first_name as recipient_first_name, r.last_name as recipient_last_name
         FROM interbank_transfers it
         LEFT JOIN accounts u ON it.sender_id = u.id
         LEFT JOIN accounts r ON it.recipient_id = r.id)
        ORDER BY created_at DESC
        LIMIT 10";
    
    $union_result = $db->query($union_sql);
    
    if ($union_result && $union_result->num_rows > 0) {
        echo "<p>✅ <strong>UNION query successful!</strong> Found " . $union_result->num_rows . " combined transfers</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Transaction ID</th>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Sender</th>";
        echo "<th style='padding: 8px;'>Recipient</th>";
        echo "<th style='padding: 8px;'>Amount</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        while ($row = $union_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['transaction_id']) . "</td>";
            echo "<td style='padding: 8px;'><span style='background: " . ($row['transfer_type'] === 'local-bank' ? '#e3f2fd' : '#f3e5f5') . "; padding: 2px 6px; border-radius: 3px;'>" . htmlspecialchars($row['transfer_type']) . "</span></td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['recipient_name'] ?? 'Unknown') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['amount']) . " " . htmlspecialchars($row['currency']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ UNION query failed or no results</p>";
    }
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>📋 Implementation Summary</h2>";
    echo "<ul>";
    echo "<li>✅ <strong>Local Bank Transfers</strong> → <code>local_transfers</code> table</li>";
    echo "<li>✅ <strong>Inter-Bank Transfers</strong> → <code>interbank_transfers</code> table</li>";
    echo "<li>✅ <strong>Wire Transfers</strong> → <code>transfers</code> table (unchanged)</li>";
    echo "<li>✅ <strong>Admin Page</strong> → UNION query from both local_transfers and interbank_transfers</li>";
    echo "<li>✅ <strong>Edit/View Pages</strong> → Updated to handle both table types</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

table {
    font-size: 14px;
}

th {
    font-weight: bold;
}

h1 {
    color: #333;
    border-bottom: 3px solid #007bff;
    padding-bottom: 10px;
}

h2 {
    color: #555;
    margin-top: 0;
}

h3 {
    color: #666;
    margin-bottom: 10px;
}
</style>
