# Test Files Organization Summary

This document tracks the test and utility files that were moved from the root directory and subdirectories to the centralized `/test` folder structure.

## Files Moved from Root Directory

### Database Tools → test/database/
- `add_virtual_card_balance.php` → `test/database/add_virtual_card_balance.php`
- `analyze_database.php` → `test/database/analyze_database.php`
- `apply_billing_schema.php` → `test/database/apply_billing_schema.php`
- `apply_schema.php` → `test/database/apply_schema.php`
- `database_cli.php` → `test/database/database_cli.php`
- `database_terminal.php` → `test/database/database_terminal.php`
- `update_document_paths.php` → `test/database/update_document_paths.php`
- `update_otp_table.php` → `test/database/update_otp_table.php`

### Setup/Installation Tools → test/setup/
- `create-admin-virtual-card.php` → `test/setup/create-admin-virtual-card.php`
- `create-wire-beneficiaries.php` → `test/setup/create-wire-beneficiaries.php`
- `fix_beneficiaries_ownership.php` → `test/setup/fix_beneficiaries_ownership.php`
- `fix_passwords.php` → `test/setup/fix_passwords.php`
- `install_phpmailer.php` → `test/setup/install_phpmailer.php`
- `mysql_connect.bat` → `test/setup/mysql_connect.bat`
- `mysql_connect.sh` → `test/setup/mysql_connect.sh`

### Debug Tools → test/debug/
- `debug-virtual-cards.php` → Already existed in test/debug/
- `get_latest_otp.php` → Already existed in test/debug/
- `get_otp.php` → Already existed in test/debug/

### Admin Debug → test/admin/
- `admin/debug-test.php` → `test/admin/debug-test.php`

### User Debug → test/debug/
- `dashboard/test-dashboard.php` → `test/debug/test-dashboard.php`
- `user/test-layout.php` → `test/debug/test-layout.php`

## Files Moved from database_tools/ Directory

All files from the `database_tools/` directory were moved to appropriate test subdirectories:

### Database Tools → test/database/
- `database_tools/database_cli.php` → `test/database/database_cli.php`
- `database_tools/database_terminal.php` → `test/database/database_terminal.php`
- `database_tools/sql_export_generator.php` → `test/database/sql_export_generator.php`
- `database_tools/sql_export_interface.php` → `test/database/sql_export_interface.php`
- `database_tools/update_otp_table.php` → `test/database/update_otp_table.php`

### Setup Tools → test/setup/
- `database_tools/mysql_connect.bat` → `test/setup/mysql_connect.bat`
- `database_tools/mysql_connect.sh` → `test/setup/mysql_connect.sh`

### Duplicates Removed
- `database_tools/analyze_database.php` (duplicate of root file)
- `database_tools/apply_billing_schema.php` (duplicate of root file)
- `database_tools/apply_schema.php` (duplicate of root file)
- `database_tools/fix_passwords.php` (duplicate of root file)

## Directory Structure After Organization

```
test/
├── admin/
│   └── debug-test.php
├── database/
│   ├── add_virtual_card_balance.php
│   ├── analyze_database.php
│   ├── apply_billing_schema.php
│   ├── apply_schema.php
│   ├── database_cli.php
│   ├── database_terminal.php
│   ├── sql_export_generator.php
│   ├── sql_export_interface.php
│   ├── update_document_paths.php
│   └── update_otp_table.php
├── debug/
│   ├── debug-virtual-cards.php (existing)
│   ├── get_latest_otp.php (existing)
│   ├── get_otp.php (existing)
│   ├── test-dashboard.php
│   └── test-layout.php
├── flow/
│   ├── admin_flow_smoke.php
│   ├── link_verifier.php
│   └── user_flow_smoke.php
├── setup/
│   ├── create-admin-virtual-card.php
│   ├── create-wire-beneficiaries.php
│   ├── fix_beneficiaries_ownership.php
│   ├── fix_passwords.php
│   ├── install_phpmailer.php
│   ├── mysql_connect.bat
│   └── mysql_connect.sh
├── smoke/
│   └── environment_smoke.php
├── MOVED_FILES_SUMMARY.md
├── README.md
└── run_all_smoke.php
```

## Directories Removed

- `database_tools/` - All files moved to appropriate test subdirectories

## Path Updates

All moved files had their include paths updated to work from their new locations:
- `require_once 'config/config.php'` → `require_once '../../config/config.php'`
- `require_once 'config/database.php'` → `require_once '../../config/database.php'`

## Benefits of This Organization

1. **Centralized Testing**: All test, debug, and utility files are now under `/test`
2. **Clear Structure**: Files are organized by purpose (database, setup, debug, etc.)
3. **No Root Clutter**: Repository root is clean of test files
4. **Easy Discovery**: Related tools are grouped together
5. **Consistent Paths**: All test files use consistent relative paths

## Next Steps

- Run the smoke tests to verify all moved files work correctly
- Update any documentation that references old file locations
- Consider adding more comprehensive tests to the organized structure
