# Test Suite Instructions

You can run the smoke tests via web browser (recommended on MAMP/XAMPP) or CLI.

## Browser

1. Start your local web server (MAMP) and ensure the site is served at:
   http://localhost/online_banking
2. Open this URL to run all smoke tests:
   http://localhost/online_banking/test/run_all_smoke.php

## CLI (if PHP is available on PATH)

```
php test/smoke/environment_smoke.php
php test/flow/admin_flow_smoke.php
php test/flow/user_flow_smoke.php
php test/flow/link_verifier.php
php test/run_all_smoke.php
```

## Files

- test/smoke/environment_smoke.php — environment checks (PHP, autoload, config, DB)
- test/flow/admin_flow_smoke.php — admin key pages existence + syntax
- test/flow/user_flow_smoke.php — user key pages existence + syntax
- test/flow/link_verifier.php — extracts links from nav includes and checks target files exist
- test/run_all_smoke.php — runner that executes all above tests and aggregates results

