<?php
/**
 * Admin Dashboard Debug Test Page
 * This page will help identify what's causing the blank admin dashboard
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<!DOCTYPE html><html><head><title>Admin Debug Test</title></head><body>";
echo "<h1>🔍 Admin Dashboard Debug Test</h1>";

// Test 1: Basic PHP functionality
echo "<h2>1. Basic PHP Test</h2>";
echo "✅ PHP is working - Version: " . PHP_VERSION . "<br>";
echo "✅ Current time: " . date('Y-m-d H:i:s') . "<br>";

// Test 2: File path resolution
echo "<h2>2. File Path Test</h2>";
echo "Current file: " . __FILE__ . "<br>";
echo "Current directory: " . __DIR__ . "<br>";
echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

// Test 3: Config file inclusion
echo "<h2>3. Config File Test</h2>";
try {
    require_once '../../config/config.php';
    echo "✅ Config file loaded successfully<br>";
    echo "✅ APP_NAME: " . (defined('APP_NAME') ? APP_NAME : 'Not defined') . "<br>";
    echo "✅ BASE_URL: " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "<br>";
} catch (Exception $e) {
    echo "❌ Config file error: " . $e->getMessage() . "<br>";
}

// Test 4: Database connection
echo "<h2>4. Database Connection Test</h2>";
try {
    $db = getDB();
    echo "✅ Database connection successful<br>";
    
    // Test a simple query
    $result = $db->query("SELECT COUNT(*) as count FROM accounts");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "✅ Database query successful - Found " . $row['count'] . " accounts<br>";
    } else {
        echo "❌ Database query failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 5: Session functionality
echo "<h2>5. Session Test</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
echo "✅ Session started - ID: " . session_id() . "<br>";

// Check if user is logged in
if (isset($_SESSION['user_id'])) {
    echo "✅ User logged in - ID: " . $_SESSION['user_id'] . "<br>";
    echo "✅ Username: " . ($_SESSION['username'] ?? 'Not set') . "<br>";
    echo "✅ Is Admin: " . ($_SESSION['is_admin'] ?? 'Not set') . "<br>";
} else {
    echo "⚠️ No user session found<br>";
    echo "<a href='../../admin/login.php'>Login as Admin</a><br>";
}

// Test 6: Admin functions
echo "<h2>6. Admin Function Test</h2>";
if (function_exists('isAdmin')) {
    echo "✅ isAdmin() function exists<br>";
    if (isAdmin()) {
        echo "✅ Current user is admin<br>";
    } else {
        echo "⚠️ Current user is not admin<br>";
    }
} else {
    echo "❌ isAdmin() function not found<br>";
}

if (function_exists('requireAdmin')) {
    echo "✅ requireAdmin() function exists<br>";
} else {
    echo "❌ requireAdmin() function not found<br>";
}

// Test 7: Include files check
echo "<h2>7. Include Files Check</h2>";
$include_files = [
    '../../admin/includes/admin-header.php',
    '../../admin/includes/admin-sidebar.php',
    '../../admin/includes/admin-footer.php'
];

foreach ($include_files as $file) {
    if (file_exists($file)) {
        echo "✅ Found: " . basename($file) . "<br>";
    } else {
        echo "❌ Missing: " . basename($file) . "<br>";
    }
}

// Test 8: Memory and execution
echo "<h2>8. System Resources</h2>";
echo "Memory limit: " . ini_get('memory_limit') . "<br>";
echo "Max execution time: " . ini_get('max_execution_time') . "<br>";
echo "Current memory usage: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB<br>";

echo "<h2>9. Next Steps</h2>";
echo "<p>If all tests above pass, try accessing:</p>";
echo "<ul>";
echo "<li><a href='../../admin/'>Admin Dashboard</a></li>";
echo "<li><a href='../../admin/users.php'>Admin Users Page</a></li>";
echo "<li><a href='../../admin/login.php'>Admin Login</a></li>";
echo "</ul>";

echo "</body></html>";
?>
