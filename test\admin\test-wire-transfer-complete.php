<?php
/**
 * Complete Wire Transfer Edit Page Test
 * Comprehensive test for both design and functionality
 */

// Include necessary files
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Start session for testing
session_start();

// Set up test admin session (for testing purposes)
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'test_admin';
$_SESSION['admin_role'] = 'super_admin';

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Complete Wire Transfer Edit Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h1 class='mb-4'><i class='fas fa-clipboard-check text-success me-2'></i>Complete Wire Transfer Edit Test</h1>";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

// Get test data first
try {
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_error) {
        throw new Exception("Connection failed: " . $db->connect_error);
    }
    
    // Get a test wire transfer
    $test_id = 49;
    $query = "SELECT * FROM wire_transfers WHERE id = ? LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bind_param("i", $test_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $query = "SELECT * FROM wire_transfers ORDER BY id DESC LIMIT 1";
        $result = $db->query($query);
    }
    
    if ($result && $result->num_rows > 0) {
        $transfer = $result->fetch_assoc();
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Database connection failed: " . $e->getMessage() . "</div>";
}

// Test Categories
$test_categories = [
    'design' => ['name' => 'Design & Structure', 'tests' => [], 'icon' => 'fas fa-paint-brush'],
    'functionality' => ['name' => 'Form Functionality', 'tests' => [], 'icon' => 'fas fa-cogs'],
    'integration' => ['name' => 'Integration & Navigation', 'tests' => [], 'icon' => 'fas fa-link']
];

// DESIGN TESTS
$total_tests++;
$design_test_passed = true;
$design_issues = [];

// Check if edit.php exists
if (!file_exists('../../admin/wire-transfers/edit.php')) {
    $design_issues[] = "Edit.php file not found";
    $design_test_passed = false;
}

// Check if view.php exists for comparison
if (!file_exists('../../admin/wire-transfers/view.php')) {
    $design_issues[] = "View.php file not found for comparison";
    $design_test_passed = false;
}

if ($design_test_passed) {
    $edit_content = file_get_contents('../../admin/wire-transfers/edit.php');
    
    // Check for key design elements that should match view.php
    $required_elements = [
        'Transfer Details' => 'Transfer Details',
        'Sender Information' => 'Sender Information', 
        'Recipient Information' => 'Recipient Information',
        'Timeline Information' => 'Timeline Information',
        'Dynamic Wire Transfer Fields' => 'Dynamic Wire Transfer Fields',
        'Action Buttons' => 'Action Buttons',
        'card shadow-sm' => 'Bootstrap card styling',
        'btn btn-primary' => 'Primary button styling',
        'form-control' => 'Form control styling'
    ];
    
    foreach ($required_elements as $element => $description) {
        if (strpos($edit_content, $element) === false) {
            $design_issues[] = "Missing: $description";
            $design_test_passed = false;
        }
    }
}

if ($design_test_passed) {
    $test_categories['design']['tests'][] = "PASS: All design elements present";
    $passed_tests++;
} else {
    $test_categories['design']['tests'][] = "FAIL: Design issues found - " . implode(', ', $design_issues);
}

// FUNCTIONALITY TESTS
if (isset($transfer)) {
    // Test 1: Database Update Functionality
    $total_tests++;
    try {
        $test_description = "AUTOMATED TEST - " . date('Y-m-d H:i:s');
        $update_query = "UPDATE wire_transfers SET description = ?, updated_at = NOW() WHERE id = ?";
        $stmt = $db->prepare($update_query);
        $stmt->bind_param("si", $test_description, $transfer['id']);
        
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            $test_categories['functionality']['tests'][] = "PASS: Database update successful";
            $passed_tests++;
        } else {
            $test_categories['functionality']['tests'][] = "FAIL: Database update failed";
        }
    } catch (Exception $e) {
        $test_categories['functionality']['tests'][] = "FAIL: Database update error - " . $e->getMessage();
    }
    
    // Test 2: Data Validation
    $total_tests++;
    try {
        $verify_query = "SELECT * FROM wire_transfers WHERE id = ?";
        $stmt = $db->prepare($verify_query);
        $stmt->bind_param("i", $transfer['id']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $updated_transfer = $result->fetch_assoc();
            if ($updated_transfer['description'] === $test_description) {
                $test_categories['functionality']['tests'][] = "PASS: Data validation successful";
                $passed_tests++;
            } else {
                $test_categories['functionality']['tests'][] = "FAIL: Data validation failed";
            }
        } else {
            $test_categories['functionality']['tests'][] = "FAIL: Could not verify updated data";
        }
    } catch (Exception $e) {
        $test_categories['functionality']['tests'][] = "FAIL: Data validation error - " . $e->getMessage();
    }
}

// INTEGRATION TESTS
$total_tests++;
$edit_file_accessible = file_exists('../../admin/wire-transfers/edit.php') && is_readable('../../admin/wire-transfers/edit.php');
$view_file_accessible = file_exists('../../admin/wire-transfers/view.php') && is_readable('../../admin/wire-transfers/view.php');

if ($edit_file_accessible && $view_file_accessible) {
    $test_categories['integration']['tests'][] = "PASS: Both edit and view pages accessible";
    $passed_tests++;
} else {
    $test_categories['integration']['tests'][] = "FAIL: Page accessibility issues";
}

// Display Results by Category
foreach ($test_categories as $category_key => $category) {
    echo "<div class='card mb-4'>";
    echo "<div class='card-header'>";
    echo "<h5 class='mb-0'><i class='{$category['icon']} me-2'></i>{$category['name']}</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    if (empty($category['tests'])) {
        echo "<div class='alert alert-info'>No tests in this category</div>";
    } else {
        foreach ($category['tests'] as $test_result) {
            $class = strpos($test_result, 'PASS') === 0 ? 'success' : 'danger';
            $icon = strpos($test_result, 'PASS') === 0 ? 'check' : 'times';
            echo "<div class='alert alert-$class'>";
            echo "<i class='fas fa-$icon me-2'></i>$test_result";
            echo "</div>";
        }
    }
    
    echo "</div></div>";
}

// Overall Summary
$success_rate = $total_tests > 0 ? ($passed_tests / $total_tests) * 100 : 0;

echo "<div class='card border-primary'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'><i class='fas fa-trophy me-2'></i>Final Test Results</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-8'>";
echo "<h6>Overall Success Rate:</h6>";
echo "<div class='progress mb-3' style='height: 30px;'>";
echo "<div class='progress-bar bg-" . ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'danger')) . "' style='width: {$success_rate}%'>";
echo "<strong>" . round($success_rate, 1) . "%</strong>";
echo "</div>";
echo "</div>";

if ($success_rate >= 80) {
    echo "<div class='alert alert-success'>";
    echo "<i class='fas fa-check-circle me-2'></i><strong>Excellent!</strong> The wire transfer edit page redesign is successful and fully functional.";
    echo "</div>";
} elseif ($success_rate >= 60) {
    echo "<div class='alert alert-warning'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i><strong>Good!</strong> The redesign is mostly successful with minor issues.";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-times-circle me-2'></i><strong>Issues Found!</strong> The redesign needs attention.";
    echo "</div>";
}

echo "</div>";

echo "<div class='col-md-4'>";
echo "<div class='card bg-light'>";
echo "<div class='card-body text-center'>";
echo "<h2 class='text-primary'>$passed_tests/$total_tests</h2>";
echo "<p class='mb-0'>Tests Passed</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

// Task Completion Checklist
echo "<div class='mt-4'>";
echo "<h6>Task Completion Checklist:</h6>";
echo "<div class='row'>";

$checklist_items = [
    'Design Analysis' => true,
    'View Page Study' => true,
    'Edit Page Redesign' => true,
    'Visual Styling Match' => $design_test_passed,
    'Form Functionality' => isset($transfer) && $passed_tests >= ($total_tests * 0.6),
    'Testing Complete' => true
];

foreach ($checklist_items as $item => $completed) {
    echo "<div class='col-md-6 mb-2'>";
    echo "<div class='d-flex align-items-center'>";
    if ($completed) {
        echo "<i class='fas fa-check-square text-success me-2'></i>";
        echo "<span class='text-success'>$item</span>";
    } else {
        echo "<i class='fas fa-square text-muted me-2'></i>";
        echo "<span class='text-muted'>$item</span>";
    }
    echo "</div>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

echo "</div></div>";

// Quick Access Links
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h5>Quick Access</h5></div>";
echo "<div class='card-body'>";
echo "<div class='d-flex flex-wrap gap-2'>";

if (isset($transfer)) {
    echo "<a href='../../admin/wire-transfers/edit.php?id=" . $transfer['id'] . "' class='btn btn-primary' target='_blank'>";
    echo "<i class='fas fa-edit me-2'></i>Test Edit Page";
    echo "</a>";
    
    echo "<a href='../../admin/wire-transfers/view.php?id=" . $transfer['id'] . "' class='btn btn-outline-primary' target='_blank'>";
    echo "<i class='fas fa-eye me-2'></i>Compare View Page";
    echo "</a>";
}

echo "<a href='test-wire-transfer-edit.php' class='btn btn-outline-secondary'>";
echo "<i class='fas fa-vial me-2'></i>Structure Test";
echo "</a>";

echo "<a href='test-wire-transfer-edit-submission.php' class='btn btn-outline-info'>";
echo "<i class='fas fa-paper-plane me-2'></i>Submission Test";
echo "</a>";

echo "</div>";
echo "</div></div>";

echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";

// Close database connection
if (isset($db)) {
    $db->close();
}
?>