<?php
/**
 * Test Wire Transfer Edit Form Submission
 * Tests the actual form submission functionality of the redesigned edit.php page
 */

// Include necessary files
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Start session for testing
session_start();

// Set up test admin session (for testing purposes)
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'test_admin';
$_SESSION['admin_role'] = 'super_admin';

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Wire Transfer Edit Submission Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h1 class='mb-4'><i class='fas fa-paper-plane text-primary me-2'></i>Wire Transfer Edit Submission Test</h1>";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

// Test 1: Get a test wire transfer
$total_tests++;
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Test 1: Get Test Wire Transfer Data</h5></div>";
echo "<div class='card-body'>";

try {
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_error) {
        throw new Exception("Connection failed: " . $db->connect_error);
    }
    
    // Get a test wire transfer
    $test_id = 49; // Try the specific ID first
    $query = "SELECT * FROM wire_transfers WHERE id = ? LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bind_param("i", $test_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // If ID 49 doesn't exist, get any wire transfer
        $query = "SELECT * FROM wire_transfers ORDER BY id DESC LIMIT 1";
        $result = $db->query($query);
    }
    
    if ($result && $result->num_rows > 0) {
        $transfer = $result->fetch_assoc();
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ Test wire transfer found (ID: " . $transfer['id'] . ")</div>";
        echo "<div class='alert alert-info'>";
        echo "<strong>Original Data:</strong><br>";
        echo "Amount: " . number_format($transfer['amount'], 2) . " " . $transfer['currency'] . "<br>";
        echo "Status: " . $transfer['status'] . "<br>";
        echo "Recipient: " . htmlspecialchars($transfer['recipient_name']) . "<br>";
        echo "Description: " . htmlspecialchars($transfer['description'] ?? 'N/A');
        echo "</div>";
        $test_results[] = "PASS: Test wire transfer data retrieved";
        $passed_tests++;
    } else {
        throw new Exception("No wire transfers found in database");
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ Error: " . $e->getMessage() . "</div>";
    $test_results[] = "FAIL: Could not retrieve test data";
}
echo "</div></div>";

// Test 2: Simulate form submission (if we have test data)
if (isset($transfer)) {
    $total_tests++;
    echo "<div class='card mb-3'>";
    echo "<div class='card-header'><h5>Test 2: Simulate Form Submission</h5></div>";
    echo "<div class='card-body'>";
    
    try {
        // Prepare test data for update
        $test_description = "TEST UPDATE - " . date('Y-m-d H:i:s');
        $test_admin_notes = "Test admin notes updated via automated test";
        
        // Simulate the update query that edit.php would perform
        $update_query = "UPDATE wire_transfers SET 
                        description = ?, 
                        admin_notes = ?, 
                        updated_at = NOW() 
                        WHERE id = ?";
        
        $stmt = $db->prepare($update_query);
        $stmt->bind_param("ssi", $test_description, $test_admin_notes, $transfer['id']);
        
        if ($stmt->execute()) {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ Form submission simulation successful</div>";
            echo "<div class='alert alert-info'>";
            echo "<strong>Updated Data:</strong><br>";
            echo "Description: " . htmlspecialchars($test_description) . "<br>";
            echo "Admin Notes: " . htmlspecialchars($test_admin_notes) . "<br>";
            echo "Rows affected: " . $stmt->affected_rows;
            echo "</div>";
            $test_results[] = "PASS: Form submission simulation successful";
            $passed_tests++;
        } else {
            throw new Exception("Update query failed: " . $stmt->error);
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ Error: " . $e->getMessage() . "</div>";
        $test_results[] = "FAIL: Form submission simulation failed";
    }
    echo "</div></div>";
    
    // Test 3: Verify the update was successful
    $total_tests++;
    echo "<div class='card mb-3'>";
    echo "<div class='card-header'><h5>Test 3: Verify Update Success</h5></div>";
    echo "<div class='card-body'>";
    
    try {
        // Retrieve the updated record
        $verify_query = "SELECT * FROM wire_transfers WHERE id = ?";
        $stmt = $db->prepare($verify_query);
        $stmt->bind_param("i", $transfer['id']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $updated_transfer = $result->fetch_assoc();
            
            if ($updated_transfer['description'] === $test_description && 
                $updated_transfer['admin_notes'] === $test_admin_notes) {
                echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ Update verification successful</div>";
                echo "<div class='alert alert-info'>";
                echo "<strong>Verified Updated Data:</strong><br>";
                echo "Description: " . htmlspecialchars($updated_transfer['description']) . "<br>";
                echo "Admin Notes: " . htmlspecialchars($updated_transfer['admin_notes']) . "<br>";
                echo "Updated At: " . $updated_transfer['updated_at'];
                echo "</div>";
                $test_results[] = "PASS: Update verification successful";
                $passed_tests++;
            } else {
                echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>⚠️ Data mismatch in verification</div>";
                $test_results[] = "FAIL: Update verification failed - data mismatch";
            }
        } else {
            throw new Exception("Could not retrieve updated record");
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ Error: " . $e->getMessage() . "</div>";
        $test_results[] = "FAIL: Update verification failed";
    }
    echo "</div></div>";
    
    // Test 4: Test edit.php page accessibility
    $total_tests++;
    echo "<div class='card mb-3'>";
    echo "<div class='card-header'><h5>Test 4: Edit Page Accessibility</h5></div>";
    echo "<div class='card-body'>";
    
    $edit_url = "http://localhost:8080/online_banking/admin/wire-transfers/edit.php?id=" . $transfer['id'];
    echo "<div class='alert alert-info'>";
    echo "<strong>Edit Page URL:</strong><br>";
    echo "<a href='$edit_url' target='_blank' class='btn btn-primary btn-sm'>$edit_url</a>";
    echo "</div>";
    
    // Check if the edit.php file exists and is readable
    $edit_file = '../../admin/wire-transfers/edit.php';
    if (file_exists($edit_file) && is_readable($edit_file)) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ Edit page file is accessible</div>";
        $test_results[] = "PASS: Edit page file accessible";
        $passed_tests++;
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ Edit page file not accessible</div>";
        $test_results[] = "FAIL: Edit page file not accessible";
    }
    echo "</div></div>";
}

// Test Summary
echo "<div class='card border-primary'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'><i class='fas fa-chart-bar me-2'></i>Submission Test Summary</h5>";
echo "</div>";
echo "<div class='card-body'>";

$success_rate = ($passed_tests / $total_tests) * 100;
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>Overall Results:</h6>";
echo "<div class='progress mb-3'>";
echo "<div class='progress-bar bg-" . ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'danger')) . "' style='width: {$success_rate}%'>";
echo round($success_rate, 1) . "%";
echo "</div>";
echo "</div>";
echo "<p><strong>Tests Passed:</strong> $passed_tests / $total_tests</p>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h6>Test Results:</h6>";
foreach ($test_results as $result) {
    $class = strpos($result, 'PASS') === 0 ? 'success' : (strpos($result, 'PARTIAL') === 0 ? 'warning' : 'danger');
    echo "<div class='badge bg-$class me-1 mb-1'>" . $result . "</div><br>";
}
echo "</div>";
echo "</div>";

if ($success_rate >= 80) {
    echo "<div class='alert alert-success mt-3'>";
    echo "<i class='fas fa-check-circle me-2'></i><strong>Excellent!</strong> The wire transfer edit form submission functionality is working correctly.";
    echo "</div>";
} elseif ($success_rate >= 60) {
    echo "<div class='alert alert-warning mt-3'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i><strong>Good!</strong> Most functionality tests passed, but there are some issues to address.";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger mt-3'>";
    echo "<i class='fas fa-times-circle me-2'></i><strong>Issues Found!</strong> Several functionality tests failed. Please review the implementation.";
    echo "</div>";
}

echo "</div></div>";

// Action Links
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h5>Test Actions</h5></div>";
echo "<div class='card-body'>";
echo "<div class='d-flex flex-wrap gap-2'>";

if (isset($transfer)) {
    echo "<a href='../../admin/wire-transfers/edit.php?id=" . $transfer['id'] . "' class='btn btn-primary' target='_blank'>";
    echo "<i class='fas fa-edit me-2'></i>Open Edit Page";
    echo "</a>";
    
    echo "<a href='../../admin/wire-transfers/view.php?id=" . $transfer['id'] . "' class='btn btn-outline-primary' target='_blank'>";
    echo "<i class='fas fa-eye me-2'></i>Open View Page";
    echo "</a>";
}

echo "<a href='test-wire-transfer-edit.php' class='btn btn-outline-secondary'>";
echo "<i class='fas fa-vial me-2'></i>Run Structure Test";
echo "</a>";

echo "<a href='test-wire-transfer-edit-submission.php' class='btn btn-outline-info'>";
echo "<i class='fas fa-redo me-2'></i>Run Submission Test Again";
echo "</a>";

echo "</div>";
echo "</div></div>";

echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";

// Close database connection
if (isset($db)) {
    $db->close();
}
?>