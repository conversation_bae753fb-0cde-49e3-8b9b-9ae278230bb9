<?php
/**
 * Test Wire Transfer Edit Functionality
 * Tests the redesigned edit.php page for wire transfers
 */

// Include necessary files
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Start session for testing
session_start();

// Set up test admin session (for testing purposes)
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'test_admin';
$_SESSION['admin_role'] = 'super_admin';

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Wire Transfer Edit Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-4'>";
echo "<h1 class='mb-4'><i class='fas fa-vial text-primary me-2'></i>Wire Transfer Edit Functionality Test</h1>";

$test_results = [];
$total_tests = 0;
$passed_tests = 0;

// Test 1: Check if edit.php file exists
$total_tests++;
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Test 1: File Existence Check</h5></div>";
echo "<div class='card-body'>";

$edit_file = '../../admin/wire-transfers/edit.php';
if (file_exists($edit_file)) {
    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ edit.php file exists</div>";
    $test_results[] = "PASS: edit.php file exists";
    $passed_tests++;
} else {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ edit.php file not found</div>";
    $test_results[] = "FAIL: edit.php file not found";
}
echo "</div></div>";

// Test 2: Check database connection and wire transfer data
$total_tests++;
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Test 2: Database Connection & Data Check</h5></div>";
echo "<div class='card-body'>";

try {
    $db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($db->connect_error) {
        throw new Exception("Connection failed: " . $db->connect_error);
    }
    
    // Check if wire_transfers table exists and has data
    $result = $db->query("SELECT COUNT(*) as count FROM wire_transfers LIMIT 1");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ Database connection successful</div>";
        echo "<div class='alert alert-info'><i class='fas fa-info me-2'></i>Wire transfers in database: $count</div>";
        $test_results[] = "PASS: Database connection and wire_transfers table accessible";
        $passed_tests++;
    } else {
        throw new Exception("Could not query wire_transfers table");
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ Database error: " . $e->getMessage() . "</div>";
    $test_results[] = "FAIL: Database connection or query failed";
}
echo "</div></div>";

// Test 3: Check for a specific wire transfer to test with
$total_tests++;
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Test 3: Test Data Availability</h5></div>";
echo "<div class='card-body'>";

try {
    $test_transfer_query = "SELECT * FROM wire_transfers WHERE id = 49 LIMIT 1";
    $test_result = $db->query($test_transfer_query);
    
    if ($test_result && $test_result->num_rows > 0) {
        $transfer = $test_result->fetch_assoc();
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ Test wire transfer (ID: 49) found</div>";
        echo "<div class='alert alert-info'>";
        echo "<strong>Transfer Details:</strong><br>";
        echo "Amount: " . number_format($transfer['amount'], 2) . " " . $transfer['currency'] . "<br>";
        echo "Status: " . $transfer['status'] . "<br>";
        echo "Recipient: " . htmlspecialchars($transfer['recipient_name']) . "<br>";
        echo "Created: " . $transfer['created_at'];
        echo "</div>";
        $test_results[] = "PASS: Test wire transfer data available";
        $passed_tests++;
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>⚠️ Test wire transfer (ID: 49) not found</div>";
        echo "<div class='alert alert-info'><i class='fas fa-info me-2'></i>Looking for any wire transfer...</div>";
        
        $any_transfer_query = "SELECT * FROM wire_transfers ORDER BY id DESC LIMIT 1";
        $any_result = $db->query($any_transfer_query);
        
        if ($any_result && $any_result->num_rows > 0) {
            $transfer = $any_result->fetch_assoc();
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ Alternative test transfer found (ID: " . $transfer['id'] . ")</div>";
            $test_results[] = "PASS: Alternative test wire transfer data available";
            $passed_tests++;
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ No wire transfers found in database</div>";
            $test_results[] = "FAIL: No test wire transfer data available";
        }
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ Error checking test data: " . $e->getMessage() . "</div>";
    $test_results[] = "FAIL: Error checking test data";
}
echo "</div></div>";

// Test 4: Check edit page structure and form elements
$total_tests++;
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Test 4: Edit Page Structure Check</h5></div>";
echo "<div class='card-body'>";

if (file_exists($edit_file)) {
    $edit_content = file_get_contents($edit_file);
    
    $structure_checks = [
        'form tag' => strpos($edit_content, '<form') !== false,
        'transfer details card' => strpos($edit_content, 'Transfer Details') !== false,
        'sender information card' => strpos($edit_content, 'Sender Information') !== false,
        'recipient information card' => strpos($edit_content, 'Recipient Information') !== false,
        'timeline information card' => strpos($edit_content, 'Timeline Information') !== false,
        'dynamic fields card' => strpos($edit_content, 'Wire Transfer Fields') !== false,
        'action buttons card' => strpos($edit_content, 'Save Changes') !== false,
        'bootstrap classes' => strpos($edit_content, 'card border-0 shadow-sm') !== false
    ];
    
    $structure_passed = 0;
    foreach ($structure_checks as $check => $result) {
        if ($result) {
            echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ $check found</div>";
            $structure_passed++;
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ $check missing</div>";
        }
    }
    
    if ($structure_passed === count($structure_checks)) {
        echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ All structure elements found</div>";
        $test_results[] = "PASS: Edit page structure complete";
        $passed_tests++;
    } else {
        echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle me-2'></i>⚠️ Some structure elements missing ($structure_passed/" . count($structure_checks) . ")</div>";
        $test_results[] = "PARTIAL: Edit page structure incomplete";
    }
} else {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ Cannot check structure - file not found</div>";
    $test_results[] = "FAIL: Cannot check structure";
}
echo "</div></div>";

// Test 5: Check if view.php exists for comparison
$total_tests++;
echo "<div class='card mb-3'>";
echo "<div class='card-header'><h5>Test 5: View Page Comparison Check</h5></div>";
echo "<div class='card-body'>";

$view_file = '../../admin/wire-transfers/view.php';
if (file_exists($view_file)) {
    echo "<div class='alert alert-success'><i class='fas fa-check me-2'></i>✅ view.php file exists for design comparison</div>";
    $test_results[] = "PASS: view.php file exists";
    $passed_tests++;
} else {
    echo "<div class='alert alert-danger'><i class='fas fa-times me-2'></i>❌ view.php file not found</div>";
    $test_results[] = "FAIL: view.php file not found";
}
echo "</div></div>";

// Test Summary
echo "<div class='card border-primary'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'><i class='fas fa-chart-bar me-2'></i>Test Summary</h5>";
echo "</div>";
echo "<div class='card-body'>";

$success_rate = ($passed_tests / $total_tests) * 100;
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>Overall Results:</h6>";
echo "<div class='progress mb-3'>";
echo "<div class='progress-bar bg-" . ($success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'danger')) . "' style='width: {$success_rate}%'>";
echo round($success_rate, 1) . "%";
echo "</div>";
echo "</div>";
echo "<p><strong>Tests Passed:</strong> $passed_tests / $total_tests</p>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h6>Test Results:</h6>";
foreach ($test_results as $result) {
    $class = strpos($result, 'PASS') === 0 ? 'success' : (strpos($result, 'PARTIAL') === 0 ? 'warning' : 'danger');
    echo "<div class='badge bg-$class me-1 mb-1'>" . $result . "</div><br>";
}
echo "</div>";
echo "</div>";

if ($success_rate >= 80) {
    echo "<div class='alert alert-success mt-3'>";
    echo "<i class='fas fa-check-circle me-2'></i><strong>Excellent!</strong> The wire transfer edit functionality appears to be working correctly.";
    echo "</div>";
} elseif ($success_rate >= 60) {
    echo "<div class='alert alert-warning mt-3'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i><strong>Good!</strong> Most tests passed, but there are some issues to address.";
    echo "</div>";
} else {
    echo "<div class='alert alert-danger mt-3'>";
    echo "<i class='fas fa-times-circle me-2'></i><strong>Issues Found!</strong> Several tests failed. Please review the implementation.";
    echo "</div>";
}

echo "</div></div>";

// Action Links
echo "<div class='card mt-4'>";
echo "<div class='card-header'><h5>Test Actions</h5></div>";
echo "<div class='card-body'>";
echo "<div class='d-flex flex-wrap gap-2'>";

if (isset($transfer)) {
    echo "<a href='../../admin/wire-transfers/edit.php?id=" . $transfer['id'] . "' class='btn btn-primary' target='_blank'>";
    echo "<i class='fas fa-edit me-2'></i>Test Edit Page";
    echo "</a>";
    
    echo "<a href='../../admin/wire-transfers/view.php?id=" . $transfer['id'] . "' class='btn btn-outline-primary' target='_blank'>";
    echo "<i class='fas fa-eye me-2'></i>Compare View Page";
    echo "</a>";
}

echo "<a href='../../admin/wire-transfers.php' class='btn btn-outline-secondary' target='_blank'>";
echo "<i class='fas fa-list me-2'></i>Wire Transfers List";
echo "</a>";

echo "<a href='test-wire-transfer-edit.php' class='btn btn-outline-info'>";
echo "<i class='fas fa-redo me-2'></i>Run Test Again";
echo "</a>";

echo "</div>";
echo "</div></div>";

echo "</div>"; // container

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body>";
echo "</html>";

// Close database connection
if (isset($db)) {
    $db->close();
}
?>