<?php
/**
 * Crypto Deposit Database Analysis
 * Check database structure for crypto deposits functionality
 */

require_once __DIR__ . '/../config/config.php';

// Set content type for web display
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Crypto Deposit Database Analysis</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
<h1>CRYPTO DEPOSIT DATABASE ANALYSIS</h1>
<pre>
<?php

echo "=== CRYPTO DEPOSIT DATABASE ANALYSIS ===\n\n";

try {
    $db = getDB();
    
    // Check if crypto_deposits table exists
    echo "1. Checking crypto_deposits table:\n";
    try {
        $result = $db->query("DESCRIBE crypto_deposits");
        echo "✓ crypto_deposits table exists\n";
        echo "Columns:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Key']}\n";
        }
    } catch (Exception $e) {
        echo "✗ crypto_deposits table does not exist\n";
        echo "Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n2. Checking payment_methods table:\n";
    try {
        $result = $db->query("DESCRIBE payment_methods");
        echo "✓ payment_methods table exists\n";
        echo "Columns:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Key']}\n";
        }
    } catch (Exception $e) {
        echo "✗ payment_methods table does not exist\n";
        echo "Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n3. Checking admin_crypto_addresses table:\n";
    try {
        $result = $db->query("DESCRIBE admin_crypto_addresses");
        echo "✓ admin_crypto_addresses table exists\n";
        echo "Columns:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Key']}\n";
        }
    } catch (Exception $e) {
        echo "✗ admin_crypto_addresses table does not exist\n";
        echo "Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n4. Checking existing payment methods:\n";
    try {
        $result = $db->query("SELECT id, method_name, method_type, is_active FROM payment_methods ORDER BY id");
        if ($result->num_rows > 0) {
            echo "Existing payment methods:\n";
            while ($row = $result->fetch_assoc()) {
                $status = $row['is_active'] ? 'Active' : 'Inactive';
                echo "  - ID: {$row['id']}, Name: {$row['method_name']}, Type: {$row['method_type']}, Status: {$status}\n";
            }
        } else {
            echo "No payment methods found\n";
        }
    } catch (Exception $e) {
        echo "Error checking payment methods: " . $e->getMessage() . "\n";
    }
    
    echo "\n5. Checking crypto deposits:\n";
    try {
        $result = $db->query("SELECT COUNT(*) as count FROM crypto_deposits");
        $count = $result->fetch_assoc()['count'];
        echo "Total crypto deposits: {$count}\n";
        
        if ($count > 0) {
            $result = $db->query("SELECT * FROM crypto_deposits ORDER BY created_at DESC LIMIT 3");
            echo "Recent deposits:\n";
            while ($row = $result->fetch_assoc()) {
                echo "  - ID: {$row['id']}, Amount: {$row['deposit_amount']}, Method: {$row['deposit_method']}, Status: {$row['status']}\n";
            }
        }
    } catch (Exception $e) {
        echo "Error checking crypto deposits: " . $e->getMessage() . "\n";
    }
    
    echo "\n6. Checking crypto_wallet_applications table:\n";
    try {
        $result = $db->query("DESCRIBE crypto_wallet_applications");
        echo "✓ crypto_wallet_applications table exists\n";
        echo "Columns:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']} ({$row['Type']}) {$row['Null']} {$row['Key']}\n";
        }
    } catch (Exception $e) {
        echo "✗ crypto_wallet_applications table does not exist\n";
        echo "Error: " . $e->getMessage() . "\n";
    }

    echo "\n7. Checking admin_crypto_addresses data:\n";
    try {
        $result = $db->query("SELECT * FROM admin_crypto_addresses WHERE is_active = 1");
        if ($result->num_rows > 0) {
            echo "Active admin crypto addresses:\n";
            while ($row = $result->fetch_assoc()) {
                echo "  - {$row['cryptocurrency']}: {$row['wallet_address']}\n";
            }
        } else {
            echo "No active admin crypto addresses found - this explains the error!\n";
        }
    } catch (Exception $e) {
        echo "Error checking admin crypto addresses: " . $e->getMessage() . "\n";
    }

    echo "\n8. Testing deposit method validation:\n";
    try {
        // Test the validation logic from the deposit form
        $valid_methods = ['bank_transfer', 'credit_card', 'debit_card', 'external_wallet'];
        echo "Valid hardcoded methods: " . implode(', ', $valid_methods) . "\n";

        // Check if any payment methods match these
        $result = $db->query("SELECT method_name, method_type FROM payment_methods WHERE is_active = 1");
        echo "Active payment methods in database:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['method_name']} ({$row['method_type']})\n";
        }
    } catch (Exception $e) {
        echo "Error in validation test: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "Database connection error: " . $e->getMessage() . "\n";
}

echo "\n=== ANALYSIS COMPLETE ===\n";
?>
</pre>
</body>
</html>
