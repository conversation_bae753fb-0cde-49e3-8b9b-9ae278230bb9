<?php
require_once '../../config/config.php';

try {
    $db = getDB();
    
    echo "=== DATABASE ANALYSIS ===\n\n";
    
    // Check if billing code tables exist
    echo "1. CHECKING BILLING CODE TABLES:\n";
    $tables = ['billing_code_settings', 'user_billing_codes', 'wire_transfer_fields'];
    
    foreach ($tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "✅ $table - EXISTS\n";
            
            // Show table structure
            $structure = $db->query("DESCRIBE $table");
            echo "   Columns: ";
            $columns = [];
            while ($row = $structure->fetch_assoc()) {
                $columns[] = $row['Field'];
            }
            echo implode(', ', $columns) . "\n";
            
            // Show row count
            $count = $db->query("SELECT COUNT(*) as count FROM $table");
            $count_row = $count->fetch_assoc();
            echo "   Rows: " . $count_row['count'] . "\n\n";
        } else {
            echo "❌ $table - MISSING\n\n";
        }
    }
    
    // Check accounts table for billing code column
    echo "2. CHECKING ACCOUNTS TABLE FOR BILLING CODE:\n";
    $result = $db->query("SHOW COLUMNS FROM accounts LIKE 'billing_code'");
    if ($result->num_rows > 0) {
        echo "✅ billing_code column exists in accounts table\n";
        
        // Show accounts with billing codes
        $accounts = $db->query("SELECT username, billing_code FROM accounts WHERE billing_code IS NOT NULL AND billing_code != ''");
        if ($accounts->num_rows > 0) {
            echo "   Accounts with billing codes:\n";
            while ($row = $accounts->fetch_assoc()) {
                echo "   - " . $row['username'] . ": " . $row['billing_code'] . "\n";
            }
        } else {
            echo "   No accounts have billing codes set\n";
        }
    } else {
        echo "❌ billing_code column missing from accounts table\n";
    }
    
    echo "\n3. CHECKING WIRE TRANSFER TABLES:\n";
    $wire_tables = ['transfers', 'wire_beneficiaries'];
    
    foreach ($wire_tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            echo "✅ $table - EXISTS\n";
            
            // Show row count
            $count = $db->query("SELECT COUNT(*) as count FROM $table");
            $count_row = $count->fetch_assoc();
            echo "   Rows: " . $count_row['count'] . "\n";
        } else {
            echo "❌ $table - MISSING\n";
        }
    }
    
    echo "\n=== ANALYSIS COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
