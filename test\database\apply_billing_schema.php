<?php
require_once '../../config/config.php';

try {
    $db = getDB();
    
    echo "Applying billing code schema...\n";
    
    // Read the schema file
    $schema = file_get_contents('../../database/billing_code_schema.sql');
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $db->query($statement);
            echo "✅ Executed: " . substr($statement, 0, 50) . "...\n";
        } catch (Exception $e) {
            echo "❌ Failed: " . substr($statement, 0, 50) . "... - " . $e->getMessage() . "\n";
        }
    }
    
    echo "✅ Billing schema application complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
