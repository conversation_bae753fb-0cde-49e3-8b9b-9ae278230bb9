<?php
/**
 * Apply New Features Database Schema
 * Run this script to create the new database tables for IRS, Crypto, and Card features
 */

require_once '../../config/config.php';

try {
    $db = getDB();
    
    // Read the schema file
    $schema_file = __DIR__ . '/../../sql/new_features_schema.sql';
    if (!file_exists($schema_file)) {
        throw new Exception("Schema file not found: $schema_file");
    }
    
    $sql_content = file_get_contents($schema_file);
    if ($sql_content === false) {
        throw new Exception("Failed to read schema file");
    }
    
    echo "<h2>🚀 Applying New Features Database Schema</h2>";
    echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 5px;'>";
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql_content)));
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $db->query($statement);
            
            // Extract table name for better feedback
            if (preg_match('/CREATE TABLE\s+(?:IF NOT EXISTS\s+)?`?(\w+)`?/i', $statement, $matches)) {
                echo "✅ Created table: <strong>{$matches[1]}</strong><br>";
            } elseif (preg_match('/ALTER TABLE\s+`?(\w+)`?/i', $statement, $matches)) {
                echo "✅ Modified table: <strong>{$matches[1]}</strong><br>";
            } elseif (preg_match('/INSERT INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                echo "✅ Inserted data into: <strong>{$matches[1]}</strong><br>";
            } else {
                echo "✅ Executed SQL statement<br>";
            }
            
            $success_count++;
            
        } catch (Exception $e) {
            echo "❌ <span style='color: red;'>Error executing statement:</span><br>";
            echo "<span style='color: #666;'>" . htmlspecialchars(substr($statement, 0, 100)) . "...</span><br>";
            echo "<span style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</span><br><br>";
            $error_count++;
        }
    }
    
    echo "</div>";
    
    echo "<h3>📊 Summary</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>$success_count</strong> statements executed successfully</li>";
    echo "<li>❌ <strong>$error_count</strong> statements failed</li>";
    echo "</ul>";
    
    if ($error_count === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<strong>🎉 Schema application completed successfully!</strong><br>";
        echo "All new features tables have been created and are ready to use.";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<strong>⚠️ Schema application completed with errors</strong><br>";
        echo "Some statements failed. Please review the errors above and fix any issues.";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>❌ Fatal Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}
?>
