<?php
/**
 * Database CLI Tool - Command Line Interface for Database Operations
 * Online Banking System Database Command Line Access
 * 
 * Usage: php database_cli.php [command] [options]
 * 
 * Commands:
 *   query "SQL"     - Execute SQL query
 *   tables          - List all tables
 *   describe TABLE  - Show table structure
 *   stats           - Show database statistics
 *   backup          - Create database backup
 *   users           - List user accounts
 *   help            - Show this help
 */

// Include database configuration
require_once '../../config/database.php';

// Color output functions for CLI
function colorOutput($text, $color = 'white') {
    $colors = [
        'red' => "\033[31m",
        'green' => "\033[32m",
        'yellow' => "\033[33m",
        'blue' => "\033[34m",
        'magenta' => "\033[35m",
        'cyan' => "\033[36m",
        'white' => "\033[37m",
        'reset' => "\033[0m"
    ];
    
    return $colors[$color] . $text . $colors['reset'];
}

function printHeader($text) {
    echo "\n" . colorOutput(str_repeat("=", 60), 'cyan') . "\n";
    echo colorOutput($text, 'cyan') . "\n";
    echo colorOutput(str_repeat("=", 60), 'cyan') . "\n";
}

function printSuccess($text) {
    echo colorOutput("✓ " . $text, 'green') . "\n";
}

function printError($text) {
    echo colorOutput("✗ " . $text, 'red') . "\n";
}

function printWarning($text) {
    echo colorOutput("⚠ " . $text, 'yellow') . "\n";
}

// Main CLI handler
function handleCommand($args) {
    if (empty($args) || count($args) < 2) {
        showHelp();
        return;
    }
    
    $command = strtolower($args[1]);
    
    try {
        $db = getDB();
        
        switch ($command) {
            case 'query':
                if (count($args) < 3) {
                    printError("Usage: php database_cli.php query \"SQL STATEMENT\"");
                    return;
                }
                executeQuery($db, $args[2]);
                break;
                
            case 'tables':
                listTables($db);
                break;
                
            case 'describe':
                if (count($args) < 3) {
                    printError("Usage: php database_cli.php describe TABLE_NAME");
                    return;
                }
                describeTable($db, $args[2]);
                break;
                
            case 'stats':
                showStats($db);
                break;
                
            case 'backup':
                createBackup($db);
                break;
                
            case 'users':
                listUsers($db);
                break;
                
            case 'help':
                showHelp();
                break;
                
            default:
                printError("Unknown command: $command");
                showHelp();
        }
        
    } catch (Exception $e) {
        printError("Database error: " . $e->getMessage());
    }
}

function executeQuery($db, $sql) {
    printHeader("Executing Query");
    echo colorOutput("SQL: ", 'yellow') . $sql . "\n\n";
    
    try {
        $result = $db->query($sql);
        
        if ($result === true) {
            printSuccess("Query executed successfully");
            return;
        }
        
        if ($result && $result->num_rows > 0) {
            // Get column names
            $fields = $result->fetch_fields();
            $headers = [];
            foreach ($fields as $field) {
                $headers[] = $field->name;
            }
            
            // Print headers
            echo colorOutput(implode("\t", $headers), 'cyan') . "\n";
            echo colorOutput(str_repeat("-", count($headers) * 15), 'cyan') . "\n";
            
            // Print rows
            while ($row = $result->fetch_assoc()) {
                $values = [];
                foreach ($headers as $header) {
                    $values[] = $row[$header] ?? 'NULL';
                }
                echo implode("\t", $values) . "\n";
            }
            
            printSuccess("Query returned " . $result->num_rows . " rows");
        } else {
            printWarning("Query executed but returned no results");
        }
        
    } catch (Exception $e) {
        printError("Query failed: " . $e->getMessage());
    }
}

function listTables($db) {
    printHeader("Database Tables");
    
    $result = $db->query("SHOW TABLES");
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_array()) {
            echo colorOutput("• ", 'green') . $row[0] . "\n";
        }
        printSuccess("Found " . $result->num_rows . " tables");
    } else {
        printWarning("No tables found");
    }
}

function describeTable($db, $table) {
    printHeader("Table Structure: $table");
    
    $result = $db->query("DESCRIBE `$table`");
    if ($result && $result->num_rows > 0) {
        echo colorOutput("Field\t\tType\t\tNull\tKey\tDefault\tExtra", 'cyan') . "\n";
        echo colorOutput(str_repeat("-", 80), 'cyan') . "\n";
        
        while ($row = $result->fetch_assoc()) {
            echo sprintf("%-15s %-15s %-5s %-5s %-10s %s\n",
                $row['Field'],
                $row['Type'],
                $row['Null'],
                $row['Key'],
                $row['Default'] ?? 'NULL',
                $row['Extra']
            );
        }
    } else {
        printError("Table '$table' not found or no structure available");
    }
}

function showStats($db) {
    printHeader("Database Statistics");
    
    // Database size
    $result = $db->query("SELECT 
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB'
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()");
    
    if ($result && $row = $result->fetch_assoc()) {
        echo colorOutput("Database Size: ", 'yellow') . $row['DB Size in MB'] . " MB\n";
    }
    
    // Table count
    $result = $db->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = DATABASE()");
    if ($result && $row = $result->fetch_assoc()) {
        echo colorOutput("Total Tables: ", 'yellow') . $row['table_count'] . "\n";
    }
    
    // User accounts
    $result = $db->query("SELECT COUNT(*) as user_count FROM accounts");
    if ($result && $row = $result->fetch_assoc()) {
        echo colorOutput("User Accounts: ", 'yellow') . $row['user_count'] . "\n";
    }
    
    // Transactions
    $result = $db->query("SELECT COUNT(*) as transaction_count FROM transactions");
    if ($result && $row = $result->fetch_assoc()) {
        echo colorOutput("Transactions: ", 'yellow') . $row['transaction_count'] . "\n";
    }
}

function createBackup($db) {
    printHeader("Creating Database Backup");
    
    $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
    $filepath = __DIR__ . '/../../sql/' . $filename;
    
    // This is a simplified backup - in production, use mysqldump
    printWarning("This creates a basic backup. For production, use mysqldump.");
    
    $tables = [];
    $result = $db->query("SHOW TABLES");
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
    }
    
    $backup_content = "-- Database Backup Created: " . date('Y-m-d H:i:s') . "\n\n";
    
    foreach ($tables as $table) {
        $backup_content .= "-- Table: $table\n";
        $result = $db->query("SELECT * FROM `$table`");
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $values = array_map(function($value) {
                    return "'" . addslashes($value) . "'";
                }, array_values($row));
                $backup_content .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
            }
        }
        $backup_content .= "\n";
    }
    
    if (file_put_contents($filepath, $backup_content)) {
        printSuccess("Backup created: $filename");
    } else {
        printError("Failed to create backup file");
    }
}

function listUsers($db) {
    printHeader("User Accounts");
    
    $result = $db->query("SELECT id, username, first_name, last_name, email, is_admin, status, created_at FROM accounts ORDER BY created_at DESC");
    
    if ($result && $result->num_rows > 0) {
        echo colorOutput("ID\tUsername\tName\t\tEmail\t\tAdmin\tStatus\tCreated", 'cyan') . "\n";
        echo colorOutput(str_repeat("-", 100), 'cyan') . "\n";
        
        while ($row = $result->fetch_assoc()) {
            $admin_status = $row['is_admin'] ? 'Yes' : 'No';
            echo sprintf("%-5s %-15s %-20s %-25s %-5s %-10s %s\n",
                $row['id'],
                $row['username'],
                $row['first_name'] . ' ' . $row['last_name'],
                $row['email'],
                $admin_status,
                $row['status'],
                $row['created_at']
            );
        }
        
        printSuccess("Found " . $result->num_rows . " user accounts");
    } else {
        printWarning("No user accounts found");
    }
}

function showHelp() {
    printHeader("Database CLI Tool - Help");
    
    echo colorOutput("Usage: ", 'yellow') . "php database_cli.php [command] [options]\n\n";
    
    echo colorOutput("Available Commands:", 'cyan') . "\n";
    echo "  query \"SQL\"     - Execute a SQL query\n";
    echo "  tables          - List all database tables\n";
    echo "  describe TABLE  - Show table structure\n";
    echo "  stats           - Show database statistics\n";
    echo "  backup          - Create a simple database backup\n";
    echo "  users           - List all user accounts\n";
    echo "  help            - Show this help message\n\n";
    
    echo colorOutput("Examples:", 'yellow') . "\n";
    echo "  php database_cli.php tables\n";
    echo "  php database_cli.php describe accounts\n";
    echo "  php database_cli.php query \"SELECT * FROM accounts LIMIT 5\"\n";
    echo "  php database_cli.php stats\n\n";
    
    echo colorOutput("Note: ", 'red') . "Be careful with query commands. Always backup before making changes.\n";
}

// Run the CLI
if (php_sapi_name() === 'cli') {
    handleCommand($argv);
} else {
    echo "<h1>Database CLI Tool</h1>";
    echo "<p>This tool is designed to run from the command line.</p>";
    echo "<p>Usage: <code>php database_cli.php [command] [options]</code></p>";
    echo "<p><a href='database_terminal.php'>Use Database Terminal instead</a></p>";
}
?>
