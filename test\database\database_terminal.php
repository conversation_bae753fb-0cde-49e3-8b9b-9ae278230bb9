<?php
/**
 * Database Terminal - Interactive SQL Query Tool
 * Online Banking System Database Access Terminal
 * 
 * This tool provides secure, interactive access to the database
 * with query execution, result formatting, and safety features.
 */

// Security check - only allow access from localhost or specific IPs
$allowed_ips = ['127.0.0.1', '::1', 'localhost'];
$client_ip = $_SERVER['REMOTE_ADDR'] ?? $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 'unknown';

if (!in_array($client_ip, $allowed_ips) && !isset($_GET['allow_remote'])) {
    die("Access denied. Database terminal only accessible from localhost for security.");
}

// Include database configuration
require_once '../../config/database.php';

// Initialize variables
$query = $_POST['query'] ?? '';
$result_data = null;
$error_message = null;
$execution_time = 0;

// Process query if submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($query)) {
    $start_time = microtime(true);
    
    try {
        $db = getDB();
        
        // Security check - prevent dangerous operations
        $dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE'];
        $query_upper = strtoupper(trim($query));
        
        $is_dangerous = false;
        foreach ($dangerous_keywords as $keyword) {
            if (strpos($query_upper, $keyword) === 0) {
                $is_dangerous = true;
                break;
            }
        }
        
        if ($is_dangerous && !isset($_POST['confirm_dangerous'])) {
            $error_message = "This query contains potentially dangerous operations. Check 'Confirm Dangerous Operations' to execute.";
        } else {
            $result = $db->query($query);
            $execution_time = round((microtime(true) - $start_time) * 1000, 2);
            
            if ($result === true) {
                $result_data = ['type' => 'success', 'message' => 'Query executed successfully'];
            } elseif ($result && $result->num_rows > 0) {
                $result_data = ['type' => 'select', 'data' => []];
                
                // Get column information
                $fields = $result->fetch_fields();
                $result_data['columns'] = [];
                foreach ($fields as $field) {
                    $result_data['columns'][] = $field->name;
                }
                
                // Get row data
                while ($row = $result->fetch_assoc()) {
                    $result_data['data'][] = $row;
                }
                
                $result_data['row_count'] = count($result_data['data']);
            } else {
                $result_data = ['type' => 'empty', 'message' => 'Query executed but returned no results'];
            }
        }
        
    } catch (Exception $e) {
        $error_message = "Query Error: " . $e->getMessage();
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
    }
}

// Get database statistics
$db_stats = [];
try {
    $db = getDB();
    
    // Table count
    $result = $db->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE()");
    $db_stats['tables'] = $result ? $result->fetch_assoc()['count'] : 0;
    
    // Database size
    $result = $db->query("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb FROM information_schema.tables WHERE table_schema = DATABASE()");
    $db_stats['size_mb'] = $result ? $result->fetch_assoc()['size_mb'] : 0;
    
    // User count
    $result = $db->query("SELECT COUNT(*) as count FROM accounts");
    $db_stats['users'] = $result ? $result->fetch_assoc()['count'] : 0;
    
} catch (Exception $e) {
    $db_stats = ['error' => $e->getMessage()];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Terminal - Online Banking System</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Courier New', monospace; background: #1a1a1a; color: #00ff00; padding: 20px; }
        .terminal { background: #000; border: 2px solid #00ff00; border-radius: 10px; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; margin-bottom: 20px; border-bottom: 1px solid #00ff00; padding-bottom: 10px; }
        .stats { display: flex; justify-content: space-around; margin-bottom: 20px; padding: 10px; background: #0a0a0a; border-radius: 5px; }
        .stat-item { text-align: center; }
        .stat-value { font-size: 1.5em; color: #00ffff; }
        .query-form { margin-bottom: 20px; }
        .query-textarea { width: 100%; height: 150px; background: #0a0a0a; color: #00ff00; border: 1px solid #00ff00; padding: 10px; font-family: inherit; resize: vertical; }
        .form-controls { margin: 10px 0; display: flex; gap: 10px; align-items: center; }
        .btn { background: #00ff00; color: #000; border: none; padding: 10px 20px; cursor: pointer; font-family: inherit; border-radius: 3px; }
        .btn:hover { background: #00cc00; }
        .btn-danger { background: #ff0000; color: #fff; }
        .btn-danger:hover { background: #cc0000; }
        .checkbox { margin-right: 5px; }
        .result-container { margin-top: 20px; }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .info { color: #00ffff; }
        .execution-time { font-size: 0.9em; color: #888; margin-top: 5px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #00ff00; padding: 8px; text-align: left; }
        th { background: #0a0a0a; color: #00ffff; }
        tr:nth-child(even) { background: #0a0a0a; }
        .quick-queries { margin-top: 20px; }
        .quick-query { display: inline-block; margin: 5px; padding: 5px 10px; background: #333; border: 1px solid #666; cursor: pointer; border-radius: 3px; }
        .quick-query:hover { background: #555; }
        .footer { margin-top: 30px; text-align: center; font-size: 0.9em; color: #666; border-top: 1px solid #333; padding-top: 10px; }
    </style>
</head>
<body>
    <div class="terminal">
        <div class="header">
            <h1>🖥️ DATABASE TERMINAL</h1>
            <p>Online Banking System - Interactive SQL Query Tool</p>
        </div>
        
        <?php if (!isset($db_stats['error'])): ?>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value"><?php echo $db_stats['tables']; ?></div>
                <div>Tables</div>
            </div>
            <div class="stat-item">
                <div class="stat-value"><?php echo $db_stats['size_mb']; ?> MB</div>
                <div>Database Size</div>
            </div>
            <div class="stat-item">
                <div class="stat-value"><?php echo $db_stats['users']; ?></div>
                <div>User Accounts</div>
            </div>
            <div class="stat-item">
                <div class="stat-value"><?php echo date('H:i:s'); ?></div>
                <div>Current Time</div>
            </div>
        </div>
        <?php endif; ?>
        
        <form method="POST" class="query-form">
            <label for="query">SQL Query:</label>
            <textarea name="query" id="query" class="query-textarea" placeholder="Enter your SQL query here..."><?php echo htmlspecialchars($query); ?></textarea>
            
            <div class="form-controls">
                <button type="submit" class="btn">Execute Query</button>
                <label>
                    <input type="checkbox" name="confirm_dangerous" class="checkbox" <?php echo isset($_POST['confirm_dangerous']) ? 'checked' : ''; ?>>
                    Confirm Dangerous Operations (DROP, DELETE, etc.)
                </label>
                <button type="button" class="btn" onclick="clearQuery()">Clear</button>
            </div>
        </form>
        
        <?php if ($error_message): ?>
        <div class="result-container">
            <div class="error">❌ <?php echo htmlspecialchars($error_message); ?></div>
            <?php if ($execution_time > 0): ?>
            <div class="execution-time">Execution time: <?php echo $execution_time; ?>ms</div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <?php if ($result_data): ?>
        <div class="result-container">
            <?php if ($result_data['type'] === 'success'): ?>
                <div class="success">✅ <?php echo $result_data['message']; ?></div>
            <?php elseif ($result_data['type'] === 'empty'): ?>
                <div class="warning">⚠️ <?php echo $result_data['message']; ?></div>
            <?php elseif ($result_data['type'] === 'select'): ?>
                <div class="success">✅ Query executed successfully - <?php echo $result_data['row_count']; ?> rows returned</div>
                <table>
                    <thead>
                        <tr>
                            <?php foreach ($result_data['columns'] as $column): ?>
                            <th><?php echo htmlspecialchars($column); ?></th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($result_data['data'] as $row): ?>
                        <tr>
                            <?php foreach ($result_data['columns'] as $column): ?>
                            <td><?php echo htmlspecialchars($row[$column] ?? 'NULL'); ?></td>
                            <?php endforeach; ?>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
            <div class="execution-time">Execution time: <?php echo $execution_time; ?>ms</div>
        </div>
        <?php endif; ?>
        
        <div class="quick-queries">
            <h3>Quick Queries:</h3>
            <div class="quick-query" onclick="setQuery('SHOW TABLES')">Show Tables</div>
            <div class="quick-query" onclick="setQuery('SELECT * FROM accounts LIMIT 10')">List Users</div>
            <div class="quick-query" onclick="setQuery('SELECT * FROM transactions ORDER BY created_at DESC LIMIT 10')">Recent Transactions</div>
            <div class="quick-query" onclick="setQuery('SELECT COUNT(*) as total_users FROM accounts')">User Count</div>
            <div class="quick-query" onclick="setQuery('SELECT status, COUNT(*) as count FROM accounts GROUP BY status')">User Status</div>
            <div class="quick-query" onclick="setQuery('DESCRIBE accounts')">Accounts Structure</div>
            <div class="quick-query" onclick="setQuery('SELECT TABLE_NAME, TABLE_ROWS FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE()')">Table Sizes</div>
        </div>
        
        <div class="footer">
            <p>⚠️ <strong>Security Notice:</strong> This terminal is restricted to localhost access only.</p>
            <p>Use with caution. Always backup before making changes to the database.</p>
            <p><a href="../../admin/" style="color: #00ffff;">← Back to Admin Dashboard</a></p>
        </div>
    </div>
    
    <script>
        function setQuery(query) {
            document.getElementById('query').value = query;
        }
        
        function clearQuery() {
            document.getElementById('query').value = '';
        }
        
        // Auto-focus on query textarea
        document.getElementById('query').focus();
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                document.querySelector('form').submit();
            }
        });
    </script>
</body>
</html>
