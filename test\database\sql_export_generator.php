<?php
/**
 * SQL Export Generator - Database Backup and Export Tool
 * Online Banking System Database Export Utility
 */

require_once '../../config/database.php';

// Configuration
$export_types = [
    'full' => 'Full Database (Structure + Data)',
    'structure' => 'Structure Only',
    'data' => 'Data Only',
    'custom' => 'Custom Tables'
];

$export_type = $_GET['type'] ?? 'full';
$selected_tables = $_GET['tables'] ?? [];
$download = isset($_GET['download']);

// Get all tables
$db = getDB();
$tables_result = $db->query("SHOW TABLES");
$all_tables = [];
while ($row = $tables_result->fetch_array()) {
    $all_tables[] = $row[0];
}

// Generate export if requested
$export_content = '';
$filename = '';

if ($download) {
    $timestamp = date('Y-m-d_H-i-s');
    
    switch ($export_type) {
        case 'full':
            $filename = "full_database_backup_$timestamp.sql";
            $export_content = generateFullExport($db, $all_tables);
            break;
            
        case 'structure':
            $filename = "database_structure_$timestamp.sql";
            $export_content = generateStructureExport($db, $all_tables);
            break;
            
        case 'data':
            $filename = "database_data_$timestamp.sql";
            $export_content = generateDataExport($db, $all_tables);
            break;
            
        case 'custom':
            $tables_to_export = is_array($selected_tables) ? $selected_tables : [$selected_tables];
            $filename = "custom_export_$timestamp.sql";
            $export_content = generateFullExport($db, $tables_to_export);
            break;
    }
    
    // Send download headers
    header('Content-Type: application/sql');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . strlen($export_content));
    echo $export_content;
    exit;
}

function generateFullExport($db, $tables) {
    $export = "-- Full Database Export\n";
    $export .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
    $export .= "-- Database: " . DB_NAME . "\n\n";
    
    $export .= "SET FOREIGN_KEY_CHECKS = 0;\n";
    $export .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
    $export .= "SET time_zone = \"+00:00\";\n\n";
    
    foreach ($tables as $table) {
        $export .= generateTableStructure($db, $table);
        $export .= generateTableData($db, $table);
        $export .= "\n";
    }
    
    $export .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    return $export;
}

function generateStructureExport($db, $tables) {
    $export = "-- Database Structure Export\n";
    $export .= "-- Generated: " . date('Y-m-d H:i:s') . "\n\n";
    
    foreach ($tables as $table) {
        $export .= generateTableStructure($db, $table);
    }
    
    return $export;
}

function generateDataExport($db, $tables) {
    $export = "-- Database Data Export\n";
    $export .= "-- Generated: " . date('Y-m-d H:i:s') . "\n\n";
    
    foreach ($tables as $table) {
        $export .= generateTableData($db, $table);
    }
    
    return $export;
}

function generateTableStructure($db, $table) {
    $structure = "-- Table structure for table `$table`\n";
    $structure .= "DROP TABLE IF EXISTS `$table`;\n";
    
    $result = $db->query("SHOW CREATE TABLE `$table`");
    if ($result && $row = $result->fetch_assoc()) {
        $structure .= $row['Create Table'] . ";\n\n";
    }
    
    return $structure;
}

function generateTableData($db, $table) {
    $data = "-- Dumping data for table `$table`\n";
    
    $result = $db->query("SELECT * FROM `$table`");
    if ($result && $result->num_rows > 0) {
        // Get column names
        $fields = $result->fetch_fields();
        $columns = [];
        foreach ($fields as $field) {
            $columns[] = "`{$field->name}`";
        }
        
        $data .= "INSERT INTO `$table` (" . implode(', ', $columns) . ") VALUES\n";
        
        $rows = [];
        while ($row = $result->fetch_assoc()) {
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = "'" . addslashes($value) . "'";
                }
            }
            $rows[] = '(' . implode(', ', $values) . ')';
        }
        
        $data .= implode(",\n", $rows) . ";\n\n";
    } else {
        $data .= "-- No data found for table `$table`\n\n";
    }
    
    return $data;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Export Generator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .checkbox-group { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 10px; }
        .checkbox-item { display: flex; align-items: center; }
        .checkbox-item input { width: auto; margin-right: 10px; }
        .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .stat-value { font-size: 1.5em; font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ SQL Export Generator</h1>
            <p>Database Backup and Export Tool</p>
        </div>
        
        <div class="info">
            <strong>📊 Database Information:</strong><br>
            Database: <?php echo DB_NAME; ?><br>
            Host: <?php echo DB_HOST; ?><br>
            Total Tables: <?php echo count($all_tables); ?>
        </div>
        
        <div class="stats">
            <?php
            // Get table statistics
            foreach (['accounts', 'transactions', 'virtual_cards', 'audit_logs'] as $key_table) {
                if (in_array($key_table, $all_tables)) {
                    $result = $db->query("SELECT COUNT(*) as count FROM `$key_table`");
                    $count = $result ? $result->fetch_assoc()['count'] : 0;
                    echo "<div class='stat-card'>";
                    echo "<div class='stat-value'>$count</div>";
                    echo "<div>" . ucfirst(str_replace('_', ' ', $key_table)) . "</div>";
                    echo "</div>";
                }
            }
            ?>
        </div>
        
        <form method="GET">
            <div class="form-group">
                <label for="type">Export Type:</label>
                <select name="type" id="type" onchange="toggleCustomTables()">
                    <?php foreach ($export_types as $key => $label): ?>
                    <option value="<?php echo $key; ?>" <?php echo $export_type === $key ? 'selected' : ''; ?>>
                        <?php echo $label; ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group" id="custom-tables" style="<?php echo $export_type === 'custom' ? '' : 'display: none;'; ?>">
                <label>Select Tables to Export:</label>
                <div class="checkbox-group">
                    <?php foreach ($all_tables as $table): ?>
                    <div class="checkbox-item">
                        <input type="checkbox" name="tables[]" value="<?php echo $table; ?>" id="table_<?php echo $table; ?>"
                               <?php echo in_array($table, $selected_tables) ? 'checked' : ''; ?>>
                        <label for="table_<?php echo $table; ?>"><?php echo $table; ?></label>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" name="download" value="1" class="btn btn-success">
                    📥 Download Export
                </button>
                <a href="sql_export_interface.php" class="btn">
                    🔧 Advanced Export Interface
                </a>
                <a href="../../admin/" class="btn">
                    ← Back to Admin
                </a>
            </div>
        </form>
        
        <div class="info">
            <strong>📝 Export Types:</strong><br>
            • <strong>Full Database:</strong> Complete backup with structure and data<br>
            • <strong>Structure Only:</strong> Table definitions without data<br>
            • <strong>Data Only:</strong> Table data without structure<br>
            • <strong>Custom Tables:</strong> Select specific tables to export
        </div>
    </div>
    
    <script>
        function toggleCustomTables() {
            const type = document.getElementById('type').value;
            const customDiv = document.getElementById('custom-tables');
            customDiv.style.display = type === 'custom' ? 'block' : 'none';
        }
    </script>
</body>
</html>
