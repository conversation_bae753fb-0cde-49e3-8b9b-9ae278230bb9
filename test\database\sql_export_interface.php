<?php
/**
 * SQL Export Interface - Advanced Database Export Tool
 * Web-based interface for database exports with advanced options
 */

require_once '../../config/database.php';

$db = getDB();
$message = '';
$export_files = [];

// Get list of existing export files
$export_dir = __DIR__ . '/../../sql/';
if (is_dir($export_dir)) {
    $files = scandir($export_dir);
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $filepath = $export_dir . $file;
            $export_files[] = [
                'name' => $file,
                'size' => filesize($filepath),
                'date' => date('Y-m-d H:i:s', filemtime($filepath))
            ];
        }
    }
    // Sort by date, newest first
    usort($export_files, function($a, $b) {
        return strcmp($b['date'], $a['date']);
    });
}

// Handle export generation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $export_name = $_POST['export_name'] ?? 'custom_export';
    $include_structure = isset($_POST['include_structure']);
    $include_data = isset($_POST['include_data']);
    $selected_tables = $_POST['tables'] ?? [];
    
    if (!empty($selected_tables) && ($include_structure || $include_data)) {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = $export_name . '_' . $timestamp . '.sql';
        $filepath = $export_dir . $filename;
        
        $export_content = generateCustomExport($db, $selected_tables, $include_structure, $include_data);
        
        if (file_put_contents($filepath, $export_content)) {
            $message = "Export created successfully: $filename";
        } else {
            $message = "Error: Could not create export file";
        }
    } else {
        $message = "Error: Please select tables and at least one export option";
    }
}

// Handle file download
if (isset($_GET['download'])) {
    $filename = basename($_GET['download']);
    $filepath = $export_dir . $filename;
    
    if (file_exists($filepath)) {
        header('Content-Type: application/sql');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filepath));
        readfile($filepath);
        exit;
    }
}

// Handle file deletion
if (isset($_GET['delete'])) {
    $filename = basename($_GET['delete']);
    $filepath = $export_dir . $filename;
    
    if (file_exists($filepath) && unlink($filepath)) {
        $message = "File deleted: $filename";
    } else {
        $message = "Error: Could not delete file";
    }
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

function generateCustomExport($db, $tables, $include_structure, $include_data) {
    $export = "-- Custom Database Export\n";
    $export .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
    $export .= "-- Tables: " . implode(', ', $tables) . "\n";
    $export .= "-- Structure: " . ($include_structure ? 'Yes' : 'No') . "\n";
    $export .= "-- Data: " . ($include_data ? 'Yes' : 'No') . "\n\n";
    
    if ($include_structure || $include_data) {
        $export .= "SET FOREIGN_KEY_CHECKS = 0;\n";
        $export .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $export .= "SET time_zone = \"+00:00\";\n\n";
    }
    
    foreach ($tables as $table) {
        if ($include_structure) {
            $export .= generateTableStructure($db, $table);
        }
        if ($include_data) {
            $export .= generateTableData($db, $table);
        }
        $export .= "\n";
    }
    
    if ($include_structure || $include_data) {
        $export .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    }
    
    return $export;
}

function generateTableStructure($db, $table) {
    $structure = "-- Table structure for table `$table`\n";
    $structure .= "DROP TABLE IF EXISTS `$table`;\n";
    
    $result = $db->query("SHOW CREATE TABLE `$table`");
    if ($result && $row = $result->fetch_assoc()) {
        $structure .= $row['Create Table'] . ";\n\n";
    }
    
    return $structure;
}

function generateTableData($db, $table) {
    $data = "-- Dumping data for table `$table`\n";
    
    $result = $db->query("SELECT * FROM `$table`");
    if ($result && $result->num_rows > 0) {
        $fields = $result->fetch_fields();
        $columns = [];
        foreach ($fields as $field) {
            $columns[] = "`{$field->name}`";
        }
        
        $data .= "INSERT INTO `$table` (" . implode(', ', $columns) . ") VALUES\n";
        
        $rows = [];
        while ($row = $result->fetch_assoc()) {
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = "'" . addslashes($value) . "'";
                }
            }
            $rows[] = '(' . implode(', ', $values) . ')';
        }
        
        $data .= implode(",\n", $rows) . ";\n\n";
    } else {
        $data .= "-- No data found for table `$table`\n\n";
    }
    
    return $data;
}

// Get all tables with row counts
$tables_result = $db->query("SHOW TABLES");
$all_tables = [];
while ($row = $tables_result->fetch_array()) {
    $table_name = $row[0];
    $count_result = $db->query("SELECT COUNT(*) as count FROM `$table_name`");
    $row_count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
    $all_tables[] = ['name' => $table_name, 'rows' => $row_count];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Export Interface</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #333; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { padding: 8px; border: 1px solid #ddd; border-radius: 3px; }
        .checkbox-group { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin-top: 10px; }
        .checkbox-item { display: flex; align-items: center; padding: 5px; background: #f8f9fa; border-radius: 3px; }
        .checkbox-item input { margin-right: 10px; }
        .table-info { font-size: 0.9em; color: #666; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-small { padding: 5px 10px; font-size: 0.9em; }
        .message { padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .file-list { max-height: 400px; overflow-y: auto; }
        .file-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .file-info { flex-grow: 1; }
        .file-actions { display: flex; gap: 5px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ SQL Export Interface</h1>
            <p>Advanced Database Export Tool</p>
        </div>
        
        <?php if ($message): ?>
        <div class="message <?php echo strpos($message, 'Error') === 0 ? 'error' : 'success'; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h3>📊 Create New Export</h3>
            <form method="POST">
                <div class="form-group">
                    <label for="export_name">Export Name:</label>
                    <input type="text" name="export_name" id="export_name" value="custom_export" required>
                </div>
                
                <div class="form-group">
                    <label>Export Options:</label>
                    <div style="margin-top: 10px;">
                        <label style="display: inline; font-weight: normal;">
                            <input type="checkbox" name="include_structure" checked> Include Table Structure
                        </label><br>
                        <label style="display: inline; font-weight: normal;">
                            <input type="checkbox" name="include_data" checked> Include Table Data
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Select Tables:</label>
                    <div style="margin: 10px 0;">
                        <button type="button" onclick="selectAllTables()" class="btn btn-small">Select All</button>
                        <button type="button" onclick="selectNoTables()" class="btn btn-small">Select None</button>
                    </div>
                    <div class="checkbox-group">
                        <?php foreach ($all_tables as $table): ?>
                        <div class="checkbox-item">
                            <input type="checkbox" name="tables[]" value="<?php echo $table['name']; ?>" id="table_<?php echo $table['name']; ?>">
                            <label for="table_<?php echo $table['name']; ?>">
                                <strong><?php echo $table['name']; ?></strong>
                                <div class="table-info"><?php echo number_format($table['rows']); ?> rows</div>
                            </label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-success">Create Export</button>
            </form>
        </div>
        
        <div class="section">
            <h3>📁 Existing Export Files</h3>
            <?php if (empty($export_files)): ?>
            <p>No export files found.</p>
            <?php else: ?>
            <div class="file-list">
                <?php foreach ($export_files as $file): ?>
                <div class="file-item">
                    <div class="file-info">
                        <strong><?php echo htmlspecialchars($file['name']); ?></strong><br>
                        <small>Size: <?php echo number_format($file['size'] / 1024, 1); ?> KB | Created: <?php echo $file['date']; ?></small>
                    </div>
                    <div class="file-actions">
                        <a href="?download=<?php echo urlencode($file['name']); ?>" class="btn btn-small">Download</a>
                        <a href="?delete=<?php echo urlencode($file['name']); ?>" class="btn btn-small btn-danger" 
                           onclick="return confirm('Are you sure you want to delete this file?')">Delete</a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h3>🔧 Quick Actions</h3>
            <a href="sql_export_generator.php" class="btn">Simple Export Generator</a>
            <a href="database_terminal.php" class="btn">Database Terminal</a>
            <a href="../../admin/" class="btn">← Back to Admin</a>
        </div>
    </div>
    
    <script>
        function selectAllTables() {
            const checkboxes = document.querySelectorAll('input[name="tables[]"]');
            checkboxes.forEach(cb => cb.checked = true);
        }
        
        function selectNoTables() {
            const checkboxes = document.querySelectorAll('input[name="tables[]"]');
            checkboxes.forEach(cb => cb.checked = false);
        }
    </script>
</body>
</html>
