<?php
require_once '../../config/config.php';

try {
    $db = getDB();
    
    echo "Updating document file paths...\n";
    
    // Update the test documents to point to actual files
    $updates = [
        [1, 'uploads/documents/user_1/drivers_license.html'],
        [2, 'uploads/documents/user_1/drivers_license.html'], // Same file for testing
        [3, 'uploads/documents/user_1/drivers_license.html'], // Same file for testing
        [4, 'uploads/documents/user_1/drivers_license.html']  // Same file for testing
    ];
    
    foreach ($updates as [$id, $path]) {
        $result = $db->query("UPDATE user_documents SET file_path = ? WHERE id = ?", [$path, $id]);
        if ($result) {
            echo "✓ Updated document ID $id with path: $path\n";
        } else {
            echo "✗ Failed to update document ID $id\n";
        }
    }
    
    // Verify updates
    echo "\nVerifying updates...\n";
    $verify = $db->query("SELECT id, file_path FROM user_documents WHERE id IN (1,2,3,4)");
    while ($row = $verify->fetch_assoc()) {
        echo "Document ID {$row['id']}: {$row['file_path']}\n";
    }
    
    echo "\n✅ Document path updates complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
