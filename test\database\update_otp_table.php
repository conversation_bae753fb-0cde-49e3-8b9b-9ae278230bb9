<?php
require_once '../../config/config.php';

try {
    $db = getDB();
    
    echo "Updating OTP table structure...\n";
    
    // Check if otp_codes table exists
    $result = $db->query("SHOW TABLES LIKE 'otp_codes'");
    
    if ($result->num_rows > 0) {
        echo "✓ otp_codes table exists\n";
        
        // Check current structure
        $structure = $db->query("DESCRIBE otp_codes");
        $columns = [];
        while ($row = $structure->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        
        echo "Current columns: " . implode(', ', $columns) . "\n";
        
        // Add missing columns if needed
        $required_columns = [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'user_id' => 'INT NOT NULL',
            'otp_code' => 'VARCHAR(6) NOT NULL',
            'purpose' => 'VARCHAR(50) NOT NULL',
            'expires_at' => 'TIMESTAMP NOT NULL',
            'used' => 'BOOLEAN DEFAULT FALSE',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ];
        
        foreach ($required_columns as $column => $definition) {
            if (!in_array($column, $columns)) {
                try {
                    $db->query("ALTER TABLE otp_codes ADD COLUMN $column $definition");
                    echo "✓ Added column: $column\n";
                } catch (Exception $e) {
                    echo "✗ Failed to add column $column: " . $e->getMessage() . "\n";
                }
            }
        }
        
        // Add foreign key if not exists
        try {
            $db->query("ALTER TABLE otp_codes ADD CONSTRAINT fk_otp_user FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE");
            echo "✓ Added foreign key constraint\n";
        } catch (Exception $e) {
            echo "⚠ Foreign key constraint may already exist or failed: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "Creating otp_codes table...\n";
        
        $create_sql = "
        CREATE TABLE otp_codes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            otp_code VARCHAR(6) NOT NULL,
            purpose VARCHAR(50) NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE
        )";
        
        $db->query($create_sql);
        echo "✓ otp_codes table created\n";
    }
    
    // Clean up expired OTPs
    $cleanup = $db->query("DELETE FROM otp_codes WHERE expires_at < NOW()");
    echo "✓ Cleaned up expired OTPs\n";
    
    // Show current OTP count
    $count = $db->query("SELECT COUNT(*) as count FROM otp_codes");
    $count_row = $count->fetch_assoc();
    echo "Current OTP codes in database: " . $count_row['count'] . "\n";
    
    echo "\n✅ OTP table update complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
