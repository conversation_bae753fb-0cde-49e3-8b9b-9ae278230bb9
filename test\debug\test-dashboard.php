<?php
/**
 * Test Dashboard - Simple version to debug issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Test Dashboard</title></head><body>";
echo "<h1>Dashboard Test</h1>";

try {
    echo "<p>✅ PHP is working</p>";
    
    // Test config include
    echo "<p>Testing config include...</p>";
    require_once '../../config/config.php';
    echo "<p>✅ Config loaded</p>";
    
    // Test database connection
    echo "<p>Testing database connection...</p>";
    $db = getDB();
    echo "<p>✅ Database connected</p>";
    
    // Test session
    echo "<p>Testing session...</p>";
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "<p>✅ Session started</p>";
    
    // Test user authentication check
    echo "<p>Testing authentication...</p>";
    if (isset($_SESSION['user_id'])) {
        echo "<p>✅ User is logged in (ID: " . $_SESSION['user_id'] . ")</p>";
        
        // Get user details
        $user_query = "SELECT * FROM accounts WHERE id = ?";
        $user_result = $db->query($user_query, [$_SESSION['user_id']]);
        
        if ($user = $user_result->fetch_assoc()) {
            echo "<p>✅ User data loaded: " . $user['first_name'] . " " . $user['last_name'] . "</p>";
        } else {
            echo "<p>❌ User data not found</p>";
        }
    } else {
        echo "<p>⚠️ User not logged in</p>";
        echo "<p><a href='../../login.php'>Login here</a></p>";
    }
    
    echo "<h2>System Information</h2>";
    echo "<p>PHP Version: " . PHP_VERSION . "</p>";
    echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
    echo "<p>Session ID: " . session_id() . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
