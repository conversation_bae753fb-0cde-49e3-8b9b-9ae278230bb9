<?php
/**
 * Layout Test Page
 * Test the footer layout fixes
 */

// Set page variables
$page_title = 'Layout Test';
$current_page = 'test';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$user_id = $_SESSION['user_id'];
$db = getDB();
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

// Get site settings
$site_name = getBankName();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . $site_name; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/user-dashboard.css">
    
    <!-- Dynamic CSS -->
    <style><?php echo getDynamicCSS(); ?></style>
</head>
<body>
    <!-- Include Header -->
    <?php include __DIR__ . '/../../user/shared/user_header.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../../user/shared/sidebar.php'; ?>
        
        <!-- Content Area -->
        <div class="content-area">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h2><i class="fas fa-vial"></i> Layout Test Page</h2>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h4><i class="fas fa-info-circle"></i> Layout Test</h4>
                                    <p>This page is used to test the layout and footer positioning fixes.</p>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>User Information</h5>
                                        <ul class="list-group">
                                            <li class="list-group-item">Name: <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></li>
                                            <li class="list-group-item">Username: <?php echo htmlspecialchars($user['username']); ?></li>
                                            <li class="list-group-item">Account: <?php echo htmlspecialchars($user['account_number']); ?></li>
                                            <li class="list-group-item">Balance: $<?php echo number_format($user['balance'], 2); ?></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>System Information</h5>
                                        <ul class="list-group">
                                            <li class="list-group-item">PHP Version: <?php echo PHP_VERSION; ?></li>
                                            <li class="list-group-item">Current Time: <?php echo date('Y-m-d H:i:s'); ?></li>
                                            <li class="list-group-item">Session ID: <?php echo session_id(); ?></li>
                                            <li class="list-group-item">Site Name: <?php echo htmlspecialchars($site_name); ?></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <h5>Test Content</h5>
                                    <p>This is test content to verify that the layout is working correctly and the footer is positioned properly.</p>
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                                    
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle"></i> If you can see this content and the footer is at the bottom of the page, the layout is working correctly.
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <a href="../../dashboard/" class="btn btn-primary">
                                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Include Footer -->
    <?php include __DIR__ . '/../../user/shared/user_footer.php'; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
