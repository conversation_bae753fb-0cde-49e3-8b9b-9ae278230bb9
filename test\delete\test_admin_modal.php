<?php
// Test admin modal functionality with session simulation
session_start();

// Simulate admin session for testing
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'test_admin';
$_SESSION['admin_name'] = 'Test Administrator';
$_SESSION['admin_role'] = 'super_admin';
$_SESSION['admin_login_time'] = time();

// Include config to test authentication
require_once 'config/config.php';

echo "<h2>Admin Modal Test Results</h2>";

// Test 1: Check if admin session is valid
echo "<h3>1. Admin Session Test</h3>";
if (isAdminLoggedIn()) {
    echo "<div style='color: green;'>✓ Admin session is valid</div>";
} else {
    echo "<div style='color: red;'>✗ Admin session is invalid</div>";
}

// Test 2: Test AJAX endpoint directly
echo "<h3>2. AJAX Endpoint Test</h3>";
$test_url = "http://localhost:8083/admin/ajax/get-deposit-details.php?id=1";

// Create a context with session cookies
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => "Cookie: " . session_name() . "=" . session_id() . "\r\n"
    ]
]);

$response = @file_get_contents($test_url, false, $context);
if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<div style='color: green;'>✓ AJAX endpoint working correctly</div>";
        echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
    } else {
        echo "<div style='color: red;'>✗ AJAX endpoint returned error: " . ($data['message'] ?? 'Unknown error') . "</div>";
    }
} else {
    echo "<div style='color: red;'>✗ Could not connect to AJAX endpoint</div>";
}

// Test 3: Check if crypto-deposits.php loads
echo "<h3>3. Main Page Test</h3>";
$main_url = "http://localhost:8083/admin/crypto-deposits.php";
$main_context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => "Cookie: " . session_name() . "=" . session_id() . "\r\n"
    ]
]);

$main_response = @file_get_contents($main_url, false, $main_context);
if ($main_response && strpos($main_response, 'viewDeposit') !== false) {
    echo "<div style='color: green;'>✓ Main crypto-deposits page loads and contains viewDeposit function</div>";
} else {
    echo "<div style='color: red;'>✗ Main crypto-deposits page has issues</div>";
}

// Test 4: Check database connection and data
echo "<h3>4. Database Test</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=online_banking", "root", "root");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM crypto_deposits WHERE id = 1");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo "<div style='color: green;'>✓ Test deposit (ID: 1) exists in database</div>";
    } else {
        echo "<div style='color: orange;'>⚠ Test deposit (ID: 1) not found in database</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Database connection error: " . $e->getMessage() . "</div>";
}

echo "<h3>5. Modal Integration Test</h3>";
?>
<!DOCTYPE html>
<html>
<head>
    <title>Modal Integration Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-3">
        <button class="btn btn-primary" onclick="testModalFunction()">Test Modal Function</button>
        <div id="testResult" class="mt-3"></div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="depositModalLabel">Deposit Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="depositModalBody">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testModalFunction() {
            document.getElementById('testResult').innerHTML = '<div class="alert alert-info">Testing modal function...</div>';
            
            // Use the same viewDeposit function as in crypto-deposits.php
            viewDeposit(1);
        }

        function viewDeposit(depositId) {
            fetch(`admin/ajax/get-deposit-details.php?id=${depositId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayDepositDetails(data.data);
                        document.getElementById('testResult').innerHTML = '<div class="alert alert-success">✓ Modal function working correctly!</div>';
                    } else {
                        showError(data.message || 'Failed to load deposit details');
                        document.getElementById('testResult').innerHTML = '<div class="alert alert-danger">✗ Modal function failed: ' + (data.message || 'Unknown error') + '</div>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('Network error occurred while loading deposit details');
                    document.getElementById('testResult').innerHTML = '<div class="alert alert-danger">✗ Network error: ' + error.message + '</div>';
                });
        }

        function displayDepositDetails(data) {
            const { user, deposit, transaction, admin_notes } = data;
            
            const reviewedInfo = deposit.reviewed_by ? `
                <div class="row mb-2">
                    <div class="col-sm-3"><strong>Reviewed By:</strong></div>
                    <div class="col-sm-9">${escapeHtml(deposit.reviewed_by)} on ${formatDateTime(deposit.reviewed_at)}</div>
                </div>
            ` : '';

            const creditedInfo = deposit.credited_at ? `
                <div class="row mb-2">
                    <div class="col-sm-3"><strong>Credited At:</strong></div>
                    <div class="col-sm-9">${formatDateTime(deposit.credited_at)}</div>
                </div>
            ` : '';

            const modalContent = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">User Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Name:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.name)}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Username:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.username)}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Email:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.email)}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Account:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.account_number)}</div>
                                </div>
                                ${user.phone ? `
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Phone:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.phone)}</div>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header" style="background-color: ${getStatusColor(deposit.status)}; color: white;">
                                <h6 class="mb-0">Deposit Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Amount:</strong></div>
                                    <div class="col-sm-8">${parseFloat(deposit.amount).toFixed(8)} ${escapeHtml(deposit.cryptocurrency)}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Status:</strong></div>
                                    <div class="col-sm-8">
                                        <span class="badge" style="background-color: ${getStatusColor(deposit.status)};">
                                            ${escapeHtml(deposit.status)}
                                        </span>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Receipt:</strong></div>
                                    <div class="col-sm-8">
                                        ${deposit.receipt_file ? 
                                            `<a href="uploads/receipts/${escapeHtml(deposit.receipt_file)}" target="_blank" class="btn btn-sm btn-outline-primary">View Receipt</a>` : 
                                            'No receipt uploaded'
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">Transaction Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Admin Wallet:</strong></div>
                            <div class="col-sm-9">
                                <code class="small text-break">${escapeHtml(deposit.admin_wallet_address || 'N/A')}</code>
                            </div>
                        </div>
                        ${deposit.user_wallet_address ? `
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>User Wallet:</strong></div>
                            <div class="col-sm-9">
                                <code class="small text-break">${escapeHtml(deposit.user_wallet_address)}</code>
                            </div>
                        </div>
                        ` : ''}
                        ${deposit.transaction_hash ? `
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Transaction Hash:</strong></div>
                            <div class="col-sm-9">
                                <code class="small text-break">${escapeHtml(deposit.transaction_hash)}</code>
                            </div>
                        </div>
                        ` : ''}
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>USD Equivalent:</strong></div>
                            <div class="col-sm-9">$${parseFloat(deposit.usd_equivalent || 0).toFixed(2)}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Exchange Rate:</strong></div>
                            <div class="col-sm-9">${parseFloat(deposit.exchange_rate || 0).toFixed(8)}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Created At:</strong></div>
                            <div class="col-sm-9">${formatDateTime(transaction.created_at)}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Last Updated:</strong></div>
                            <div class="col-sm-9">${formatDateTime(transaction.updated_at)}</div>
                        </div>
                        ${reviewedInfo}
                        ${creditedInfo}
                    </div>
                </div>
                ${admin_notes && admin_notes.length > 0 ? `
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">Admin Notes</h6>
                    </div>
                    <div class="card-body">
                        ${admin_notes.map(note => `
                            <div class="border-bottom pb-2 mb-2">
                                <small class="text-muted">${formatDateTime(note.created_at)} by ${escapeHtml(note.admin_name)}</small>
                                <div>${escapeHtml(note.note)}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            `;

            document.getElementById('depositModalBody').innerHTML = modalContent;
            new bootstrap.Modal(document.getElementById('depositModal')).show();
        }

        function showError(message) {
            document.getElementById('depositModalBody').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> ${escapeHtml(message)}
                </div>
            `;
            new bootstrap.Modal(document.getElementById('depositModal')).show();
        }

        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleString();
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function getStatusColor(status) {
            const colors = {
                'pending': '#ffc107',
                'approved': '#28a745',
                'rejected': '#dc3545',
                'processing': '#17a2b8'
            };
            return colors[status?.toLowerCase()] || '#6c757d';
        }
    </script>
</body>
</html>