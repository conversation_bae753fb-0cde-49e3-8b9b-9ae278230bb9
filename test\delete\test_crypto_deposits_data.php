<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "<h2>Testing Crypto Deposits Data</h2>";
    
    // Check if there are any crypto deposits
    $count_query = "SELECT COUNT(*) as total FROM crypto_deposits";
    $count_result = $db->query($count_query);
    $count = $count_result->fetch_assoc()['total'];
    
    echo "<p>Total crypto deposits: $count</p>";
    
    if ($count > 0) {
        // Get first few deposits
        $deposits_query = "SELECT cd.*, a.first_name, a.last_name, a.username 
                          FROM crypto_deposits cd 
                          LEFT JOIN accounts a ON cd.account_id = a.id 
                          LIMIT 5";
        $deposits_result = $db->query($deposits_query);
        
        echo "<h3>Sample Deposits:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>User</th><th>Cryptocurrency</th><th>Amount</th><th>Status</th><th>Created</th></tr>";
        
        while ($deposit = $deposits_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($deposit['id']) . "</td>";
            echo "<td>" . htmlspecialchars($deposit['first_name'] . ' ' . $deposit['last_name']) . "</td>";
            echo "<td>" . htmlspecialchars($deposit['cryptocurrency'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($deposit['deposit_amount'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($deposit['status'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($deposit['created_at'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No crypto deposits found. Let's create a sample deposit for testing.</p>";
        
        // Create a sample deposit for testing
        $insert_query = "INSERT INTO crypto_deposits (account_id, cryptocurrency, deposit_amount, deposit_number, wallet_address, status, created_at) 
                        VALUES (1, 'Bitcoin', 0.001, 'DEP001', '**********************************', 'pending', NOW())";
        
        if ($db->query($insert_query)) {
            echo "<p>✅ Sample deposit created successfully!</p>";
            
            // Get the created deposit
            $new_deposit_query = "SELECT cd.*, a.first_name, a.last_name, a.username 
                                 FROM crypto_deposits cd 
                                 LEFT JOIN accounts a ON cd.account_id = a.id 
                                 WHERE cd.id = LAST_INSERT_ID()";
            $new_deposit_result = $db->query($new_deposit_query);
            $new_deposit = $new_deposit_result->fetch_assoc();
            
            echo "<h3>Created Sample Deposit:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>User</th><th>Cryptocurrency</th><th>Amount</th><th>Status</th><th>Created</th></tr>";
            echo "<tr>";
            echo "<td>" . htmlspecialchars($new_deposit['id']) . "</td>";
            echo "<td>" . htmlspecialchars($new_deposit['first_name'] . ' ' . $new_deposit['last_name']) . "</td>";
            echo "<td>" . htmlspecialchars($new_deposit['cryptocurrency']) . "</td>";
            echo "<td>" . htmlspecialchars($new_deposit['deposit_amount']) . "</td>";
            echo "<td>" . htmlspecialchars($new_deposit['status']) . "</td>";
            echo "<td>" . htmlspecialchars($new_deposit['created_at']) . "</td>";
            echo "</tr>";
            echo "</table>";
        } else {
            echo "<p>❌ Failed to create sample deposit</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>