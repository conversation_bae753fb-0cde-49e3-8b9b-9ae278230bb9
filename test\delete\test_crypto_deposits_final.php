<?php
// Final test to verify all crypto deposits fixes
require_once 'config/database.php';

echo "<!DOCTYPE html><html><head><title>Crypto Deposits Final Fix Test</title></head><body>";
echo "<h2>Testing All Crypto Deposits Fixes</h2>";

try {
    // Get database connection
    $db = getDB();
    
    // Test 1: Check table structure
    echo "<h3>Test 1: Table Structure</h3>";
    $result = $db->query("DESCRIBE crypto_deposits");
    if ($result) {
        echo "<p style='color: green;'>✅ crypto_deposits table exists</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ crypto_deposits table does not exist</p>";
    }

    // Test 2: Test the fixed queries
    echo "<h3>Test 2: Query Tests</h3>";
    
    // Test cryptocurrency filter (was crypto_type)
    try {
        $query = "SELECT COUNT(*) as count FROM crypto_deposits WHERE cryptocurrency = 'BTC'";
        $result = $db->query($query);
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<p style='color: green;'>✅ Cryptocurrency filter query works (found $count BTC deposits)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Cryptocurrency filter query failed: " . $e->getMessage() . "</p>";
    }
    
    // Test deposit_amount column (was amount)
    try {
        $query = "SELECT SUM(deposit_amount) as total FROM crypto_deposits WHERE status = 'approved'";
        $result = $db->query($query);
        if ($result) {
            $total = $result->fetch_assoc()['total'] ?? 0;
            echo "<p style='color: green;'>✅ deposit_amount column works (total approved: $total)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ deposit_amount query failed: " . $e->getMessage() . "</p>";
    }
    
    // Test deposit_number column (was deposit_reference)
    try {
        $query = "SELECT deposit_number FROM crypto_deposits LIMIT 1";
        $result = $db->query($query);
        if ($result) {
            $row = $result->fetch_assoc();
            $deposit_number = $row['deposit_number'] ?? 'N/A';
            echo "<p style='color: green;'>✅ deposit_number column works (sample: $deposit_number)</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ deposit_number query failed: " . $e->getMessage() . "</p>";
    }
    
    // Test 3: Verify old broken queries fail
    echo "<h3>Test 3: Verify Old Broken Queries Fail (Expected)</h3>";
    
    try {
        $query = "SELECT amount FROM crypto_deposits LIMIT 1";
        $result = $db->query($query);
        echo "<p style='color: orange;'>⚠️ Old 'amount' column still exists (should be removed)</p>";
    } catch (Exception $e) {
        echo "<p style='color: green;'>✅ Old 'amount' column properly removed: " . $e->getMessage() . "</p>";
    }
    
    try {
        $query = "SELECT crypto_type FROM crypto_deposits LIMIT 1";
        $result = $db->query($query);
        echo "<p style='color: orange;'>⚠️ Old 'crypto_type' column still exists (should be 'cryptocurrency')</p>";
    } catch (Exception $e) {
        echo "<p style='color: green;'>✅ Old 'crypto_type' column properly doesn't exist: " . $e->getMessage() . "</p>";
    }
    
    try {
        $query = "SELECT deposit_reference FROM crypto_deposits LIMIT 1";
        $result = $db->query($query);
        echo "<p style='color: orange;'>⚠️ Old 'deposit_reference' column still exists (should be 'deposit_number')</p>";
    } catch (Exception $e) {
        echo "<p style='color: green;'>✅ Old 'deposit_reference' column properly doesn't exist: " . $e->getMessage() . "</p>";
    }
    
    try {
        $query = "SELECT deposit_method FROM crypto_deposits LIMIT 1";
        $result = $db->query($query);
        echo "<p style='color: orange;'>⚠️ 'deposit_method' column exists but not in schema</p>";
    } catch (Exception $e) {
        echo "<p style='color: green;'>✅ 'deposit_method' column properly doesn't exist: " . $e->getMessage() . "</p>";
    }
    
    echo "<h3>Summary</h3>";
    echo "<p>All fixes have been applied to handle the undefined array key errors:</p>";
    echo "<ul>";
    echo "<li>✅ Fixed 'crypto_type' → 'cryptocurrency'</li>";
    echo "<li>✅ Fixed 'deposit_reference' → 'deposit_number'</li>";
    echo "<li>✅ Fixed 'amount' → 'deposit_amount'</li>";
    echo "<li>✅ Removed non-existent 'deposit_method' references</li>";
    echo "<li>✅ Added null coalescing operators (??) to prevent null value warnings</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>