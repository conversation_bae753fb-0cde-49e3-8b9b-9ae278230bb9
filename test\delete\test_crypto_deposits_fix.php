<?php
// Test script to verify crypto deposits database fixes
require_once 'admin/includes/config.php';

echo "<h2>Testing Crypto Deposits Database Fixes</h2>\n";

try {
    // Test 1: Check if crypto_deposits table exists and has correct columns
    echo "<h3>Test 1: Database Structure</h3>\n";
    $result = $db->query("DESCRIBE crypto_deposits");
    if ($result) {
        echo "<p>✅ crypto_deposits table exists</p>\n";
        echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>❌ crypto_deposits table does not exist</p>\n";
    }

    // Test 2: Test the statistics query from crypto-deposits.php
    echo "<h3>Test 2: Statistics Query</h3>\n";
    $stats_query = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status = 'declined' THEN 1 ELSE 0 END) as declined,
        SUM(CASE WHEN status = 'approved' THEN deposit_amount ELSE 0 END) as total_approved_amount
        FROM crypto_deposits";
    
    $result = $db->query($stats_query);
    if ($result) {
        $stats = $result->fetch_assoc();
        echo "<p>✅ Statistics query executed successfully</p>\n";
        echo "<ul>\n";
        echo "<li>Total deposits: " . $stats['total'] . "</li>\n";
        echo "<li>Pending: " . $stats['pending'] . "</li>\n";
        echo "<li>Approved: " . $stats['approved'] . "</li>\n";
        echo "<li>Declined: " . $stats['declined'] . "</li>\n";
        echo "<li>Total approved amount: " . $stats['total_approved_amount'] . "</li>\n";
        echo "</ul>\n";
    } else {
        echo "<p>❌ Statistics query failed: " . $db->error . "</p>\n";
    }

    // Test 3: Test the main deposits query
    echo "<h3>Test 3: Main Deposits Query</h3>\n";
    $deposits_query = "SELECT cd.*, u.first_name, u.last_name, u.email, cw.wallet_address, cw.cryptocurrency as wallet_crypto
                       FROM crypto_deposits cd
                       LEFT JOIN users u ON cd.account_id = u.id
                       LEFT JOIN crypto_wallets cw ON cd.wallet_id = cw.id
                       ORDER BY cd.created_at DESC LIMIT 5";
    
    $result = $db->query($deposits_query);
    if ($result) {
        echo "<p>✅ Main deposits query executed successfully</p>\n";
        $count = 0;
        while ($row = $result->fetch_assoc()) {
            $count++;
            echo "<p>Deposit #$count:</p>\n";
            echo "<ul>\n";
            echo "<li>ID: " . $row['id'] . "</li>\n";
            echo "<li>User: " . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . "</li>\n";
            echo "<li>Amount: " . number_format($row['deposit_amount'], 8) . " " . $row['crypto_type'] . "</li>\n";
            echo "<li>Status: " . $row['status'] . "</li>\n";
            echo "<li>Created: " . $row['created_at'] . "</li>\n";
            echo "</ul>\n";
        }
        if ($count == 0) {
            echo "<p>No deposits found in database</p>\n";
        }
    } else {
        echo "<p>❌ Main deposits query failed: " . $db->error . "</p>\n";
    }

    // Test 4: Test count query
    echo "<h3>Test 4: Count Query</h3>\n";
    $count_query = "SELECT COUNT(*) as total FROM crypto_deposits cd";
    $result = $db->query($count_query);
    if ($result) {
        $count_data = $result->fetch_assoc();
        echo "<p>✅ Count query executed successfully</p>\n";
        echo "<p>Total records: " . $count_data['total'] . "</p>\n";
    } else {
        echo "<p>❌ Count query failed: " . $db->error . "</p>\n";
    }

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>\n";
}

echo "<h3>Summary</h3>\n";
echo "<p>If all tests show ✅, then the crypto deposits fixes are working correctly!</p>\n";
?>