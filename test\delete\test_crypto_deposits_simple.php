<?php
// Simple test to verify crypto deposits column fix
require_once 'config/database.php';

echo "<!DOCTYPE html><html><head><title>Crypto Deposits Column Fix Test</title></head><body>";
echo "<h2>Testing Crypto Deposits Column Fix</h2>";

try {
    // Get database connection
    $db = getDB();
    
    // Test 1: Check if crypto_deposits table exists and has correct columns
    echo "<h3>Test 1: Table Structure</h3>";
    $result = $db->query("DESCRIBE crypto_deposits");
    if ($result) {
        echo "<p style='color: green;'>✅ SUCCESS: crypto_deposits table exists</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        $has_deposit_amount = false;
        $has_amount = false;
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td style='padding: 5px;'>" . htmlspecialchars($value ?? '') . "</td>";
            }
            echo "</tr>";
            if ($row['Field'] === 'deposit_amount') {
                $has_deposit_amount = true;
            }
            if ($row['Field'] === 'amount') {
                $has_amount = true;
            }
        }
        echo "</table>";
        
        if ($has_deposit_amount && !$has_amount) {
            echo "<p style='color: green;'>✅ CORRECT: Table has 'deposit_amount' column (not 'amount')</p>";
        } elseif ($has_amount && !$has_deposit_amount) {
            echo "<p style='color: red;'>❌ INCORRECT: Table has 'amount' column (should be 'deposit_amount')</p>";
        } elseif ($has_amount && $has_deposit_amount) {
            echo "<p style='color: orange;'>⚠️ WARNING: Table has both 'amount' and 'deposit_amount' columns</p>";
        } else {
            echo "<p style='color: red;'>❌ ERROR: Table has neither 'amount' nor 'deposit_amount' column</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ FAILED: crypto_deposits table does not exist</p>";
    }

    // Test 2: Test the fixed statistics query (the one that was failing before)
    echo "<h3>Test 2: Statistics Query (Previously Failing with 'Unknown column amount')</h3>";
    $stats_query = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status = 'declined' THEN 1 ELSE 0 END) as declined,
        SUM(CASE WHEN status = 'approved' THEN deposit_amount ELSE 0 END) as total_approved_amount
        FROM crypto_deposits";
    
    $result = $db->query($stats_query);
    if ($result) {
        $stats = $result->fetch_assoc();
        echo "<p style='color: green;'>✅ SUCCESS: Statistics query executed without 'Unknown column' error!</p>";
        echo "<p><strong>Query results:</strong></p>";
        echo "<ul>";
        foreach ($stats as $key => $value) {
            echo "<li><strong>$key:</strong> " . ($value ?? '0') . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ FAILED: Statistics query failed</p>";
        echo "<p>Error: " . $db->getConnection()->error . "</p>";
    }

    // Test 3: Test the old query that would fail
    echo "<h3>Test 3: Old Query (Should Fail with 'Unknown column amount')</h3>";
    $old_query = "SELECT 
        SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as total_approved_amount
        FROM crypto_deposits";
    
    $result = $db->query($old_query);
    if ($result) {
        echo "<p style='color: red;'>❌ UNEXPECTED: Old query with 'amount' column succeeded (this shouldn't happen)</p>";
    } else {
        echo "<p style='color: green;'>✅ EXPECTED: Old query with 'amount' column failed as expected</p>";
        echo "<p>Error: " . $db->getConnection()->error . "</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Summary</h3>";
echo "<p>This test verifies that:</p>";
echo "<ol>";
echo "<li>The crypto_deposits table has the correct 'deposit_amount' column</li>";
echo "<li>Our fixed query using 'deposit_amount' works correctly</li>";
echo "<li>The old query using 'amount' fails as expected</li>";
echo "</ol>";
echo "<p>If tests 1 and 2 show ✅ SUCCESS, then the crypto deposits page fix is working!</p>";
echo "</body></html>";
?>