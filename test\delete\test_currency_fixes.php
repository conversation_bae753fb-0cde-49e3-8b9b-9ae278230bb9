<?php
/**
 * Test Currency Fixes
 * Test the currency symbol display and member since date functionality
 */

require_once 'config/config.php';

echo "<h2>Testing Currency Symbol Fixes</h2>";

// Test getCurrencySymbol function
$test_currencies = ['USD', 'EUR', 'ZAR', 'GBP', 'JPY', 'INR', 'NGN'];

echo "<h3>Currency Symbol Tests:</h3>";
foreach ($test_currencies as $currency) {
    $symbol = getCurrencySymbol($currency);
    echo "<p>{$currency}: {$symbol}</p>";
}

// Test formatCurrency function
echo "<h3>Format Currency Tests:</h3>";
foreach ($test_currencies as $currency) {
    $formatted = formatCurrency(1234.56, $currency);
    echo "<p>{$currency}: {$formatted}</p>";
}

// Test database connection and user currency query
try {
    $db = getDB();
    
    echo "<h3>Database Test - User Currencies:</h3>";
    $result = $db->query("SELECT id, first_name, last_name, currency, balance FROM accounts LIMIT 5");
    
    if ($result) {
        while ($user = $result->fetch_assoc()) {
            $formatted_balance = formatCurrency($user['balance'], $user['currency'] ?? 'USD');
            echo "<p>User: {$user['first_name']} {$user['last_name']} - Currency: {$user['currency']} - Balance: {$formatted_balance}</p>";
        }
    }
    
    echo "<h3>Database Schema Test - Check DATETIME columns:</h3>";
    $schema_result = $db->query("DESCRIBE accounts");
    if ($schema_result) {
        while ($column = $schema_result->fetch_assoc()) {
            if (in_array($column['Field'], ['created_at', 'updated_at', 'last_login'])) {
                echo "<p>{$column['Field']}: {$column['Type']} - {$column['Default']}</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p>Database error: " . $e->getMessage() . "</p>";
}

echo "<h3>Test Complete!</h3>";
echo "<p>If you see proper currency symbols (€, R, £, etc.) above, the fixes are working correctly.</p>";
?>
