<?php
require_once 'config/config.php';

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid deposit ID']);
    exit;
}

$deposit_id = intval($_GET['id']);

try {
    $db = getDB();
    
    // Get detailed deposit information
    $query = "SELECT cd.*, 
              a.first_name, a.last_name, a.username, a.email, a.account_number, a.phone,
              admin.first_name as admin_first_name, admin.last_name as admin_last_name
              FROM crypto_deposits cd 
              LEFT JOIN accounts a ON cd.account_id = a.id 
              LEFT JOIN accounts admin ON cd.reviewed_by = admin.id 
              WHERE cd.id = ?";
    
    $result = $db->query($query, [$deposit_id]);
    $deposit = $result->fetch_assoc();
    
    if (!$deposit) {
        http_response_code(404);
        echo json_encode(['error' => 'Deposit not found']);
        exit;
    }
    
    // Format the response
    $response = [
        'success' => true,
        'deposit' => [
            'id' => $deposit['id'],
            'user' => [
                'name' => $deposit['first_name'] . ' ' . $deposit['last_name'],
                'username' => $deposit['username'],
                'email' => $deposit['email'],
                'account_number' => $deposit['account_number'],
                'phone' => $deposit['phone']
            ],
            'cryptocurrency' => $deposit['cryptocurrency'],
            'deposit_amount' => $deposit['deposit_amount'],
            'deposit_number' => $deposit['deposit_number'],
            'wallet_address' => $deposit['wallet_address'],
            'status' => $deposit['status'],
            'admin_notes' => $deposit['admin_notes'],
            'created_at' => $deposit['created_at'],
            'updated_at' => $deposit['updated_at'],
            'reviewed_at' => $deposit['reviewed_at'],
            'reviewed_by' => $deposit['admin_first_name'] ? 
                $deposit['admin_first_name'] . ' ' . $deposit['admin_last_name'] : null
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch deposit details: ' . $e->getMessage()]);
}
?>