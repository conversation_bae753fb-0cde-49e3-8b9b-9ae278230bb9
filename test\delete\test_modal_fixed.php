<?php
// Test the fixed modal functionality
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixed Modal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h2>Test Fixed Modal Functionality</h2>
        <button class="btn btn-primary" onclick="testModalWithAjax()">Test Modal with AJAX Endpoint</button>
        <button class="btn btn-success" onclick="testModalWithDirect()">Test Modal with Direct Endpoint</button>
        
        <div id="result" class="mt-3"></div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="depositModalLabel">Deposit Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="depositModalBody">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testModalWithAjax() {
            document.getElementById('result').innerHTML = '<div class="alert alert-info">Testing AJAX endpoint...</div>';
            
            fetch('ajax/get-deposit-details.php?id=1')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayDepositDetails(data.data);
                        document.getElementById('result').innerHTML = '<div class="alert alert-success">AJAX endpoint test successful!</div>';
                    } else {
                        document.getElementById('result').innerHTML = '<div class="alert alert-danger">AJAX endpoint error: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
                });
        }

        function testModalWithDirect() {
            document.getElementById('result').innerHTML = '<div class="alert alert-info">Testing direct endpoint...</div>';
            
            fetch('test_get_deposit_direct.php?id=1')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Convert direct format to expected format
                        const convertedData = {
                            user: data.data.user,
                            deposit: data.data.deposit,
                            transaction: data.data.transaction || data.data.deposit,
                            admin_notes: data.data.admin_notes || []
                        };
                        displayDepositDetails(convertedData);
                        document.getElementById('result').innerHTML = '<div class="alert alert-success">Direct endpoint test successful!</div>';
                    } else {
                        document.getElementById('result').innerHTML = '<div class="alert alert-danger">Direct endpoint error: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('result').innerHTML = '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
                });
        }

        function displayDepositDetails(data) {
            const { user, deposit, transaction, admin_notes } = data;
            
            const reviewedInfo = deposit.reviewed_by ? `
                <div class="row mb-2">
                    <div class="col-sm-3"><strong>Reviewed By:</strong></div>
                    <div class="col-sm-9">${escapeHtml(deposit.reviewed_by)} on ${formatDateTime(deposit.reviewed_at)}</div>
                </div>
            ` : '';

            const creditedInfo = deposit.credited_at ? `
                <div class="row mb-2">
                    <div class="col-sm-3"><strong>Credited At:</strong></div>
                    <div class="col-sm-9">${formatDateTime(deposit.credited_at)}</div>
                </div>
            ` : '';

            const modalContent = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">User Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Name:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.name)}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Username:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.username)}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Email:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.email)}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Account:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.account_number)}</div>
                                </div>
                                ${user.phone ? `
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Phone:</strong></div>
                                    <div class="col-sm-8">${escapeHtml(user.phone)}</div>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header" style="background-color: ${getStatusColor(deposit.status)}; color: white;">
                                <h6 class="mb-0">Deposit Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Amount:</strong></div>
                                    <div class="col-sm-8">${parseFloat(deposit.amount).toFixed(8)} ${escapeHtml(deposit.cryptocurrency)}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Status:</strong></div>
                                    <div class="col-sm-8">
                                        <span class="badge" style="background-color: ${getStatusColor(deposit.status)};">
                                            ${escapeHtml(deposit.status)}
                                        </span>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-sm-4"><strong>Receipt:</strong></div>
                                    <div class="col-sm-8">
                                        ${deposit.receipt_file ? 
                                            `<a href="uploads/receipts/${escapeHtml(deposit.receipt_file)}" target="_blank" class="btn btn-sm btn-outline-primary">View Receipt</a>` : 
                                            'No receipt uploaded'
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">Transaction Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Admin Wallet:</strong></div>
                            <div class="col-sm-9">
                                <code class="small text-break">${escapeHtml(deposit.admin_wallet_address || 'N/A')}</code>
                            </div>
                        </div>
                        ${deposit.user_wallet_address ? `
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>User Wallet:</strong></div>
                            <div class="col-sm-9">
                                <code class="small text-break">${escapeHtml(deposit.user_wallet_address)}</code>
                            </div>
                        </div>
                        ` : ''}
                        ${deposit.transaction_hash ? `
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Transaction Hash:</strong></div>
                            <div class="col-sm-9">
                                <code class="small text-break">${escapeHtml(deposit.transaction_hash)}</code>
                            </div>
                        </div>
                        ` : ''}
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>USD Equivalent:</strong></div>
                            <div class="col-sm-9">$${parseFloat(deposit.usd_equivalent || 0).toFixed(2)}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Exchange Rate:</strong></div>
                            <div class="col-sm-9">${parseFloat(deposit.exchange_rate || 0).toFixed(8)}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Created At:</strong></div>
                            <div class="col-sm-9">${formatDateTime(transaction.created_at)}</div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-3"><strong>Last Updated:</strong></div>
                            <div class="col-sm-9">${formatDateTime(transaction.updated_at)}</div>
                        </div>
                        ${reviewedInfo}
                        ${creditedInfo}
                    </div>
                </div>
                ${admin_notes && admin_notes.length > 0 ? `
                <div class="card mt-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">Admin Notes</h6>
                    </div>
                    <div class="card-body">
                        ${admin_notes.map(note => `
                            <div class="border-bottom pb-2 mb-2">
                                <small class="text-muted">${formatDateTime(note.created_at)} by ${escapeHtml(note.admin_name)}</small>
                                <div>${escapeHtml(note.note)}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            `;

            document.getElementById('depositModalBody').innerHTML = modalContent;
            new bootstrap.Modal(document.getElementById('depositModal')).show();
        }

        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleString();
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function getStatusColor(status) {
            const colors = {
                'pending': '#ffc107',
                'approved': '#28a745',
                'rejected': '#dc3545',
                'processing': '#17a2b8'
            };
            return colors[status?.toLowerCase()] || '#6c757d';
        }
    </script>
</body>
</html>