<?php
require_once 'config/config.php';

// Simple test without admin authentication
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Functionality</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Crypto Deposit Modal</h2>
        
        <button type="button" class="btn btn-primary" onclick="viewDeposit(1)">
            View Deposit ID: 1
        </button>
        
        <!-- Deposit Details Modal -->
        <div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="depositModalLabel">
                            <i class="fas fa-eye me-2"></i>Deposit Details
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="depositModalBody">
                        <!-- Content will be loaded here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewDeposit(depositId) {
            const modal = new bootstrap.Modal(document.getElementById('depositModal'));
            const modalBody = document.getElementById('depositModalBody');
            
            // Show loading state
            modalBody.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">Loading deposit details...</p></div>';
            modal.show();
            
            // Test direct database query instead of AJAX
            fetch('test_get_deposit_direct.php?id=' + depositId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayDepositDetails(data.deposit);
                    } else {
                        showError(data.error || 'Failed to load deposit details');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('Network error occurred');
                });
        }
        
        function displayDepositDetails(deposit) {
            const modalBody = document.getElementById('depositModalBody');
            
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="fas fa-user me-2"></i>User Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Name:</strong></td><td>${escapeHtml(deposit.user.name)}</td></tr>
                            <tr><td><strong>Username:</strong></td><td>${escapeHtml(deposit.user.username)}</td></tr>
                            <tr><td><strong>Email:</strong></td><td>${escapeHtml(deposit.user.email)}</td></tr>
                            <tr><td><strong>Account Number:</strong></td><td>${escapeHtml(deposit.user.account_number)}</td></tr>
                            <tr><td><strong>Phone:</strong></td><td>${escapeHtml(deposit.user.phone || 'N/A')}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success mb-3"><i class="fas fa-coins me-2"></i>Deposit Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Deposit ID:</strong></td><td>${escapeHtml(deposit.id)}</td></tr>
                            <tr><td><strong>Cryptocurrency:</strong></td><td>${escapeHtml(deposit.cryptocurrency)}</td></tr>
                            <tr><td><strong>Amount:</strong></td><td>${escapeHtml(deposit.deposit_amount)}</td></tr>
                            <tr><td><strong>Reference:</strong></td><td>${escapeHtml(deposit.deposit_number)}</td></tr>
                            <tr><td><strong>Status:</strong></td><td><span class="badge bg-${getStatusColor(deposit.status)}">${escapeHtml(deposit.status)}</span></td></tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-info mb-3"><i class="fas fa-info-circle me-2"></i>Transaction Details</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Wallet Address:</strong></td><td class="font-monospace">${escapeHtml(deposit.wallet_address || 'N/A')}</td></tr>
                            <tr><td><strong>Created:</strong></td><td>${formatDateTime(deposit.created_at)}</td></tr>
                            <tr><td><strong>Updated:</strong></td><td>${formatDateTime(deposit.updated_at)}</td></tr>
                            ${deposit.reviewed_at ? `<tr><td><strong>Reviewed:</strong></td><td>${formatDateTime(deposit.reviewed_at)}</td></tr>` : ''}
                            ${deposit.reviewed_by ? `<tr><td><strong>Reviewed By:</strong></td><td>${escapeHtml(deposit.reviewed_by)}</td></tr>` : ''}
                        </table>
                    </div>
                </div>
                
                ${deposit.admin_notes ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-warning mb-3"><i class="fas fa-sticky-note me-2"></i>Admin Notes</h6>
                        <div class="alert alert-light">
                            ${escapeHtml(deposit.admin_notes)}
                        </div>
                    </div>
                </div>
                ` : ''}
            `;
        }
        
        function showError(message) {
            const modalBody = document.getElementById('depositModalBody');
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${escapeHtml(message)}
                </div>
            `;
        }
        
        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            return new Date(dateString).toLocaleString();
        }
        
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function getStatusColor(status) {
            switch(status?.toLowerCase()) {
                case 'approved': return 'success';
                case 'pending': return 'warning';
                case 'rejected': return 'danger';
                default: return 'secondary';
            }
        }
    </script>
</body>
</html>