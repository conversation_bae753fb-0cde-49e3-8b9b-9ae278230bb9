<?php
// Admin Flow Smoke Test - checks key admin pages exist and have valid PHP syntax
header('Content-Type: text/plain');

$pages = [
  'admin/index.php',
  'admin/login.php',
  'admin/users.php',
  'admin/add-user.php',
  'admin/transactions.php',
  'admin/settings/index.php',
  'admin/security-settings.php',
  'admin/configure-2fa.php',
  'admin/virtual-cards/index.php',
];

$allOk = true;

echo "=== Admin Flow Smoke Test ===\n";
foreach ($pages as $p) {
    $exists = file_exists(__DIR__ . '/../../' . $p);
    echo ($exists ? "✓" : "✗") . " Exists: $p\n";
    if (!$exists) { $allOk = false; continue; }
    $output = shell_exec("php -l " . escapeshellarg(__DIR__ . '/../../' . $p) . " 2>&1");
    if (strpos($output, 'No syntax errors detected') !== false) {
        echo "   ✓ Syntax OK\n";
    } else {
        echo "   ✗ Syntax issue: $output\n"; $allOk = false;
    }
}

echo "\nOverall: " . ($allOk ? 'PASS' : 'FAIL') . "\n";

