<?php
// Link Verifier - parses nav includes, extracts links, and checks file existence
header('Content-Type: text/plain');

$root = realpath(__DIR__ . '/../../');
$targets = [
    $root . '/includes/sidebar.php' => 'user sidebar (url helper)',
    $root . '/includes/header.php'  => 'user header (url helper)',
    $root . '/admin/includes/admin-sidebar.php' => 'admin sidebar (relative hrefs)'
];

$found = [];
foreach ($targets as $file => $label) {
    if (!file_exists($file)) {
        echo "Missing include: $label ($file)\n";
        continue;
    }
    $content = file_get_contents($file);
    // Capture url('...') patterns
    if (preg_match_all("/url\(\s*['\"]([^'\"]+)['\"]\s*\)/", $content, $m)) {
        foreach ($m[1] as $p) {
            $found[] = $p;
        }
    }
    // Capture href="..." for admin sidebar
    if (preg_match_all('/href\s*=\s*[\"\']([^\"\']+)[\"\']/', $content, $m2)) {
        foreach ($m2[1] as $href) {
            // Skip external links and anchors
            if (preg_match('/^https?:/i', $href) || str_starts_with($href, '#')) continue;
            $found[] = $href;
        }
    }
}

// Normalize and check
$found = array_values(array_unique($found));
$results = [];
foreach ($found as $rel) {
    $relTrim = ltrim($rel, '/');
    $fsPath = $root . DIRECTORY_SEPARATOR . str_replace('/', DIRECTORY_SEPARATOR, $relTrim);
    // If path ends with '/', assume index.php
    if (substr($relTrim, -1) === '/') {
        $fsPath = rtrim($fsPath, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR . 'index.php';
    }
    // If path has no .php and points to a dir, add index.php
    if (!preg_match('/\.(php|html|htm)$/i', $relTrim)) {
        if (is_dir($root . DIRECTORY_SEPARATOR . $relTrim)) {
            $fsPath = $root . DIRECTORY_SEPARATOR . $relTrim . DIRECTORY_SEPARATOR . 'index.php';
        }
    }
    $exists = file_exists($fsPath);
    $results[] = [
        'link' => $rel,
        'resolved' => $fsPath,
        'exists' => $exists,
        'type' => (strpos($rel, 'admin/') === 0 ? 'admin' : 'user')
    ];
}

$adminMissing = 0; $userMissing = 0; $total = count($results);
foreach ($results as $r) {
    $status = $r['exists'] ? '✓' : '✗';
    if (!$r['exists']) { if ($r['type']==='admin') $adminMissing++; else $userMissing++; }
    echo sprintf("%s %-5s -> %s\n", $status, $r['type'], str_replace($root . DIRECTORY_SEPARATOR, '', $r['resolved']));
}

echo "\nSummary:\n";
echo "Total links: $total\n";
echo "Admin missing: $adminMissing\n";
echo "User missing: $userMissing\n";

