<?php
// User Flow Smoke Test - checks key user pages exist and have valid PHP syntax
header('Content-Type: text/plain');

$pages = [
  'index.php',
  'login.php',
  'register.php',
  'dashboard/index.php',
  'dashboard/cards.php',
  'dashboard/payments.php',
  'dashboard/transfers/index.php',
  'dashboard/statements/index.php',
  'dashboard/security/index.php',
];

$allOk = true;

echo "=== User Flow Smoke Test ===\n";
foreach ($pages as $p) {
    $exists = file_exists(__DIR__ . '/../../' . $p);
    echo ($exists ? "✓" : "✗") . " Exists: $p\n";
    if (!$exists) { $allOk = false; continue; }
    $output = shell_exec("php -l " . escapeshellarg(__DIR__ . '/../../' . $p) . " 2>&1");
    if (strpos($output, 'No syntax errors detected') !== false) {
        echo "   ✓ Syntax OK\n";
    } else {
        echo "   ✗ Syntax issue: $output\n"; $allOk = false;
    }
}

echo "\nOverall: " . ($allOk ? 'PASS' : 'FAIL') . "\n";

