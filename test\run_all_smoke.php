<?php
// Run all smoke tests and aggregate results
header('Content-Type: text/plain');

$tests = [
    'Environment' => __DIR__ . '/smoke/environment_smoke.php',
    'Admin Flow'  => __DIR__ . '/flow/admin_flow_smoke.php',
    'User Flow'   => __DIR__ . '/flow/user_flow_smoke.php',
    'Link Check'  => __DIR__ . '/flow/link_verifier.php',
];

$overall = true;

echo "=== Online Banking - Smoke Test Runner ===\n\n";
foreach ($tests as $name => $file) {
    echo "--- $name ---\n";
    if (!file_exists($file)) {
        echo "✗ Missing test: $file\n\n";
        $overall = false;
        continue;
    }
    // Execute via CLI include to preserve output
    ob_start();
    include $file;
    $out = ob_get_clean();
    // If included script sets its own headers or outputs HTML, still print captured text
    echo trim($out) . "\n\n";
    if (stripos($out, 'FAIL') !== false || stripos($out, '✗') !== false) {
        $overall = false;
    }
}

echo "=== Overall Result: " . ($overall ? 'PASS' : 'FAIL') . " ===\n";

