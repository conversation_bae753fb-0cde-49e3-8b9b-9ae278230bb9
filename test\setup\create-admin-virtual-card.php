<?php
require_once '../../config/config.php';

echo "<h2>Create Virtual Card for Admin User</h2>";

try {
    $db = getDB();
    
    // Get admin user details
    $admin_query = "SELECT * FROM accounts WHERE username = 'admin' AND is_admin = 1";
    $admin_result = $db->query($admin_query);
    
    if ($admin = $admin_result->fetch_assoc()) {
        echo "<h3>Admin User Found</h3>";
        echo "ID: " . $admin['id'] . "<br>";
        echo "Username: " . $admin['username'] . "<br>";
        echo "Name: " . $admin['first_name'] . " " . $admin['last_name'] . "<br>";
        echo "Account Number: " . $admin['account_number'] . "<br>";
        
        // Check if admin already has a virtual card
        $card_check = "SELECT * FROM virtual_cards WHERE user_id = ?";
        $card_result = $db->query($card_check, [$admin['id']]);
        
        if ($card_result->num_rows > 0) {
            echo "<h3 style='color: orange;'>Admin Already Has Virtual Card</h3>";
            $card = $card_result->fetch_assoc();
            echo "Card Number: " . $card['card_number'] . "<br>";
            echo "CVV: " . $card['cvv'] . "<br>";
            echo "Expiry: " . $card['expiry_date'] . "<br>";
            echo "Status: " . $card['status'] . "<br>";
            echo "Balance: $" . number_format($card['balance'], 2) . "<br>";
        } else {
            echo "<h3>Creating Virtual Card for Admin</h3>";
            
            // Generate card details
            $card_number = '4532' . str_pad(rand(0, ************), 12, '0', STR_PAD_LEFT);
            $cvv = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT);
            $expiry_date = date('Y-m-d', strtotime('+3 years'));
            $initial_balance = 5000.00;
            
            // Insert virtual card
            $insert_card = "INSERT INTO virtual_cards (user_id, card_number, cvv, expiry_date, balance, status, created_at) VALUES (?, ?, ?, ?, ?, 'active', NOW())";
            $result = $db->query($insert_card, [$admin['id'], $card_number, $cvv, $expiry_date, $initial_balance]);
            
            if ($result) {
                echo "<h3 style='color: green;'>✅ Virtual Card Created Successfully!</h3>";
                echo "Card Number: " . $card_number . "<br>";
                echo "CVV: " . $cvv . "<br>";
                echo "Expiry: " . $expiry_date . "<br>";
                echo "Initial Balance: $" . number_format($initial_balance, 2) . "<br>";
                echo "Status: Active<br>";
                
                // Also update the virtual_card_balance in accounts table if it exists
                try {
                    $update_balance = "UPDATE accounts SET virtual_card_balance = ? WHERE id = ?";
                    $db->query($update_balance, [$initial_balance, $admin['id']]);
                    echo "<br>✅ Account virtual card balance updated<br>";
                } catch (Exception $e) {
                    echo "<br>⚠️ Could not update account virtual card balance (column may not exist)<br>";
                }
                
                echo "<br><div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<strong>Admin can now:</strong><br>";
                echo "• View virtual card in admin dashboard<br>";
                echo "• Make virtual card transactions<br>";
                echo "• Test virtual card functionality<br>";
                echo "• Manage virtual card settings<br>";
                echo "</div>";
                
            } else {
                echo "<h3 style='color: red;'>❌ Failed to Create Virtual Card</h3>";
            }
        }
        
    } else {
        echo "<h3 style='color: red;'>❌ Admin User Not Found</h3>";
        echo "Please ensure there is a user with username 'admin' and is_admin = 1";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error: " . $e->getMessage() . "</h3>";
}

echo "<br><a href='../../admin/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Admin Dashboard</a>";
?>
