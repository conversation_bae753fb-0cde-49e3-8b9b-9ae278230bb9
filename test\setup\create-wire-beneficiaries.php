<?php
/**
 * Create Wire Transfer Beneficiaries System
 * This script creates the wire_beneficiaries table and inserts test data
 */

require_once '../../config/config.php';

try {
    $db = getDB();
    
    echo "<h2>Creating Wire Transfer Beneficiaries System</h2>";
    
    // 1. Create wire_beneficiaries table
    echo "<h3>1. Creating wire_beneficiaries table...</h3>";
    
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS wire_beneficiaries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        account_number VARCHAR(50) NOT NULL,
        routing_number VARCHAR(20) NOT NULL,
        bank_name VARCHAR(255) NOT NULL,
        bank_address TEXT,
        swift_code VARCHAR(20),
        beneficiary_address TEXT,
        purpose VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE
    )";
    
    $result = $db->query($create_table_sql);
    if ($result) {
        echo "✅ wire_beneficiaries table created successfully<br>";
    } else {
        echo "❌ Failed to create wire_beneficiaries table<br>";
    }
    
    // 2. Check if table exists and show structure
    echo "<h3>2. Verifying table structure...</h3>";
    $structure = $db->query("DESCRIBE wire_beneficiaries");
    if ($structure) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. Insert test beneficiaries
    echo "<h3>3. Creating test beneficiaries...</h3>";
    
    // Get some user IDs for testing
    $users = $db->query("SELECT id, username, first_name, last_name FROM accounts WHERE is_admin = 0 LIMIT 3");
    
    $test_beneficiaries = [
        [
            'name' => 'John Smith',
            'account_number' => '**********',
            'routing_number' => '*********',
            'bank_name' => 'Chase Bank',
            'bank_address' => '270 Park Avenue, New York, NY 10017',
            'swift_code' => 'CHASUS33',
            'beneficiary_address' => '123 Main St, New York, NY 10001',
            'purpose' => 'Personal Transfer'
        ],
        [
            'name' => 'Sarah Johnson',
            'account_number' => '**********',
            'routing_number' => '*********',
            'bank_name' => 'Bank of America',
            'bank_address' => '100 N Tryon St, Charlotte, NC 28255',
            'swift_code' => 'BOFAUS3N',
            'beneficiary_address' => '456 Oak Ave, Charlotte, NC 28202',
            'purpose' => 'Business Payment'
        ],
        [
            'name' => 'Michael Brown',
            'account_number' => '**********',
            'routing_number' => '*********',
            'bank_name' => 'Wells Fargo',
            'bank_address' => '420 Montgomery St, San Francisco, CA 94104',
            'swift_code' => 'WFBIUS6S',
            'beneficiary_address' => '789 Pine St, San Francisco, CA 94108',
            'purpose' => 'Investment Transfer'
        ]
    ];
    
    $beneficiary_count = 0;
    while ($user = $users->fetch_assoc()) {
        if ($beneficiary_count < count($test_beneficiaries)) {
            $beneficiary = $test_beneficiaries[$beneficiary_count];
            
            $insert_sql = "INSERT INTO wire_beneficiaries (user_id, name, account_number, routing_number, bank_name, bank_address, swift_code, beneficiary_address, purpose) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $result = $db->query($insert_sql, [
                $user['id'],
                $beneficiary['name'],
                $beneficiary['account_number'],
                $beneficiary['routing_number'],
                $beneficiary['bank_name'],
                $beneficiary['bank_address'],
                $beneficiary['swift_code'],
                $beneficiary['beneficiary_address'],
                $beneficiary['purpose']
            ]);
            
            if ($result) {
                echo "✅ Created beneficiary '{$beneficiary['name']}' for user '{$user['username']}'<br>";
            } else {
                echo "❌ Failed to create beneficiary '{$beneficiary['name']}' for user '{$user['username']}'<br>";
            }
            
            $beneficiary_count++;
        }
    }
    
    // 4. Show created beneficiaries
    echo "<h3>4. Summary of created beneficiaries...</h3>";
    $beneficiaries = $db->query("
        SELECT wb.*, a.username, a.first_name, a.last_name 
        FROM wire_beneficiaries wb 
        JOIN accounts a ON wb.user_id = a.id 
        ORDER BY wb.created_at DESC
    ");
    
    if ($beneficiaries->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>User</th><th>Beneficiary Name</th><th>Account</th><th>Bank</th><th>Purpose</th><th>Created</th></tr>";
        while ($row = $beneficiaries->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['username'] . " (" . $row['first_name'] . " " . $row['last_name'] . ")</td>";
            echo "<td>" . $row['name'] . "</td>";
            echo "<td>" . $row['account_number'] . "</td>";
            echo "<td>" . $row['bank_name'] . "</td>";
            echo "<td>" . $row['purpose'] . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No beneficiaries found.";
    }
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>✅ Wire Transfer Beneficiaries System Setup Complete!</strong><br>";
    echo "Users can now add and manage wire transfer beneficiaries in their dashboard.";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}
?>
