<?php
/**
 * Fix Beneficiaries Ownership Issue
 * Transfer beneficiaries from admin (user_id=1) to correct user
 */

require_once __DIR__ . '/../../config/config.php';

echo "<h2>🔧 Fixing Beneficiaries Ownership Issue</h2>";

try {
    $db = getDB();
    
    echo "<h3>1. Current State Analysis</h3>";
    
    // Check current beneficiaries ownership
    $current_sql = "SELECT b.id, b.name, b.account_number as beneficiary_account, b.user_id,
                           a.username, a.account_number as user_account, a.is_admin, a.first_name, a.last_name
                    FROM beneficiaries b
                    JOIN accounts a ON b.user_id = a.id
                    ORDER BY b.user_id, b.name";
    
    $current_result = $db->query($current_sql);
    
    if ($current_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>Beneficiary ID</th><th>Beneficiary Name</th><th>Beneficiary Account</th><th>Owner User ID</th><th>Owner Username</th><th>Owner Account</th><th>Is Admin</th></tr>";
        
        $admin_beneficiaries = [];
        while ($row = $current_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['name'] . "</td>";
            echo "<td>" . $row['beneficiary_account'] . "</td>";
            echo "<td>" . $row['user_id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['user_account'] . "</td>";
            echo "<td>" . ($row['is_admin'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
            
            if ($row['is_admin']) {
                $admin_beneficiaries[] = $row;
            }
        }
        echo "</table>";
        
        if (!empty($admin_beneficiaries)) {
            echo "<h3>2. Found " . count($admin_beneficiaries) . " beneficiaries owned by admin</h3>";
            
            // Try to match beneficiaries to correct users based on account numbers
            echo "<h3>3. Attempting to fix ownership...</h3>";
            
            foreach ($admin_beneficiaries as $beneficiary) {
                echo "<h4>Processing: " . $beneficiary['name'] . " (Account: " . $beneficiary['beneficiary_account'] . ")</h4>";
                
                // Try to find a user with matching account number
                $match_sql = "SELECT id, username, account_number, first_name, last_name 
                             FROM accounts 
                             WHERE account_number = ? AND is_admin = 0";
                $match_result = $db->query($match_sql, [$beneficiary['beneficiary_account']]);
                
                if ($match_result->num_rows > 0) {
                    $correct_user = $match_result->fetch_assoc();
                    
                    echo "✅ Found matching user: " . $correct_user['username'] . " (" . $correct_user['first_name'] . " " . $correct_user['last_name'] . ")<br>";
                    
                    // Update the beneficiary ownership
                    $update_sql = "UPDATE beneficiaries SET user_id = ? WHERE id = ?";
                    $update_result = $db->query($update_sql, [$correct_user['id'], $beneficiary['id']]);
                    
                    if ($update_result) {
                        echo "✅ Updated beneficiary ownership from admin to " . $correct_user['username'] . "<br>";
                    } else {
                        echo "❌ Failed to update beneficiary ownership<br>";
                    }
                } else {
                    echo "⚠️ No matching user found for account number: " . $beneficiary['beneficiary_account'] . "<br>";
                    echo "   This beneficiary will remain with admin for manual review<br>";
                }
                
                echo "<br>";
            }
        } else {
            echo "<h3>✅ No beneficiaries owned by admin found</h3>";
        }
        
    } else {
        echo "<p>No beneficiaries found in the system.</p>";
    }
    
    // Show final state
    echo "<h3>4. Final State After Fix</h3>";
    $final_result = $db->query($current_sql);
    
    if ($final_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>Beneficiary ID</th><th>Beneficiary Name</th><th>Beneficiary Account</th><th>Owner User ID</th><th>Owner Username</th><th>Owner Account</th><th>Is Admin</th></tr>";
        
        $remaining_admin_beneficiaries = 0;
        while ($row = $final_result->fetch_assoc()) {
            $row_class = $row['is_admin'] ? "style='background-color: #ffcccc;'" : "";
            echo "<tr $row_class>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['name'] . "</td>";
            echo "<td>" . $row['beneficiary_account'] . "</td>";
            echo "<td>" . $row['user_id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['user_account'] . "</td>";
            echo "<td>" . ($row['is_admin'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
            
            if ($row['is_admin']) {
                $remaining_admin_beneficiaries++;
            }
        }
        echo "</table>";
        
        if ($remaining_admin_beneficiaries > 0) {
            echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<strong>⚠️ Warning:</strong> $remaining_admin_beneficiaries beneficiaries still owned by admin (highlighted in red)<br>";
            echo "These require manual review as no matching user accounts were found.";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<strong>✅ Success:</strong> All beneficiaries are now properly assigned to their correct users!";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "<br><a href='../../admin/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Admin Dashboard</a>";
?>
