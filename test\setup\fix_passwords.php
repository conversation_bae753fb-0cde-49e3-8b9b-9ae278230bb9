<?php
/**
 * Fix Password Hashes Script
 * This script updates the password hashes in the database to ensure they work correctly
 */

require_once '../../config/config.php';

echo "<h2>Password Hash Fix Script</h2>";
echo "<hr>";

try {
    $db = getDB();
    
    // Generate correct password hashes
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $user_password = password_hash('user123', PASSWORD_DEFAULT);
    
    echo "<h3>Step 1: Generating New Password Hashes</h3>";
    echo "✅ <strong>Admin password hash:</strong> " . substr($admin_password, 0, 30) . "...<br>";
    echo "✅ <strong>User password hash:</strong> " . substr($user_password, 0, 30) . "...<br>";
    
    echo "<h3>Step 2: Updating Admin Password</h3>";
    $admin_update = $db->query("UPDATE accounts SET password = ? WHERE username = 'admin'", [$admin_password]);
    if ($admin_update) {
        echo "✅ Admin password updated successfully<br>";
    } else {
        echo "❌ Failed to update admin password<br>";
    }
    
    echo "<h3>Step 3: Updating User Passwords</h3>";
    $users_to_update = ['testuser2025', 'jamesbong', 'jamesbong101'];
    
    foreach ($users_to_update as $username) {
        $user_update = $db->query("UPDATE accounts SET password = ? WHERE username = ?", [$user_password, $username]);
        if ($user_update) {
            echo "✅ Password updated for user: <strong>$username</strong><br>";
        } else {
            echo "❌ Failed to update password for user: <strong>$username</strong><br>";
        }
    }
    
    echo "<h3>Step 4: Verification</h3>";
    echo "Testing password verification...<br>";
    
    // Test admin password
    $admin_check = $db->query("SELECT password FROM accounts WHERE username = 'admin'");
    if ($admin_row = $admin_check->fetch_assoc()) {
        if (password_verify('admin123', $admin_row['password'])) {
            echo "✅ Admin password verification: <strong>PASS</strong><br>";
        } else {
            echo "❌ Admin password verification: <strong>FAIL</strong><br>";
        }
    }
    
    // Test user password
    $user_check = $db->query("SELECT password FROM accounts WHERE username = 'testuser2025'");
    if ($user_row = $user_check->fetch_assoc()) {
        if (password_verify('user123', $user_row['password'])) {
            echo "✅ User password verification: <strong>PASS</strong><br>";
        } else {
            echo "❌ User password verification: <strong>FAIL</strong><br>";
        }
    }
    
    echo "<h3>Step 5: Login Credentials Summary</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<strong>Updated Login Credentials:</strong><br><br>";
    echo "<strong>Admin:</strong><br>";
    echo "Username: admin<br>";
    echo "Password: admin123<br><br>";
    echo "<strong>Test Users:</strong><br>";
    echo "Username: testuser2025, jamesbong, jamesbong101<br>";
    echo "Password: user123<br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error: " . $e->getMessage() . "</h3>";
}

echo "<br><a href='../../login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Login</a>";
echo " <a href='../../admin/login.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>Test Admin Login</a>";
?>
