@echo off
echo Connecting to MySQL Database...
echo.

REM Set your MySQL connection details here
set MYSQL_HOST=localhost
set MYSQL_USER=root
set MYSQL_PASSWORD=
set MYSQL_DATABASE=online_banking

REM Try to connect to MySQL
mysql -h %MYSQL_HOST% -u %MYSQL_USER% -p%MYSQL_PASSWORD% %MYSQL_DATABASE%

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Failed to connect to MySQL database.
    echo Please check your connection details and ensure MySQL is running.
    pause
) else (
    echo.
    echo MySQL connection closed.
    pause
)
