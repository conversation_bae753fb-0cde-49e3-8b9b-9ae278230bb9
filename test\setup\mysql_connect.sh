#!/bin/bash

echo "Connecting to MySQL Database..."
echo

# Set your MySQL connection details here
MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASSWORD=""
MYSQL_DATABASE="online_banking"

# Try to connect to MySQL
if [ -z "$MYSQL_PASSWORD" ]; then
    mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" "$MYSQL_DATABASE"
else
    mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE"
fi

if [ $? -ne 0 ]; then
    echo
    echo "Failed to connect to MySQL database."
    echo "Please check your connection details and ensure MySQL is running."
    read -p "Press Enter to continue..."
else
    echo
    echo "MySQL connection closed."
fi
