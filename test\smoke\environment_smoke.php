<?php
// Environment Smoke Test for Online Banking
// Validates PHP, Composer autoload, config files, and DB connectivity
header('Content-Type: text/plain');

echo "=== Environment Smoke Test ===\n";
$ok = true;

// 1) PHP version
$phpVersion = phpversion();
echo "PHP Version: {$phpVersion}\n";
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "✓ PHP >= 7.4\n";
} else {
    echo "✗ PHP version too low (need 7.4+)\n"; $ok = false;
}

// 2) Composer autoloader
$autoloadPath = __DIR__ . '/../../vendor/autoload.php';
if (file_exists($autoloadPath)) {
    require_once $autoloadPath;
    echo "✓ Composer autoload found\n";
} else {
    echo "✗ Missing vendor/autoload.php (run composer install)\n"; $ok = false;
}

// 3) Required config files
$requiredFiles = [
    __DIR__ . '/../../config/config.php',
    __DIR__ . '/../../config/database.php',
    __DIR__ . '/../../config/email.php',
];
foreach ($requiredFiles as $f) {
    echo (file_exists($f) ? "✓ Exists: " : "✗ Missing: ") . str_replace(dirname(__DIR__,2).DIRECTORY_SEPARATOR, '', $f) . "\n";
    if (!file_exists($f)) $ok = false;
}

// 4) Library presence
if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
    echo "✓ PHPMailer available\n";
} else {
    echo "✗ PHPMailer class not found\n"; $ok = false;
}
if (class_exists('PragmaRX\\Google2FA\\Google2FA')) {
    echo "✓ Google2FA available\n";
} else {
    echo "✗ Google2FA class not found\n"; $ok = false;
}

// 5) Database connectivity
try {
    require_once __DIR__ . '/../../config/database.php';
    $db = getDB()->getConnection();
    if ($db && $db->ping()) {
        echo "✓ Database connection OK\n";
        // Simple query to validate schema presence
        $res = $db->query("SHOW TABLES LIKE 'accounts'");
        if ($res && $res->num_rows >= 0) {
            echo "✓ Database reachable; 'accounts' table " . ($res->num_rows ? 'found' : 'not found') . "\n";
        }
    } else {
        echo "✗ Database connection failed (no ping)\n"; $ok = false;
    }
} catch (Throwable $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n"; $ok = false;
}

// 6) Permissions check for logs directory
$logsDir = __DIR__ . '/../../logs';
if (is_dir($logsDir) && is_writable($logsDir)) {
    echo "✓ logs/ writable\n";
} else {
    echo "⚠ logs/ not writable or missing\n";
}

echo "\nOverall: " . ($ok ? 'PASS' : 'FAIL') . "\n";

