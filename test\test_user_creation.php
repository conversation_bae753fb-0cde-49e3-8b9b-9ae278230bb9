<?php
require_once 'config/config.php';

$message = '';
$error = '';
$user_created = false;

if (isset($_GET['create'])) {
    try {
        $db = getDB();
        
        // Check if user already exists
        $check_email = $db->query("SELECT id FROM accounts WHERE email = ?", ['<EMAIL>']);
        if ($check_email->num_rows > 0) {
            $error = "User <NAME_EMAIL> already exists!";
        } else {
            $db->beginTransaction();
            
            // Generate account number
            $account_number = generateAccountNumber();
            
            // User data
            $username = 'demouser4041';
            $email = '<EMAIL>';
            $first_name = 'Demo';
            $last_name = 'User';
            $phone = '**********';
            $address = '123 Demo Street, Demo City, Demo State';
            $password = hashPassword('DemoPass123!');
            $currency = 'USD';
            $account_type = 'savings';
            $initial_balance = 1000.00;
            $status = 'active';
            $kyc_status = 'verified';
            
            // Insert user
            $sql = "INSERT INTO accounts (
                        account_number, username, password, email, first_name, last_name,
                        phone, address, currency, account_type, balance, status, kyc_status, is_admin
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)";

            $params = [
                $account_number, $username, $password, $email, $first_name, $last_name,
                $phone, $address, $currency, $account_type, $initial_balance, $status, $kyc_status
            ];
            
            $user_id = $db->insert($sql, $params);
            
            $db->commit();
            
            // Prepare user data for welcome email
            $user_data = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'username' => $username,
                'email' => $email,
                'account_number' => $account_number,
                'account_type' => $account_type,
                'currency' => $currency,
                'balance' => $initial_balance,
                'status' => $status
            ];
            
            // Send welcome email
            $emailSent = sendWelcomeEmail($email, $user_data);
            
            $user_created = true;
            $message = "✅ User created successfully!<br>
                       📧 Email: $email<br>
                       🏦 Account Number: $account_number<br>
                       👤 Username: $username<br>
                       🔑 Password: DemoPass123!<br>
                       💰 Initial Balance: $" . number_format($initial_balance, 2) . "<br><br>";
            
            if ($emailSent) {
                $message .= "📬 <strong>Welcome email sent successfully!</strong><br>";
                $message .= "📁 Check the email log file to see the email content.";
            } else {
                $message .= "⚠️ <strong>Welcome email could not be sent.</strong><br>";
                $message .= "📁 Check the email log file for details.";
            }
        }
        
    } catch (Exception $e) {
        if (isset($db)) {
            $db->rollback();
        }
        $error = "Error creating user: " . $e->getMessage();
        error_log("Test user creation error: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test User Creation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .message { color: green; padding: 15px; background: #f0f8ff; border: 1px solid green; border-radius: 5px; margin: 15px 0; }
        .error { color: red; padding: 15px; background: #ffe0e0; border: 1px solid red; border-radius: 5px; margin: 15px 0; }
        .button { background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 10px 10px 0; }
        .button:hover { background: #218838; color: white; }
        .button.secondary { background: #6c757d; }
        .button.secondary:hover { background: #5a6268; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007cba; }
        .feature { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 3px solid #28a745; }
        h1 { color: #333; margin: 0; }
        ul { padding-left: 20px; }
        li { margin: 8px 0; }
        .user-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
        .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e9ecef; }
        .detail-row:last-child { border-bottom: none; }
        .detail-label { font-weight: bold; color: #495057; }
        .detail-value { color: #212529; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test User Creation</h1>
            <p>Create test user with email: <EMAIL></p>
        </div>
        
        <div class="info">
            <strong>What this test does:</strong><br>
            Creates a test user with the specified email address and sends a beautifully designed welcome email with all account details.
        </div>
        
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!$user_created && !$error): ?>
            <div class="feature">
                <h3>📋 User Details to be Created:</h3>
                <div class="user-details">
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value"><EMAIL></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value">demouser4041</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Password:</span>
                        <span class="detail-value">DemoPass123!</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Name:</span>
                        <span class="detail-value">Demo User</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Account Type:</span>
                        <span class="detail-value">Savings</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Initial Balance:</span>
                        <span class="detail-value">$1,000.00</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">Active</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">KYC Status:</span>
                        <span class="detail-value">Verified</span>
                    </div>
                </div>
            </div>
            
            <div class="feature">
                <h3>📧 Email Features:</h3>
                <ul>
                    <li>✅ Professional banking-themed design</li>
                    <li>✅ Complete account details card</li>
                    <li>✅ Security information and tips</li>
                    <li>✅ Next steps and guidance</li>
                    <li>✅ Support contact information</li>
                    <li>✅ Mobile-responsive design</li>
                </ul>
            </div>
            
            <a href="?create=1" class="button">🚀 Create Test User & Send Email</a>
        <?php endif; ?>
        
        <?php if ($user_created): ?>
            <h3>✅ Next Steps:</h3>
            <p>1. <a href="view_email_log.php" target="_blank">View Email Log</a> to see the welcome email content</p>
            <p>2. <a href="../login.php" target="_blank">Test Login</a> with the created user</p>
            <p>3. <a href="admin/users.php" target="_blank">View in Admin Panel</a></p>
            
            <div class="feature">
                <h3>🧪 Testing Checklist:</h3>
                <ul>
                    <li>☐ Check email log for welcome email content</li>
                    <li>☐ Verify email design and formatting</li>
                    <li>☐ Test login with: demouser4041 / DemoPass123!</li>
                    <li>☐ Check OTP functionality</li>
                    <li>☐ Verify user appears in admin panel</li>
                    <li>☐ Test account details display</li>
                </ul>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="admin/add-user.php" class="button secondary">👥 Admin Add User</a>
            <a href="view_email_log.php" class="button secondary">📧 View Email Log</a>
            <a href="admin/users.php" class="button secondary">🏠 Admin Users</a>
        </div>
    </div>
</body>
</html>
