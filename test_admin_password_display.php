<?php
/**
 * Test script to verify admin password display functionality
 * This bypasses authentication to test the password decryption
 */

require_once 'config/config.php';
require_once 'includes/encryption.php';

$db = getDB();

echo "Testing Admin Password Display Functionality\n";
echo "==========================================\n\n";

// Get the test application we created
$query = "SELECT id, first_name, last_name, idme_email, idme_password FROM irs_applications WHERE id = 6";
$result = $db->query($query);
$application = $result->fetch_assoc();

if (!$application) {
    echo "✗ Test application not found\n";
    exit(1);
}

echo "Application Details:\n";
echo "ID: " . $application['id'] . "\n";
echo "Name: " . $application['first_name'] . " " . $application['last_name'] . "\n";
echo "ID.me Email: " . $application['idme_email'] . "\n";
echo "Stored Password: " . $application['idme_password'] . "\n";

// Test the decryption logic from details.php
$decrypted_password = '';
if (!empty($application['idme_password'])) {
    // Check if password uses simple encryption
    if (isIdmePasswordEncrypted($application['idme_password'])) {
        // Decrypt using simple method
        $decrypted_password = decryptIdmePassword($application['idme_password']);
        echo "✓ Detected simple encryption\n";
    } elseif (PasswordEncryption::isEncrypted($application['idme_password'])) {
        // Old complex encryption - try to decrypt
        try {
            $decrypted_password = PasswordEncryption::decrypt($application['idme_password']);
            echo "✓ Detected complex encryption\n";
        } catch (Exception $e) {
            $decrypted_password = '[Complex Encryption - Cannot Decrypt]';
            echo "✗ Complex encryption failed: " . $e->getMessage() . "\n";
        }
    } elseif (PasswordEncryption::isBcryptHash($application['idme_password'])) {
        // Legacy hashed password - cannot be decrypted
        $decrypted_password = '[Legacy Hash - Cannot Decrypt]';
        echo "✓ Detected bcrypt hash\n";
    } else {
        // Plain text password (shouldn't happen in production)
        $decrypted_password = $application['idme_password'];
        echo "✓ Detected plain text\n";
    }
} else {
    $decrypted_password = 'Not provided';
}

echo "\nDecrypted Password: " . $decrypted_password . "\n";

// Test the HTML output that would be generated
echo "\n=== HTML Output Test ===\n";
echo "<div class=\"password-field\">\n";
echo "    <span id=\"passwordDisplay\" style=\"display: none;\">" . htmlspecialchars($decrypted_password) . "</span>\n";
echo "    <span id=\"passwordHidden\">••••••••••••</span>\n";
echo "    <button type=\"button\" class=\"password-toggle\" onclick=\"togglePassword()\">\n";
echo "        <i id=\"passwordIcon\" class=\"fas fa-eye\"></i>\n";
echo "    </button>\n";
echo "</div>\n";

if ($decrypted_password === 'TestPassword123!') {
    echo "\n✓ SUCCESS: Password decryption working correctly!\n";
} else {
    echo "\n✗ FAILURE: Expected 'TestPassword123!' but got '$decrypted_password'\n";
}

echo "\nTest completed!\n";
?>