<?php
session_start();

// Simulate a logged-in user
$_SESSION['user_id'] = 1;

echo "Testing AJAX Transfer Details Functionality...\n\n";

try {
    // Test local transfer details
    echo "Testing local transfer details (ID: 4)...\n";
    $_GET['id'] = 4;
    $_GET['type'] = 'local_transfer';
    
    // Change to the ajax directory to fix relative paths
    $original_dir = getcwd();
    chdir('user/ajax');
    
    // Capture output from get_transfer_details.php
    ob_start();
    include 'get_transfer_details.php';
    $local_output = ob_get_clean();
    
    // Change back to original directory
    chdir($original_dir);
    
    if (strpos($local_output, '<PERSON> Doe') !== false) {
        echo "✓ Local transfer details retrieved successfully\n";
        echo "Sample output: " . substr($local_output, 0, 200) . "...\n\n";
    } else {
        echo "✗ Local transfer details failed\n";
        echo "Output: $local_output\n\n";
    }
    
    // Test interbank transfer details
    echo "Testing interbank transfer details (ID: 5)...\n";
    $_GET['id'] = 5;
    $_GET['type'] = 'interbank_transfer';
    
    // Change to the ajax directory to fix relative paths
    chdir('user/ajax');
    
    // Capture output from get_transfer_details.php
    ob_start();
    include 'get_transfer_details.php';
    $interbank_output = ob_get_clean();
    
    // Change back to original directory
    chdir($original_dir);
    
    if (strpos($interbank_output, 'System Administrator') !== false) {
        echo "✓ Interbank transfer details retrieved successfully\n";
        echo "Sample output: " . substr($interbank_output, 0, 200) . "...\n\n";
    } else {
        echo "✗ Interbank transfer details failed\n";
        echo "Output: $interbank_output\n\n";
    }
    
    echo "✅ AJAX functionality tests completed!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>