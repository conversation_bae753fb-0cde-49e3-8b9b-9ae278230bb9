<?php
session_start();

// Simulate a logged-in user
$_SESSION['user_id'] = 1;

echo "Testing Export Transfer History Functionality...\n\n";

try {
    // Test export functionality
    echo "Testing export transfer history...\n";
    
    // Set up export parameters
    $_POST['date_from'] = '';
    $_POST['date_to'] = '';
    $_POST['amount_from'] = '';
    $_POST['amount_to'] = '';
    $_POST['type_filter'] = 'all';
    
    // Change to the ajax directory to fix relative paths
    $original_dir = getcwd();
    chdir('user/ajax');
    
    // Capture output from export_transfer_history.php
    ob_start();
    include 'export_transfer_history.php';
    $export_output = ob_get_clean();
    
    // Change back to original directory
    chdir($original_dir);
    
    if (strpos($export_output, 'Transfer History Report') !== false) {
        echo "✓ Export functionality working successfully\n";
        echo "Export contains transfer data: " . (strpos($export_output, '<PERSON>') !== false ? 'Yes' : 'No') . "\n";
        echo "Export contains interbank data: " . (strpos($export_output, 'System Administrator') !== false ? 'Yes' : 'No') . "\n";
    } else {
        echo "✗ Export functionality failed\n";
        echo "Output length: " . strlen($export_output) . " characters\n";
        echo "First 500 characters: " . substr($export_output, 0, 500) . "\n";
    }
    
    echo "\n✅ Export functionality tests completed!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>