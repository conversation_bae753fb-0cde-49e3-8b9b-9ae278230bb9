<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Simulate a logged-in user for testing
$_SESSION['user_id'] = 1; // Assuming user ID 1 exists

echo "Testing Transfer History Functionality...\n\n";

try {
    // Get database connection
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    echo "✓ Database connection established\n";
    
    // Test user query
    $user_stmt = $connection->prepare("SELECT id, first_name, last_name, account_number FROM accounts WHERE id = ?");
    $user_stmt->bind_param('i', $_SESSION['user_id']);
    $user_stmt->execute();
    $result = $user_stmt->get_result();
    $user = $result->fetch_assoc();
    
    if ($user) {
        echo "✓ User found: {$user['first_name']} {$user['last_name']} (Account: {$user['account_number']})\n";
    } else {
        echo "✗ User not found\n";
        exit(1);
    }
    
    // Test local transfers query
    $local_stmt = $connection->prepare("
        SELECT 
            id,
            beneficiary_account_name as recipient_name,
            beneficiary_account_number as recipient_account,
            amount,
            narration as description,
            transaction_id as reference_number,
            status,
            created_at,
            'Local Transfer' as transfer_type
        FROM local_transfers 
        WHERE sender_id = ?
        LIMIT 5
    ");
    $local_stmt->bind_param('i', $_SESSION['user_id']);
    $local_stmt->execute();
    $local_result = $local_stmt->get_result();
    $local_count = $local_result->num_rows;
    
    echo "✓ Local transfers query executed successfully ({$local_count} records found)\n";
    
    // Test interbank transfers query
    $interbank_stmt = $connection->prepare("
        SELECT 
            it.id,
            CONCAT(a.first_name, ' ', a.last_name) as recipient_name,
            a.account_number as recipient_account,
            it.amount,
            it.narration as description,
            it.transaction_id as reference_number,
            it.status,
            it.created_at,
            'Interbank Transfer' as transfer_type
        FROM interbank_transfers it
        JOIN accounts a ON it.recipient_id = a.id 
        WHERE it.sender_id = ?
        LIMIT 5
    ");
    $interbank_stmt->bind_param('i', $_SESSION['user_id']);
    $interbank_stmt->execute();
    $interbank_result = $interbank_stmt->get_result();
    $interbank_count = $interbank_result->num_rows;
    
    echo "✓ Interbank transfers query executed successfully ({$interbank_count} records found)\n";
    
    // Test statistics queries
    $total_local_stmt = $connection->prepare("SELECT COALESCE(SUM(amount), 0) FROM local_transfers WHERE sender_id = ? AND status = 'completed'");
    $total_local_stmt->bind_param('i', $_SESSION['user_id']);
    $total_local_stmt->execute();
    $result = $total_local_stmt->get_result();
    $total_local = $result->fetch_row()[0];
    
    $total_interbank_stmt = $connection->prepare("SELECT COALESCE(SUM(amount), 0) FROM interbank_transfers WHERE sender_id = ? AND status = 'completed'");
    $total_interbank_stmt->bind_param('i', $_SESSION['user_id']);
    $total_interbank_stmt->execute();
    $result = $total_interbank_stmt->get_result();
    $total_interbank = $result->fetch_row()[0];
    
    echo "✓ Statistics queries executed successfully\n";
    echo "  - Total local transfers: $" . number_format($total_local, 2) . "\n";
    echo "  - Total interbank transfers: $" . number_format($total_interbank, 2) . "\n";
    echo "  - Grand total: $" . number_format($total_local + $total_interbank, 2) . "\n";
    
    echo "\n✅ All transfer history functionality tests passed!\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>