<?php
/**
 * Test OTP Functionality
 * This script tests the complete OTP flow for local transfers
 */

// Include database connection
require_once 'config/config.php';

// Get database connection
$db = getDB();

echo "<h1>🔐 OTP Functionality Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .code { background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
</style>";

// Test 1: Check database structure
echo "<div class='test-section info'>";
echo "<h2>📋 Test 1: Database Structure Check</h2>";

try {
    // Check if user_security_settings table exists
    $table_check = $db->query("SHOW TABLES LIKE 'user_security_settings'");
    if ($table_check->num_rows > 0) {
        echo "<p>✅ user_security_settings table exists</p>";
        
        // Check table structure
        $structure = $db->query("DESCRIBE user_security_settings");
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Key']}</td>";
            echo "<td>{$row['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ user_security_settings table does not exist</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error checking database structure: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 2: Check sample users and their OTP settings
echo "<div class='test-section info'>";
echo "<h2>👥 Test 2: User OTP Settings Analysis</h2>";

try {
    $users_query = "SELECT 
                        a.id, a.username, a.first_name, a.last_name, a.status,
                        COALESCE(uss.otp_enabled, 1) as otp_enabled,
                        uss.otp_enabled as raw_otp_enabled,
                        uss.updated_at as otp_updated_at
                    FROM accounts a 
                    LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                    WHERE a.is_admin = 0 
                    ORDER BY a.username 
                    LIMIT 10";
    
    $users_result = $db->query($users_query);
    
    echo "<table>";
    echo "<tr><th>Username</th><th>Name</th><th>Account Status</th><th>Raw OTP Setting</th><th>Effective OTP</th><th>Last Updated</th><th>Expected Behavior</th></tr>";
    
    while ($user = $users_result->fetch_assoc()) {
        $raw_otp = $user['raw_otp_enabled'] ?? 'NULL (defaults to 1)';
        $effective_otp = $user['otp_enabled'] ? 'ENABLED' : 'DISABLED';
        $otp_color = $user['otp_enabled'] ? 'green' : 'orange';
        $expected = $user['otp_enabled'] ? 'OTP required for local transfers' : 'No OTP required for local transfers';
        
        echo "<tr>";
        echo "<td>{$user['username']}</td>";
        echo "<td>{$user['first_name']} {$user['last_name']}</td>";
        echo "<td>{$user['status']}</td>";
        echo "<td>{$raw_otp}</td>";
        echo "<td style='color: $otp_color; font-weight: bold;'>{$effective_otp}</td>";
        echo "<td>" . ($user['otp_updated_at'] ?? 'Never') . "</td>";
        echo "<td>{$expected}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p>❌ Error checking user OTP settings: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 3: Test the OTP logic for specific user
echo "<div class='test-section warning'>";
echo "<h2>🧪 Test 3: OTP Logic Test for 'novakane'</h2>";

try {
    // Get novakane user
    $user_query = "SELECT 
                       a.id, a.username, a.first_name, a.last_name, a.status,
                       COALESCE(uss.otp_enabled, 1) as otp_enabled,
                       uss.otp_enabled as raw_otp_enabled
                   FROM accounts a 
                   LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                   WHERE a.username = 'novakane' AND a.is_admin = 0";
    
    $user_result = $db->query($user_query);
    
    if ($user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        
        echo "<h3>User: {$user['username']} ({$user['first_name']} {$user['last_name']})</h3>";
        echo "<ul>";
        echo "<li><strong>User ID:</strong> {$user['id']}</li>";
        echo "<li><strong>Account Status:</strong> {$user['status']}</li>";
        echo "<li><strong>Raw OTP Setting:</strong> " . ($user['raw_otp_enabled'] ?? 'NULL (no record)') . "</li>";
        echo "<li><strong>Effective OTP Setting:</strong> " . ($user['otp_enabled'] ? 'ENABLED' : 'DISABLED') . "</li>";
        echo "</ul>";
        
        // Test the logic that would be used in transfers
        echo "<h4>🔍 Transfer Logic Test:</h4>";
        echo "<div class='code'>";
        echo "// This is the logic used in process-transfer.php<br>";
        echo "if (\$transfer_type === 'local-bank') {<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;// Check user's OTP setting<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;\$otp_required = COALESCE(uss.otp_enabled, 1) == 1;<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;// For user '{$user['username']}': otp_required = " . ($user['otp_enabled'] ? 'true' : 'false') . "<br>";
        echo "}<br><br>";
        
        if ($user['otp_enabled']) {
            echo "✅ <strong>Result:</strong> Local transfers for '{$user['username']}' WILL require OTP verification<br>";
        } else {
            echo "🔓 <strong>Result:</strong> Local transfers for '{$user['username']}' will NOT require OTP verification<br>";
        }
        echo "</div>";
        
    } else {
        echo "<p>❌ User 'novakane' not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error testing OTP logic: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test 4: Test JavaScript integration
echo "<div class='test-section success'>";
echo "<h2>🌐 Test 4: Frontend Integration Test</h2>";

echo "<p>The following changes have been made to ensure OTP settings are respected:</p>";
echo "<ol>";
echo "<li><strong>Backend (index.php):</strong> Added query to get user's OTP setting and pass it to JavaScript</li>";
echo "<li><strong>Frontend (transfers.js):</strong> Modified to check window.userOTPEnabled before showing OTP modal</li>";
echo "<li><strong>Processing (process-transfer.php):</strong> Already had correct logic to check OTP setting</li>";
echo "</ol>";

echo "<h4>🔧 Code Changes Made:</h4>";
echo "<div class='code'>";
echo "// In index.php - Added OTP setting query:<br>";
echo "\$otp_query = \"SELECT COALESCE(uss.otp_enabled, 1) as otp_enabled FROM accounts a LEFT JOIN user_security_settings uss ON a.id = uss.user_id WHERE a.id = ?\";<br>";
echo "\$user_otp_enabled = \$otp_setting ? \$otp_setting['otp_enabled'] : 1;<br><br>";

echo "// In index.php - Pass to JavaScript:<br>";
echo "window.userOTPEnabled = " . "<?php echo \$user_otp_enabled ? 'true' : 'false'; ?>" . ";<br><br>";

echo "// In transfers.js - Check before showing OTP:<br>";
echo "if (currentTransferType === 'local-bank' && window.userOTPEnabled) {<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;showOTPModal(); // Show OTP only if enabled<br>";
echo "} else {<br>";
echo "&nbsp;&nbsp;&nbsp;&nbsp;processTransfer(); // Skip OTP if disabled or inter-bank<br>";
echo "}<br>";
echo "</div>";

echo "</div>";

// Test 5: UI Improvements
echo "<div class='test-section success'>";
echo "<h2>🎨 Test 5: UI Improvements Completed</h2>";

echo "<p>The following UI improvements have been made to user-otp-settings.php:</p>";
echo "<ul>";
echo "<li>✅ <strong>User Avatar Color:</strong> Changed from blue (#206bc4) to primary color (#007bff)</li>";
echo "<li>✅ <strong>Badge Text Color:</strong> Ensured white text in colored badges</li>";
echo "<li>✅ <strong>Understanding OTP Settings Background:</strong> Set to white background</li>";
echo "</ul>";

echo "</div>";

// Test 6: Summary and Next Steps
echo "<div class='test-section info'>";
echo "<h2>📝 Test Summary</h2>";

echo "<h3>✅ Issues Resolved:</h3>";
echo "<ol>";
echo "<li><strong>Local Transfer OTP Respect:</strong> JavaScript now checks user's OTP setting before showing OTP modal</li>";
echo "<li><strong>Inter-bank Transfer Behavior:</strong> Continues to work without OTP (unchanged)</li>";
echo "<li><strong>UI Improvements:</strong> Avatar color, text colors, and background colors fixed</li>";
echo "</ol>";

echo "<h3>🔧 How It Works Now:</h3>";
echo "<ul>";
echo "<li><strong>Inter-bank transfers:</strong> Never require OTP (instant, between internal users)</li>";
echo "<li><strong>Local transfers with OTP enabled:</strong> Show OTP modal and require verification</li>";
echo "<li><strong>Local transfers with OTP disabled:</strong> Process directly without OTP</li>";
echo "</ul>";

echo "<h3>🧪 To Test:</h3>";
echo "<ol>";
echo "<li>Go to admin/user-otp-settings.php and disable OTP for a test user</li>";
echo "<li>Login as that user and try a local transfer - should NOT show OTP modal</li>";
echo "<li>Enable OTP for the user and try again - should show OTP modal</li>";
echo "<li>Try inter-bank transfers - should never show OTP modal</li>";
echo "</ol>";

echo "</div>";

?>