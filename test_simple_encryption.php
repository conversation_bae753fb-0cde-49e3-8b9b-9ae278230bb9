<?php
/**
 * Test script for simple ID.me password encryption/decryption
 */

require_once 'includes/encryption.php';
require_once 'config/config.php';

echo "Testing Simple ID.me Password Encryption/Decryption\n";
echo "================================================\n\n";

// Test 1: Basic encryption/decryption
echo "Test 1: Basic Encryption/Decryption\n";
$test_password = "MyTestPassword123!";
echo "Original password: $test_password\n";

$encrypted = encryptIdmePassword($test_password);
echo "Encrypted: $encrypted\n";

$decrypted = decryptIdmePassword($encrypted);
echo "Decrypted: $decrypted\n";

if ($test_password === $decrypted) {
    echo "✓ Basic encryption/decryption PASSED\n\n";
} else {
    echo "✗ Basic encryption/decryption FAILED\n\n";
}

// Test 2: Check encryption detection
echo "Test 2: Encryption Detection\n";
if (isIdmePasswordEncrypted($encrypted)) {
    echo "✓ Encryption detection PASSED\n\n";
} else {
    echo "✗ Encryption detection FAILED\n\n";
}

// Test 3: Test with database records
echo "Test 3: Database Records\n";
$db = getDB();
$query = "SELECT id, idme_password FROM irs_applications WHERE idme_password IS NOT NULL AND idme_password != '' LIMIT 3";
$result = $db->query($query);

while ($row = $result->fetch_assoc()) {
    $app_id = $row['id'];
    $stored_password = $row['idme_password'];
    
    echo "Application ID: $app_id\n";
    echo "Stored password: $stored_password\n";
    
    if (isIdmePasswordEncrypted($stored_password)) {
        $decrypted = decryptIdmePassword($stored_password);
        echo "Decrypted password: $decrypted\n";
        echo "✓ Successfully decrypted\n\n";
    } else {
        echo "✗ Password not in simple encryption format\n\n";
    }
}

echo "Testing completed!\n";
?>