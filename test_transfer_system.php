<?php
// Comprehensive test for the transfer system
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/config.php';

echo "<h1>Transfer System Integration Test</h1>";

// Test 1: Check if tables exist
echo "<h2>1. Database Table Check</h2>";
try {
    $tables = ['transfers', 'local_transfers', 'interbank_transfers', 'users'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' missing<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 2: Test the UNION query for transfers list
echo "<h2>2. Transfer List Query Test</h2>";
try {
    $sql = "
        (SELECT 
            id, 
            sender_id, 
            recipient_id, 
            amount, 
            currency, 
            status, 
            created_at,
            'local-bank' as transfer_type,
            reference_number,
            fee,
            exchange_rate,
            narration
        FROM local_transfers 
        ORDER BY created_at DESC 
        LIMIT 10)
        UNION ALL
        (SELECT 
            id, 
            sender_id, 
            recipient_id, 
            amount, 
            currency, 
            status, 
            created_at,
            'inter-bank' as transfer_type,
            reference_number,
            fee,
            exchange_rate,
            narration
        FROM interbank_transfers 
        ORDER BY created_at DESC 
        LIMIT 10)
        ORDER BY created_at DESC
        LIMIT 20
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "✅ UNION query executed successfully<br>";
    echo "Found " . count($transfers) . " transfers<br>";
    
    if (count($transfers) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Type</th><th>Amount</th><th>Currency</th><th>Status</th><th>Created</th></tr>";
        foreach (array_slice($transfers, 0, 5) as $transfer) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($transfer['id']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['transfer_type']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['amount']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['currency']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['status']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ UNION query error: " . $e->getMessage() . "<br>";
}

// Test 3: Test individual transfer retrieval
echo "<h2>3. Individual Transfer Retrieval Test</h2>";
try {
    // Test local transfer retrieval
    $test_id = 1;
    
    // Check local_transfers first
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM local_transfers WHERE id = ?");
    $stmt->execute([$test_id]);
    $local_count = $stmt->fetchColumn();
    
    if ($local_count > 0) {
        $sql = "
            SELECT lt.*, 
                   s.username as sender_username, s.first_name as sender_first_name, s.last_name as sender_last_name,
                   s.account_number as sender_account_number,
                   r.username as recipient_username, r.first_name as recipient_first_name, r.last_name as recipient_last_name,
                   r.account_number as recipient_account_number
            FROM local_transfers lt
            LEFT JOIN users s ON lt.sender_id = s.id
            LEFT JOIN users r ON lt.recipient_id = r.id
            WHERE lt.id = ?
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$test_id]);
        $transfer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($transfer) {
            echo "✅ Local transfer retrieval successful<br>";
            echo "Transfer ID: " . $transfer['id'] . ", Amount: " . $transfer['amount'] . " " . $transfer['currency'] . "<br>";
        }
    }
    
    // Check interbank_transfers
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM interbank_transfers WHERE id = ?");
    $stmt->execute([$test_id]);
    $interbank_count = $stmt->fetchColumn();
    
    if ($interbank_count > 0) {
        $sql = "
            SELECT it.*, 
                   s.username as sender_username, s.first_name as sender_first_name, s.last_name as sender_last_name,
                   s.account_number as sender_account_number,
                   r.username as recipient_username, r.first_name as recipient_first_name, r.last_name as recipient_last_name,
                   r.account_number as recipient_account_number
            FROM interbank_transfers it
            LEFT JOIN users s ON it.sender_id = s.id
            LEFT JOIN users r ON it.recipient_id = r.id
            WHERE it.id = ?
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$test_id]);
        $transfer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($transfer) {
            echo "✅ Inter-bank transfer retrieval successful<br>";
            echo "Transfer ID: " . $transfer['id'] . ", Amount: " . $transfer['amount'] . " " . $transfer['currency'] . "<br>";
        }
    }
    
    if ($local_count == 0 && $interbank_count == 0) {
        echo "ℹ️ No transfers found with ID $test_id (this is normal if no test data exists)<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Individual transfer retrieval error: " . $e->getMessage() . "<br>";
}

// Test 4: Test the dynamic transfer type detection logic
echo "<h2>4. Transfer Type Detection Logic Test</h2>";
try {
    $test_id = 1;
    
    // Check local_transfers table
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM local_transfers WHERE id = ?");
    $stmt->execute([$test_id]);
    $local_exists = $stmt->fetchColumn() > 0;
    
    // Check interbank_transfers table  
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM interbank_transfers WHERE id = ?");
    $stmt->execute([$test_id]);
    $interbank_exists = $stmt->fetchColumn() > 0;
    
    if ($local_exists) {
        $transfer_type = 'local-bank';
        echo "✅ Transfer type detection: local-bank<br>";
    } elseif ($interbank_exists) {
        $transfer_type = 'inter-bank';
        echo "✅ Transfer type detection: inter-bank<br>";
    } else {
        $transfer_type = 'other';
        echo "✅ Transfer type detection: other (fallback)<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Transfer type detection error: " . $e->getMessage() . "<br>";
}

// Test 5: Check if required columns exist
echo "<h2>5. Column Structure Check</h2>";
try {
    $tables_to_check = [
        'local_transfers' => ['id', 'sender_id', 'recipient_id', 'amount', 'currency', 'status', 'created_at', 'reference_number', 'fee', 'exchange_rate', 'narration'],
        'interbank_transfers' => ['id', 'sender_id', 'recipient_id', 'amount', 'currency', 'status', 'created_at', 'reference_number', 'fee', 'exchange_rate', 'narration'],
        'users' => ['id', 'username', 'first_name', 'last_name', 'account_number']
    ];
    
    foreach ($tables_to_check as $table => $required_columns) {
        echo "<strong>$table:</strong><br>";
        $stmt = $pdo->query("DESCRIBE $table");
        $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($required_columns as $column) {
            if (in_array($column, $existing_columns)) {
                echo "✅ Column '$column' exists<br>";
            } else {
                echo "❌ Column '$column' missing<br>";
            }
        }
        echo "<br>";
    }
} catch (Exception $e) {
    echo "❌ Column check error: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p>If all tests show ✅, the transfer system integration is working correctly.</p>";
?>