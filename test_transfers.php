<?php
// Test script to verify transfer queries work correctly
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
include_once 'config/config.php';

echo "<h1>Transfer Integration Test</h1>";

try {
    // Test the UNION query from transfers.php
    $sql = "SELECT 
                lt.id,
                lt.sender_id,
                lt.recipient_id,
                lt.amount,
                lt.currency,
                lt.status,
                lt.fee,
                lt.created_at,
                lt.updated_at,
                lt.narration,
                lt.reference,
                'local-bank' as transfer_type,
                sender.first_name as sender_first_name,
                sender.last_name as sender_last_name,
                sender.username as sender_username,
                sender.account_number as sender_account_number,
                recipient.first_name as recipient_first_name,
                recipient.last_name as recipient_last_name,
                recipient.username as recipient_username,
                recipient.account_number as recipient_account_number,
                lt.beneficiary_account_number,
                lt.beneficiary_name,
                lt.beneficiary_bank_name,
                lt.beneficiary_routing_code,
                lt.beneficiary_account_type
            FROM local_transfers lt
            LEFT JOIN users sender ON lt.sender_id = sender.id
            LEFT JOIN users recipient ON lt.recipient_id = recipient.id
            
            UNION ALL
            
            SELECT 
                it.id,
                it.sender_id,
                it.recipient_id,
                it.amount,
                it.currency,
                it.status,
                it.fee,
                it.created_at,
                it.updated_at,
                it.narration,
                it.reference,
                'inter-bank' as transfer_type,
                sender.first_name as sender_first_name,
                sender.last_name as sender_last_name,
                sender.username as sender_username,
                sender.account_number as sender_account_number,
                recipient.first_name as recipient_first_name,
                recipient.last_name as recipient_last_name,
                recipient.username as recipient_username,
                recipient.account_number as recipient_account_number,
                it.beneficiary_account_number,
                it.beneficiary_name,
                it.beneficiary_bank_name,
                it.beneficiary_routing_code,
                it.beneficiary_account_type
            FROM interbank_transfers it
            LEFT JOIN users sender ON it.sender_id = sender.id
            LEFT JOIN users recipient ON it.recipient_id = recipient.id
            
            ORDER BY created_at DESC
            LIMIT 10";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $transfers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Query Results (" . count($transfers) . " transfers found)</h2>";
    
    if (count($transfers) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Type</th>";
        echo "<th>Sender</th>";
        echo "<th>Recipient</th>";
        echo "<th>Amount</th>";
        echo "<th>Status</th>";
        echo "<th>Created</th>";
        echo "</tr>";
        
        foreach ($transfers as $transfer) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($transfer['id']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['transfer_type']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['sender_first_name'] . ' ' . $transfer['sender_last_name']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['recipient_first_name'] . ' ' . $transfer['recipient_last_name']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['currency'] . ' ' . number_format($transfer['amount'], 2)) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['status']) . "</td>";
            echo "<td>" . htmlspecialchars($transfer['created_at']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Test individual transfer retrieval
        $first_transfer = $transfers[0];
        echo "<h2>Testing Individual Transfer Retrieval</h2>";
        echo "<p>Testing transfer ID: " . $first_transfer['id'] . " (Type: " . $first_transfer['transfer_type'] . ")</p>";
        
        // Test the logic from view.php
        $transfer_id = $first_transfer['id'];
        $transfer_type = $first_transfer['transfer_type'];
        
        if ($transfer_type === 'local-bank') {
            $sql = "SELECT 
                        lt.*,
                        'local-bank' as transfer_type,
                        sender.first_name as sender_first_name,
                        sender.last_name as sender_last_name,
                        sender.username as sender_username,
                        sender.account_number as sender_account_number,
                        recipient.first_name as recipient_first_name,
                        recipient.last_name as recipient_last_name,
                        recipient.username as recipient_username,
                        recipient.account_number as recipient_account_number
                    FROM local_transfers lt
                    LEFT JOIN users sender ON lt.sender_id = sender.id
                    LEFT JOIN users recipient ON lt.recipient_id = recipient.id
                    WHERE lt.id = ?";
        } else {
            $sql = "SELECT 
                        it.*,
                        'inter-bank' as transfer_type,
                        sender.first_name as sender_first_name,
                        sender.last_name as sender_last_name,
                        sender.username as sender_username,
                        sender.account_number as sender_account_number,
                        recipient.first_name as recipient_first_name,
                        recipient.last_name as recipient_last_name,
                        recipient.username as recipient_username,
                        recipient.account_number as recipient_account_number
                    FROM interbank_transfers it
                    LEFT JOIN users sender ON it.sender_id = sender.id
                    LEFT JOIN users recipient ON it.recipient_id = recipient.id
                    WHERE it.id = ?";
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$transfer_id]);
        $individual_transfer = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($individual_transfer) {
            echo "<p style='color: green;'>✓ Individual transfer retrieval successful</p>";
            echo "<pre>" . print_r($individual_transfer, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>✗ Individual transfer retrieval failed</p>";
        }
        
    } else {
        echo "<p>No transfers found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Database Connection Test</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as local_count FROM local_transfers");
    $local_count = $stmt->fetch(PDO::FETCH_ASSOC)['local_count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as inter_count FROM interbank_transfers");
    $inter_count = $stmt->fetch(PDO::FETCH_ASSOC)['inter_count'];
    
    echo "<p>Local transfers: " . $local_count . "</p>";
    echo "<p>Inter-bank transfers: " . $inter_count . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>