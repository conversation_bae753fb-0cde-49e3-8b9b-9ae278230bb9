<?php
require_once 'config/config.php';

try {
    $db = getDB();
    
    echo "🧪 Testing Wire Transfer System\n";
    echo "================================\n\n";
    
    // Test 1: Check wire transfer fields configuration
    echo "1. Testing wire transfer fields configuration:\n";
    $fields_result = $db->query("SELECT COUNT(*) as count FROM wire_transfer_fields WHERE is_active = 1");
    $fields_count = $fields_result->fetch_assoc();
    echo "   ✅ Active fields: " . $fields_count['count'] . "\n";
    
    // Test 2: Check sample wire transfer data
    echo "\n2. Testing wire transfer data retrieval:\n";
    $transfer_id = 17; // Using an existing transfer ID
    
    $transfer_query = "SELECT t.*
                       FROM transfers t
                       WHERE t.id = $transfer_id AND t.transfer_type = 'international'";
    
    $result = $db->query($transfer_query);
    
    if ($transfer = $result->fetch_assoc()) {
        echo "   ✅ Transfer found: ID " . $transfer['id'] . "\n";
        echo "   📊 Amount: $" . $transfer['amount'] . " " . $transfer['currency'] . "\n";
        echo "   📅 Status: " . $transfer['status'] . " / " . $transfer['processing_status'] . "\n";
        echo "   🏦 Bank: " . ($transfer['bank_name'] ?: 'Not specified') . "\n";
        echo "   💳 SWIFT: " . ($transfer['swift_code'] ?: 'Not specified') . "\n";
        
        // Test wire transfer data parsing
        if (!empty($transfer['wire_transfer_data'])) {
            $wire_data = json_decode($transfer['wire_transfer_data'], true);
            if ($wire_data) {
                echo "   📋 Wire data fields: " . count($wire_data) . "\n";
                foreach ($wire_data as $key => $value) {
                    echo "      - " . $key . ": " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "\n";
                }
            } else {
                echo "   ⚠️  Wire transfer data is not valid JSON\n";
            }
        } else {
            echo "   ⚠️  No wire transfer data found\n";
        }
    } else {
        echo "   ❌ No transfer found with ID " . $transfer_id . "\n";
    }
    
    // Test 3: Check field configuration and data merging
    echo "\n3. Testing field configuration and data merging:\n";
    $fields_query = "SELECT * FROM wire_transfer_fields WHERE is_active = 1 ORDER BY field_group, display_order, field_label";
    $fields_result = $db->query($fields_query);
    
    $configured_fields = [];
    while ($field = $fields_result->fetch_assoc()) {
        $configured_fields[$field['field_name']] = $field;
    }
    
    echo "   📋 Configured fields:\n";
    foreach ($configured_fields as $field_name => $field) {
        echo "      - " . $field_name . " (" . $field['field_group'] . "): " . $field['field_label'] . "\n";
    }
    
    // Test 4: Check for missing standard fields
    echo "\n4. Testing for missing standard fields:\n";
    $standard_fields = [
        'beneficiary_account_number' => 'Beneficiary Account Number',
        'beneficiary_account_name' => 'Beneficiary Account Name',
        'bank_name' => 'Bank Name',
        'swift_code' => 'SWIFT Code',
        'routing_number' => 'Routing Number'
    ];
    
    foreach ($standard_fields as $field_name => $field_label) {
        if (isset($configured_fields[$field_name])) {
            echo "   ✅ " . $field_label . " is configured\n";
        } else {
            echo "   ⚠️  " . $field_label . " is missing from configuration\n";
        }
    }
    
    // Test 5: Check billing code integration
    echo "\n5. Testing billing code integration:\n";
    $billing_settings = $db->query("SELECT * FROM billing_code_settings LIMIT 1");
    if ($billing_settings->num_rows > 0) {
        $settings = $billing_settings->fetch_assoc();
        echo "   ✅ Billing code settings found\n";
        echo "   📊 Enabled: " . (isset($settings['is_enabled']) && $settings['is_enabled'] ? 'Yes' : 'No') . "\n";
        echo "   🔢 Code length: " . (isset($settings['code_length']) ? $settings['code_length'] : 'Not set') . "\n";
        
        // Show all available columns for debugging
        echo "   🔍 Available columns: " . implode(', ', array_keys($settings)) . "\n";
    } else {
        echo "   ❌ No billing code settings found\n";
    }
    
    echo "\n🎉 Wire Transfer System Test Complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
}
?>