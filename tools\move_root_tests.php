<?php
// Move root-level test_*.php files into test/delete
$root = __DIR__ . '/..';
$targetDir = $root . '/test/delete';
if (!is_dir($targetDir)) {
    mkdir($targetDir, 0777, true);
}
$files = glob($root . '/test_*.php');
$moved = 0;
foreach ($files as $file) {
    $basename = basename($file);
    $dest = $targetDir . '/' . $basename;
    if (@rename($file, $dest)) {
        echo "Moved: $basename\n";
        $moved++;
    } else {
        echo "Failed: $basename\n";
    }
}
echo "Total moved: $moved\n";

