/**
 * Transfer History Page CSS
 * Dedicated styles for transfer history page
 * Extends dashboard patterns with transfer-specific styling
 */

/* Import dashboard base styles */
@import url('../dashboard/dashboard.css');

/* Mini Hero Section - aligned with transactions page */
.transaction-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 16px;
    padding: 1.5rem 2rem;
    color: white;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.transaction-hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.transaction-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.transaction-hero .hero-main { flex: 1; }
.transaction-hero .hero-title { font-size: 1.75rem; font-weight: 700; margin-bottom: 0.5rem; text-shadow: 0 2px 4px rgba(0,0,0,0.3); }
.transaction-hero .hero-subtitle { font-size: 1rem; opacity: 0.9; margin-bottom: 0.5rem; }
.transaction-hero .hero-stats { font-size: 0.9rem; opacity: 0.8; font-weight: 500; }
.transaction-hero .hero-actions { display: flex; gap: 0.75rem; }

/* Filters Section */
.filters-section {
    margin-bottom: 2rem;
}

.filters-section .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.filters-section .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.filters-section .card-body {
    padding: 1.5rem;
}

.filters-actions {
    display: flex;
    gap: 0.5rem;
    align-items: end;
    height: 100%;
    padding-top: 2rem;
}

.filters-actions .btn {
    flex: 1;
}

/* Transactions Section */
.transactions-section {
    margin-bottom: 2rem;
}

.transactions-section .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.transactions-section .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.transactions-summary {
    font-size: 0.9rem;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

/* Transaction Row Styles */
.transaction-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.transaction-row:hover {
    background-color: #f8f9fa;
}

/* Cell Specific Styles */
.transaction-number {
    font-weight: 600;
    color: #6c757d;
    width: 60px;
}

.sender-name {
    min-width: 200px;
}

.sender-info strong {
    color: #212529;
    font-weight: 600;
}

.type-badge {
    width: 100px;
}

.type-badge .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.description {
    min-width: 150px;
}

.transaction-description {
    color: #495057;
}

.reference-number {
    width: 120px;
}

.reference {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.amount-value {
    width: 120px;
    text-align: right;
}

.amount {
    font-weight: 600;
    font-size: 1rem;
}

.amount.credit {
    color: #28a745;
}

.amount.debit {
    color: #dc3545;
}

.date-time {
    width: 140px;
}

.transaction-date {
    color: #495057;
    font-weight: 500;
}

.time-display {
    color: #6c757d;
    font-size: 0.85rem;
}

.status-badge {
    width: 100px;
}

.status-badge .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Modal Styles */
.modal-header.bg-primary {
    border-bottom: none;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .transaction-hero .hero-content { flex-direction: column; gap: 1rem; text-align: center; }
    
    .filters-actions {
        flex-direction: column;
    }
    
    .filters-actions .btn {
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .sender-name,
    .description,
    .reference-number {
        min-width: auto;
    }
}

@media (max-width: 576px) {
    .transaction-hero {
        padding: 1.5rem 0;
    }
    
    .transaction-hero h1 {
        font-size: 1.75rem;
    }
    
    .filters-section .card-body,
    .transactions-section .card-header {
        padding: 1rem;
    }
    
    .table th,
    .table td {
        padding: 0.75rem 0.5rem;
    }
}

/* Stats Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card.bg-primary .card-body,
.card.bg-success .card-body,
.card.bg-info .card-body,
.card.bg-warning .card-body {
    padding: 1.5rem;
}

.card.bg-primary h5,
.card.bg-success h5,
.card.bg-info h5,
.card.bg-warning h5 {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.card.bg-primary h3,
.card.bg-success h3,
.card.bg-info h3,
.card.bg-warning h3 {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0;
}

/* Balance card styles are imported from dashboard.css */