<?php
/**
 * Transfer History Page
 * View and export your complete transfer history
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

// Get user data from database
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user account information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Filter settings
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$transfer_type = $_GET['transfer_type'] ?? '';
$status = $_GET['status'] ?? '';
$amount_min = $_GET['amount_min'] ?? '';
$amount_max = $_GET['amount_max'] ?? '';

// Build WHERE conditions for local transfers
$local_where_conditions = [];
$local_params = [];

// User ID condition
$local_where_conditions[] = "sender_id = ?";
$local_params[] = $user_id;

// Date filters
if (!empty($date_from)) {
    $local_where_conditions[] = "DATE(created_at) >= ?";
    $local_params[] = $date_from;
}
if (!empty($date_to)) {
    $local_where_conditions[] = "DATE(created_at) <= ?";
    $local_params[] = $date_to;
}

// Status filter
if (!empty($status)) {
    $local_where_conditions[] = "status = ?";
    $local_params[] = $status;
}

// Amount filters
if (!empty($amount_min)) {
    $local_where_conditions[] = "amount >= ?";
    $local_params[] = floatval($amount_min);
}
if (!empty($amount_max)) {
    $local_where_conditions[] = "amount <= ?";
    $local_params[] = floatval($amount_max);
}

$local_where_clause = implode(' AND ', $local_where_conditions);

// Build WHERE conditions for interbank transfers
$interbank_where_conditions = [];
$interbank_params = [];

// User ID condition
$interbank_where_conditions[] = "ib.sender_id = ?";
$interbank_params[] = $user_id;

// Date filters
if (!empty($date_from)) {
    $interbank_where_conditions[] = "DATE(ib.created_at) >= ?";
    $interbank_params[] = $date_from;
}
if (!empty($date_to)) {
    $interbank_where_conditions[] = "DATE(ib.created_at) <= ?";
    $interbank_params[] = $date_to;
}

// Status filter
if (!empty($status)) {
    $interbank_where_conditions[] = "ib.status = ?";
    $interbank_params[] = $status;
}

// Amount filters
if (!empty($amount_min)) {
    $interbank_where_conditions[] = "ib.amount >= ?";
    $interbank_params[] = floatval($amount_min);
}
if (!empty($amount_max)) {
    $interbank_where_conditions[] = "ib.amount <= ?";
    $interbank_params[] = floatval($amount_max);
}

$interbank_where_clause = implode(' AND ', $interbank_where_conditions);

// Build the combined query
$local_query = "
    SELECT 
        id,
        beneficiary_account_name as recipient_name,
        beneficiary_account_number as recipient_account,
        amount,
        narration as description,
        transaction_id as reference_number,
        status,
        created_at,
        'Local Transfer' as transfer_type
    FROM local_transfers 
    WHERE $local_where_clause
";

$interbank_query = "
    SELECT 
        ib.id,
        CONCAT(u.first_name, ' ', u.last_name) as recipient_name,
        u.account_number as recipient_account,
        ib.amount,
        ib.narration as description,
        ib.transaction_id as reference_number,
        ib.status,
        ib.created_at,
        'Interbank Transfer' as transfer_type
    FROM interbank_transfers ib
    LEFT JOIN accounts u ON ib.recipient_id = u.id
    WHERE $interbank_where_clause
";

// Execute queries based on transfer_type filter
if ($transfer_type === 'local') {
    $final_query = $local_query . " ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
    $transfers_result = $db->query($final_query, $local_params);
    $transfers = [];
    while ($row = $transfers_result->fetch_assoc()) {
        $transfers[] = $row;
    }
} elseif ($transfer_type === 'interbank') {
    $final_query = $interbank_query . " ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
    $transfers_result = $db->query($final_query, $interbank_params);
    $transfers = [];
    while ($row = $transfers_result->fetch_assoc()) {
        $transfers[] = $row;
    }
} else {
    // For UNION query, execute both queries separately and merge results
    $local_final = $local_query . " ORDER BY created_at DESC";
    $interbank_final = $interbank_query . " ORDER BY created_at DESC";
    
    // Execute local transfers query
    $local_result = $db->query($local_final, $local_params);
    $local_transfers = [];
    while ($row = $local_result->fetch_assoc()) {
        $local_transfers[] = $row;
    }
    
    // Execute interbank transfers query
    $interbank_result = $db->query($interbank_final, $interbank_params);
    $interbank_transfers = [];
    while ($row = $interbank_result->fetch_assoc()) {
        $interbank_transfers[] = $row;
    }
    
    // Merge and sort results
    $transfers = array_merge($local_transfers, $interbank_transfers);
    
    // Sort by created_at DESC
    usort($transfers, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    
    // Apply pagination
    $transfers = array_slice($transfers, $offset, $per_page);
}

// Count total records for pagination
$count_local_query = "SELECT COUNT(*) FROM local_transfers WHERE $local_where_clause";
$count_interbank_query = "SELECT COUNT(*) FROM interbank_transfers ib WHERE $interbank_where_clause";

if ($transfer_type === 'local') {
    $count_result = $db->query($count_local_query, $local_params);
    $total_records = $count_result->fetch_row()[0];
} elseif ($transfer_type === 'interbank') {
    $count_result = $db->query($count_interbank_query, $interbank_params);
    $total_records = $count_result->fetch_row()[0];
} else {
    $count_result1 = $db->query($count_local_query, $local_params);
    $local_count = $count_result1->fetch_row()[0];
    
    $count_result2 = $db->query($count_interbank_query, $interbank_params);
    $interbank_count = $count_result2->fetch_row()[0];
    
    $total_records = $local_count + $interbank_count;
}

$total_pages = ceil($total_records / $per_page);

// Calculate statistics
// Total sent amount (both local and interbank)
$local_sent_result = $db->query("SELECT COALESCE(SUM(amount), 0) FROM local_transfers WHERE sender_id = ? AND status = 'completed'", [$user_id]);
$local_sent = $local_sent_result->fetch_row()[0];

$interbank_sent_result = $db->query("SELECT COALESCE(SUM(amount), 0) FROM interbank_transfers WHERE sender_id = ? AND status = 'completed'", [$user_id]);
$interbank_sent = $interbank_sent_result->fetch_row()[0];

$total_sent = $local_sent + $interbank_sent;

// This month sent
$local_month_result = $db->query("SELECT COALESCE(SUM(amount), 0) FROM local_transfers WHERE sender_id = ? AND status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())", [$user_id]);
$local_month = $local_month_result->fetch_row()[0];

$interbank_month_result = $db->query("SELECT COALESCE(SUM(amount), 0) FROM interbank_transfers WHERE sender_id = ? AND status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())", [$user_id]);
$interbank_month = $interbank_month_result->fetch_row()[0];

$this_month_sent = $local_month + $interbank_month;

// Get user's available balance from loaded user record
$available_balance = $user['balance'] ?? 0;

// Set page title and subtitle
$page_title = 'Transfer History';
$page_subtitle = 'Complete transfer history and export';

// Include header
require_once '../shared/header.php';
?>
<link rel="stylesheet" href="history.css">

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content">

    <!-- Mini Hero Section -->
    <div class="transaction-hero mb-4">
        <div class="hero-content">
            <div class="hero-main">
                <div class="hero-title">Transfer History</div>
                <div class="hero-subtitle">Complete transfer history and export</div>
                <div class="hero-stats">
                    Total Transfers: <?php echo number_format($total_records); ?>
                </div>
            </div>
            <div class="hero-actions">
                <button class="btn btn-primary" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>Export PDF
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Available Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Available</div>
                    <div class="balance-amount available">
                        <?php echo formatCurrency($available_balance, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">Main Account</div>
                </div>
            </div>

            <!-- Total Sent Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: #16a34a;">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total Sent</div>
                    <div class="balance-amount" style="color: #16a34a;">
                        <?php echo formatCurrency($total_sent, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">All Transfers</div>
                </div>
            </div>

            <!-- This Month Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: #0ea5e9;">
                    <i class="fas fa-calendar-month"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">This Month</div>
                    <div class="balance-amount" style="color: #0ea5e9;">
                        <?php echo formatCurrency($this_month_sent, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">Monthly Total</div>
                </div>
            </div>

            <!-- Total Records Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: #f59e0b;">
                    <i class="fas fa-list"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total Records</div>
                    <div class="balance-amount" style="color: #f59e0b;">
                        <?php echo number_format($total_records); ?>
                    </div>
                    <div class="balance-subtitle">All Time</div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
            <div class="filters-section">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Filter Transfers</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="transfer_type" class="form-label">Transfer Type</label>
                            <select name="transfer_type" id="transfer_type" class="form-select">
                                <option value="">All Types</option>
                                <option value="local" <?php echo $transfer_type === 'local' ? 'selected' : ''; ?>>Local Transfer</option>
                                <option value="interbank" <?php echo $transfer_type === 'interbank' ? 'selected' : ''; ?>>Interbank Transfer</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="amount_min" class="form-label">Min Amount</label>
                            <input type="number" name="amount_min" id="amount_min" class="form-control" 
                                   value="<?php echo htmlspecialchars($amount_min); ?>" step="0.01" min="0" placeholder="0.00">
                        </div>
                        <div class="col-md-2">
                            <label for="amount_max" class="form-label">Max Amount</label>
                            <input type="number" name="amount_max" id="amount_max" class="form-control" 
                                   value="<?php echo htmlspecialchars($amount_max); ?>" step="0.01" min="0" placeholder="0.00">
                        </div>
                        <div class="col-md-2">
                            <div class="filters-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>Apply Filters
                                </button>
                                <a href="history.php" class="btn btn-outline-secondary">
                                    Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            </div>
                
            <!-- Transfers Section -->
            <div class="transactions-section">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Transfer Statement</h5>
                        <div class="transactions-summary">
                            <span class="text-muted">Total: <?php echo $total_records; ?> transfers</span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($transfers)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No transfers found</h5>
                            <?php if (!empty($date_from) || !empty($date_to) || !empty($transfer_type) || !empty($amount_min) || !empty($amount_max)): ?>
                                <p class="text-muted mb-0">No transfers match your current filters. Try adjusting your search criteria.</p>
                            <?php else: ?>
                                <p class="text-muted mb-0">You haven't made any transfers yet.</p>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="transaction-number">#</th>
                                        <th class="sender-name">Recipient</th>
                                        <th class="type-badge">Type</th>
                                        <th class="description">Description</th>
                                        <th class="reference-number">Reference</th>
                                        <th class="amount-value">Amount</th>
                                        <th class="date-time">Date</th>
                                        <th class="status-badge">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $counter = ($page - 1) * $per_page + 1;
                                    foreach ($transfers as $transfer): 
                                    ?>
                                        <tr class="transaction-row">
                                            <td class="transaction-number"><?php echo $counter++; ?></td>
                                            <td class="sender-name">
                                                <div class="sender-info">
                                                    <strong><?php echo htmlspecialchars($transfer['recipient_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($transfer['recipient_account']); ?></small>
                                                </div>
                                            </td>
                                            <td class="type-badge">
                                                <span class="badge <?php echo $transfer['transfer_type'] === 'Local Transfer' ? 'bg-info' : 'bg-warning'; ?>">
                                                    <?php echo $transfer['transfer_type'] === 'Local Transfer' ? 'Local' : 'Interbank'; ?>
                                                </span>
                                            </td>
                                            <td class="description">
                                                <span class="transaction-description"><?php echo htmlspecialchars($transfer['description'] ?? 'Transfer'); ?></span>
                                            </td>
                                            <td class="reference-number">
                                                <span class="reference"><?php echo htmlspecialchars($transfer['reference_number']); ?></span>
                                            </td>
                                            <td class="amount-value">
                                                <span class="amount debit">-$<?php echo number_format($transfer['amount'], 2); ?></span>
                                            </td>
                                            <td class="date-time">
                                                <span class="transaction-date"><?php echo date('M d, Y', strtotime($transfer['created_at'])); ?></span>
                                                <br><small class="text-muted"><?php echo date('h:i A', strtotime($transfer['created_at'])); ?></small>
                                            </td>
                                            <td class="status-badge">
                                                <span class="badge <?php 
                                                    echo $transfer['status'] === 'completed' ? 'bg-success' : 
                                                        ($transfer['status'] === 'pending' ? 'bg-warning' : 'bg-danger'); 
                                                ?>">
                                                    <?php echo ucfirst($transfer['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <div class="card-footer bg-white">
                                <nav aria-label="Transfer history pagination">
                                    <ul class="pagination justify-content-center mb-0">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                                    <i class="fas fa-chevron-left"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <?php if ($page < $total_pages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query(array_filter($_GET, function($key) { return $key !== 'page'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                                    <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            </div>
        </div>
    </div>

    <!-- Transfer Details Modal -->
    <div class="modal fade" id="transferModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-receipt me-2"></i>Transfer Receipt</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="transferModalBody">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading transfer details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Close
                    </button>
                    <button type="button" class="btn btn-primary" onclick="printTransferReceipt()">
                        <i class="fas fa-print me-1"></i>Print Receipt
                    </button>
                </div>
            </div>
        </div>
    </div>

    </div> <!-- End main-content -->

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>

</div> <!-- End main-content-wrapper -->

<script>
    function viewTransferDetails(transferId, transferType) {
        // Load transfer details via AJAX
        fetch(`../ajax/get_transfer_details.php?id=${transferId}&type=${transferType}`)
            .then(response => response.text())
            .then(data => {
                document.getElementById('transferModalBody').innerHTML = data;
                new bootstrap.Modal(document.getElementById('transferModal')).show();
            })
            .catch(error => {
                console.error('Error loading transfer details:', error);
                alert('Error loading transfer details. Please try again.');
            });
    }
    
    // Add click handlers for transaction rows
    document.addEventListener('DOMContentLoaded', function() {
        const transactionRows = document.querySelectorAll('.transaction-row');
        transactionRows.forEach(row => {
            row.addEventListener('click', function() {
                const transferId = this.querySelector('.transaction-number').textContent;
                const typeElement = this.querySelector('.type-badge .badge');
                const transferType = typeElement.textContent.toLowerCase().includes('local') ? 'local_transfer' : 'interbank_transfer';
                viewTransferDetails(transferId, transferType);
            });
        });
    });
    
    function printTransferReceipt() {
        window.print();
    }
    
    function exportToPDF() {
        // Create a form to submit the current filters for PDF export
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '../ajax/export_transfer_history.php';
        
        // Add current filter parameters
        const params = new URLSearchParams(window.location.search);
        for (const [key, value] of params) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }
</script>