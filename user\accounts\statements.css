/**
 * Transfer Statements Page CSS
 * Extends transaction patterns with transfer-specific styling
 * Maintains consistent UI/UX with existing banking pages
 */

/* Import transaction base styles */
@import url('../transactions/transactions.css');

/* Statements Hero Section - Same as transaction hero */
.statements-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 16px;
    padding: 1.5rem 2rem;
    color: white;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.statements-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.statements-hero .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.statements-hero .hero-main {
    flex: 1;
}

.statements-hero .hero-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.statements-hero .hero-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.statements-hero .hero-stats {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

.statements-hero .hero-actions {
    display: flex;
    gap: 0.75rem;
}

/* New Transfer Button - White Background */
.statements-hero .hero-actions .btn-outline-primary {
    background: white !important;
    border: 2px solid #007bff !important;
    color: #007bff !important;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.statements-hero .hero-actions .btn-outline-primary:hover {
    background: #007bff !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.statements-hero .hero-actions .btn-outline-primary:focus {
    background: white !important;
    border-color: #0056b3 !important;
    color: #0056b3 !important;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Statements Section - Same as transactions section */
.statements-section {
    background: var(--background-white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
    overflow: hidden;
}

/* Table Container for Responsive Scrolling */
.table-container {
    overflow-x: auto;
    overflow-y: visible;
    max-width: 100%;
    width: 100%;
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    max-width: calc(100vw - 320px); /* Account for sidebar width */
}

.statements-header {
    background: var(--background-light);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.statements-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
}

.statements-summary {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Statements Table - Responsive Design */
.statements-table {
    width: 100%;
    min-width: 700px;
    border-collapse: collapse;
    font-size: 0.85rem;
    table-layout: fixed;
}

.statements-table th {
    background: var(--background-light);
    padding: 0.75rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.statements-table td {
    padding: 0.75rem 0.75rem;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

/* Column width optimization for 14-inch screens */
.statements-table th:nth-child(1),
.statements-table td:nth-child(1) {
    width: 50px;
    min-width: 50px;
    text-align: center;
}

.statements-table th:nth-child(2),
.statements-table td:nth-child(2) {
    width: 200px;
    min-width: 200px;
}

.statements-table th:nth-child(3),
.statements-table td:nth-child(3) {
    width: 140px;
    min-width: 140px;
}

.statements-table th:nth-child(4),
.statements-table td:nth-child(4) {
    width: 130px;
    min-width: 130px;
    text-align: right;
}

.statements-table th:nth-child(5),
.statements-table td:nth-child(5) {
    width: 90px;
    min-width: 90px;
    text-align: center;
}

.statements-table th:nth-child(6),
.statements-table td:nth-child(6) {
    width: 140px;
    min-width: 140px;
}

.statements-table th:nth-child(7),
.statements-table td:nth-child(7) {
    width: 100px;
    min-width: 100px;
    text-align: center;
}

.statements-table tbody tr:nth-child(even) {
    background: rgba(0,0,0,0.02);
}

.statements-table tbody tr:hover {
    background: rgba(79, 70, 229, 0.05);
    transition: background-color 0.2s ease;
}

/* Transfer-specific type badges with dynamic color scheme */
.type-badge {
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

/* Local Bank Transfer - Blue gradient */
.type-badge.type-local {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-color: #1d4ed8;
}

.type-badge.type-local:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Wire Transfer (International) - Green gradient for better accessibility */
.type-badge.type-international {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #047857;
}

.type-badge.type-international:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Inter-Bank Transfer - Orange gradient */
.type-badge.type-inter-bank,
.type-badge.type-interbank {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    border-color: #c2410c;
}

.type-badge.type-inter-bank:hover,
.type-badge.type-interbank:hover {
    background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
}

/* Bitcoin Transfer - Amber gradient */
.type-badge.type-bitcoin {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-color: #b45309;
}

.type-badge.type-bitcoin:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

/* Default fallback for unknown transfer types */
.type-badge:not(.type-local):not(.type-international):not(.type-inter-bank):not(.type-interbank):not(.type-bitcoin) {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border-color: #374151;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .type-badge {
        transition: none;
    }
    
    .type-badge:hover {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .type-badge {
        border-width: 2px;
        font-weight: 700;
    }
}

/* Recipient info styling */
.recipient-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.recipient-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.recipient-account {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

/* Transfer date styling */
.transfer-date {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.transfer-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* Amount styling - negative for outgoing */
.amount-negative {
    color: #dc2626;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

/* Fee styling */
.fee {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--text-primary);
}

/* Reference styling */
.reference code {
    background: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #374151;
    border: 1px solid #e5e7eb;
}

/* Transfer number styling */
.transfer-number {
    font-weight: 600;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

/* Status badges - same as transactions */
.status-badge {
    display: inline-block;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-size: 0.65rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-completed {
    background: #10b981;
    color: white;
}

.status-pending {
    background: #f59e0b;
    color: white;
}

.status-failed {
    background: #ef4444;
    color: white;
}

.status-cancelled {
    background: #6b7280;
    color: white;
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.empty-state i {
    opacity: 0.5;
}

.empty-state h5 {
    margin: 1rem 0 0.5rem 0;
}

.empty-state p {
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive design for 14-inch screens */
@media (max-width: 1400px) {
    .statements-table {
        min-width: 850px;
        font-size: 0.8rem;
    }

    .statements-table th,
    .statements-table td {
        padding: 0.6rem 0.4rem;
    }

    .recipient-name {
        font-size: 0.85rem;
        font-weight: 500;
    }

    .recipient-account {
        font-size: 0.75rem;
    }

    .type-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .transfer-time {
        font-size: 0.7rem;
    }
}

@media (max-width: 768px) {
    .statements-table {
        min-width: 700px;
    }

    .statements-table th,
    .statements-table td {
        padding: 0.5rem 0.3rem;
        font-size: 0.75rem;
    }

    .recipient-info {
        gap: 0.1rem;
    }
    
    .recipient-name {
        font-size: 0.8rem;
    }
    
    .recipient-account {
        font-size: 0.7rem;
    }
    
    .transfer-date {
        gap: 0.1rem;
    }
    
    .transfer-time {
        font-size: 0.7rem;
    }
}

/* Receipt Modal Button Styling */
.btn-download {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 200px;
    min-width: 200px;
}

.btn-download:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-print {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 200px;
    min-width: 200px;
}

.btn-print:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-close-receipt {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 200px;
    min-width: 200px;
}

.btn-close-receipt:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

/* Receipt Modal Styles - Matching Transactions Page */
.receipt-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
}

.receipt-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 1px solid #e0e0e0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 900px;
    width: 95%;
    max-height: 85vh;
    overflow: hidden;
    scroll-behavior: smooth;
    animation: slideIn 0.3s ease-out;
    font-family: 'Courier New', 'Monaco', monospace;
}

/* Receipt Header */
.receipt-header {
    background: white;
    border-bottom: 1px solid #ddd;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.bank-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.bank-logo i {
    font-size: 2rem;
    color: #3498db;
}

.bank-name {
    font-family: 'Arial', sans-serif;
    letter-spacing: 1px;
}

.receipt-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.25rem;
    font-weight: bold;
    letter-spacing: 2px;
    color: #2c3e50;
    font-family: 'Arial', sans-serif;
}

.receipt-close {
    background: none;
    border: 1px solid #ddd;
    color: #666;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.receipt-close:hover {
    background: #f5f5f5;
    border-color: #999;
}

/* Receipt Body */
.receipt-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
    background: white;
    scroll-behavior: smooth;
    /* Hide scrollbar for webkit browsers */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.receipt-body::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.receipt-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    font-size: 0.9rem;
    line-height: 1.6;
}

/* Receipt Sections */
.receipt-section {
    margin-bottom: 1.5rem;
}

.receipt-section-title {
    font-size: 1rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ddd;
    font-family: 'Arial', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.receipt-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px dotted #ddd;
}

.receipt-row:last-child {
    border-bottom: none;
}

.receipt-label {
    font-weight: 500;
    color: #555;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex: 1;
}

.receipt-value {
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
    font-family: 'Courier New', monospace;
    flex: 1;
    word-break: break-word;
}

/* Special styling for amounts */
.receipt-amount {
    font-size: 1.1rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
}

.receipt-amount.positive {
    color: #27ae60;
}

.receipt-amount.negative {
    color: #e74c3c;
}

/* Transaction status styling */
.receipt-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Arial', sans-serif;
}

.receipt-status.completed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.receipt-status.pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.receipt-status.failed {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Full width sections */
.receipt-section.full-width {
    grid-column: 1 / -1;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
}

/* Receipt Footer */
.receipt-footer {
    background: #f8f9fa;
    border-top: 1px solid #ddd;
    padding: 1.5rem 2rem;
}

.receipt-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.receipt-disclaimer {
    text-align: center;
    color: #666;
    font-size: 0.8rem;
    font-style: italic;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* Print styles */
@media print {
    .statements-hero .hero-actions,
    .filters-section,
    .pagination-wrapper {
        display: none !important;
    }

    .statements-table {
        min-width: auto;
    }

    .statements-table th,
    .statements-table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }
}
