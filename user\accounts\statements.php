<?php
/**
 * Transfer Statements Page
 * Shows outgoing transfers only (debits from user's account)
 * Includes local bank transfers, inter-bank transfers, and wire transfers
 */

// Set page variables
$page_title = 'Transaction Statements';
$current_page = 'statements';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

// Get user data from database
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user account information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Filter settings
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$transfer_type = $_GET['transfer_type'] ?? '';
$amount_min = $_GET['amount_min'] ?? '';
$amount_max = $_GET['amount_max'] ?? '';

// Build WHERE clause for filters (outgoing transfers only - sender_id = current user)
$where_conditions = ["sender_id = ?"];
$params = [$user_id];

if (!empty($date_from)) {
    $where_conditions[] = "DATE(created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(created_at) <= ?";
    $params[] = $date_to;
}

if (!empty($transfer_type)) {
    $where_conditions[] = "transfer_type = ?";
    $params[] = $transfer_type;
}

if (!empty($amount_min)) {
    $where_conditions[] = "amount >= ?";
    $params[] = floatval($amount_min);
}

if (!empty($amount_max)) {
    $where_conditions[] = "amount <= ?";
    $params[] = floatval($amount_max);
}

$where_clause = implode(' AND ', $where_conditions);

// Get outgoing transfers with pagination
$transfers_query = "
    SELECT t.id, t.transaction_id, t.sender_id, t.recipient_id, t.recipient_account,
           t.recipient_name, t.amount, t.currency, t.transfer_type, t.status,
           t.description, t.fee, t.exchange_rate, t.created_at, t.completed_at,
           CASE 
               WHEN t.recipient_id IS NOT NULL THEN CONCAT(a.first_name, ' ', a.last_name)
               ELSE t.recipient_name
           END as recipient_display_name,
           CASE
               WHEN t.recipient_id IS NOT NULL THEN 'Inter-Bank'
               ELSE 'External Bank'
           END as transfer_category
    FROM transfers t
    LEFT JOIN accounts a ON t.recipient_id = a.id
    WHERE $where_clause
    ORDER BY t.created_at DESC
    LIMIT ? OFFSET ?
";

// Add LIMIT and OFFSET to params
$params[] = $per_page;
$params[] = $offset;

try {
    $transfers_result = $db->query($transfers_query, $params);
    $outgoing_transfers = [];
    while ($row = $transfers_result->fetch_assoc()) {
        // Ensure all fields have default values to prevent display issues
        $row['currency'] = $row['currency'] ?: 'USD';
        $row['description'] = $row['description'] ?: 'Transfer';
        $row['recipient_name'] = $row['recipient_name'] ?: 'Unknown Recipient';
        $row['transaction_id'] = $row['transaction_id'] ?: 'REF' . $row['id'];
        $row['status'] = $row['status'] ?: 'completed';
        $row['fee'] = $row['fee'] ?: 0;
        $outgoing_transfers[] = $row;
    }
} catch (Exception $e) {
    error_log("Outgoing transfers query error: " . $e->getMessage());
    $outgoing_transfers = [];
}

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM transfers WHERE $where_clause";
$count_params = array_slice($params, 0, -2); // Remove LIMIT and OFFSET params
$count_result = $db->query($count_query, $count_params);
$total_outgoing_transfers = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_outgoing_transfers / $per_page);

// Get stats data
// 1. Available Balance
$available_balance = $user['balance'];

// 2. Total sent amount (all time)
try {
    $total_sent_query = "SELECT COALESCE(SUM(amount + fee), 0) as total FROM transfers WHERE sender_id = ? AND status = 'completed'";
    $total_sent_result = $db->query($total_sent_query, [$user_id]);
    $total_sent_amount = $total_sent_result->fetch_assoc()['total'];
} catch (Exception $e) {
    $total_sent_amount = 0;
}

// 3. This month sent amount
try {
    $month_sent_query = "SELECT COALESCE(SUM(amount + fee), 0) as total FROM transfers WHERE sender_id = ? AND status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())";
    $month_sent_result = $db->query($month_sent_query, [$user_id]);
    $month_sent_amount = $month_sent_result->fetch_assoc()['total'];
} catch (Exception $e) {
    $month_sent_amount = 0;
}

// 4. Total fees paid
try {
    $total_fees_query = "SELECT COALESCE(SUM(fee), 0) as total FROM transfers WHERE sender_id = ? AND status = 'completed'";
    $total_fees_result = $db->query($total_fees_query, [$user_id]);
    $total_fees_paid = $total_fees_result->fetch_assoc()['total'];
} catch (Exception $e) {
    $total_fees_paid = 0;
}

// Set page title and subtitle
$page_title = 'Transfer Statements';
$page_subtitle = 'Complete record of your outgoing transfers and payments';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Statements CSS -->
<link rel="stylesheet" href="statements.css">

<!-- Dynamic CSS Variables Only -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Mini Hero Section -->
        <div class="statements-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Transfer Statements</div>
                    <div class="hero-subtitle">Complete record of your outgoing transfers and payments</div>
                    <div class="hero-stats">
                        Total Outgoing Transfers: <?php echo number_format($total_outgoing_transfers); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </button>
                    <a href="../transfers/" class="btn btn-outline-primary ms-2">
                        <i class="fas fa-paper-plane me-2"></i>New Transfer
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Total Sent Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total Sent</div>
                    <div class="balance-amount" style="color: #ef4444;">
                        <?php echo formatCurrency($total_sent_amount, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">All Time</div>
                </div>
            </div>

            <!-- This Month Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    <i class="fas fa-calendar-month"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">This Month</div>
                    <div class="balance-amount" style="color: #f59e0b;">
                        <?php echo formatCurrency($month_sent_amount, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle"><?php echo date('M Y'); ?></div>
                </div>
            </div>

            <!-- Total Fees Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total Fees</div>
                    <div class="balance-amount" style="color: #8b5cf6;">
                        <?php echo formatCurrency($total_fees_paid, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">All Transfers</div>
                </div>
            </div>

            <!-- Available Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Available</div>
                    <div class="balance-amount available">
                        <?php echo formatCurrency($available_balance, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">Current Balance</div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section mb-4">
            <div class="filters-card">
                <div class="filters-header">
                    <h5><i class="fas fa-filter me-2"></i>Filter Outgoing Transfers</h5>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>Clear
                    </button>
                </div>
                <form method="GET" class="filters-form">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Date From</label>
                            <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date To</label>
                            <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Transfer Type</label>
                            <select name="transfer_type" class="form-control">
                                <option value="">All Types</option>
                                <option value="local" <?php echo $transfer_type === 'local' ? 'selected' : ''; ?>>Local Bank</option>
                                <option value="international" <?php echo $transfer_type === 'international' ? 'selected' : ''; ?>>Wire/International</option>
                                <option value="bitcoin" <?php echo $transfer_type === 'bitcoin' ? 'selected' : ''; ?>>Bitcoin</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Min Amount</label>
                            <input type="number" name="amount_min" class="form-control" step="0.01" value="<?php echo htmlspecialchars($amount_min); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Max Amount</label>
                            <input type="number" name="amount_max" class="form-control" step="0.01" value="<?php echo htmlspecialchars($amount_max); ?>">
                        </div>
                    </div>
                    <div class="filters-actions mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transfer Statements Table Section -->
        <div class="statements-section">
            <div class="statements-header">
                <h3><i class="fas fa-paper-plane me-2"></i>Outgoing Transfer Statement</h3>
                <div class="statements-summary">
                    Showing <?php echo number_format(count($outgoing_transfers)); ?> of <?php echo number_format($total_outgoing_transfers); ?> transfers
                </div>
            </div>

            <?php if (!empty($outgoing_transfers)): ?>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="statements-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Recipient</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Fee</th>
                            <th>Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($outgoing_transfers as $index => $transfer): ?>
                        <tr data-transfer='<?php echo htmlspecialchars(json_encode($transfer)); ?>' onclick="showTransferDetails(this)" style="cursor: pointer;">
                            <td class="transfer-number">
                                <?php echo ($page - 1) * $per_page + $index + 1; ?>
                            </td>
                            <td class="recipient-info">
                                <div class="recipient-name"><?php echo htmlspecialchars($transfer['recipient_display_name'] ?: $transfer['recipient_name']); ?></div>
                                <div class="recipient-account">****<?php echo substr($transfer['recipient_account'], -4); ?></div>
                            </td>
                            <td class="transfer-type">
                                <span class="type-badge type-<?php echo $transfer['transfer_type']; ?>">
                                    <?php
                                    switch ($transfer['transfer_type']) {
                                        case 'local':
                                            echo '<i class="fas fa-building me-1"></i>Local Bank';
                                            break;
                                        case 'international':
                                            echo '<i class="fas fa-globe me-1"></i>Wire Transfer';
                                            break;
                                        case 'bitcoin':
                                            echo '<i class="fab fa-bitcoin me-1"></i>Bitcoin';
                                            break;
                                        default:
                                            echo '<i class="fas fa-university me-1"></i>Inter-Bank';
                                    }
                                    ?>
                                </span>
                            </td>
                            <td class="amount amount-negative">
                                -<?php echo formatCurrency($transfer['amount'], $transfer['currency']); ?>
                            </td>
                            <td class="fee">
                                <?php if ($transfer['fee'] > 0): ?>
                                    <?php echo formatCurrency($transfer['fee'], $transfer['currency']); ?>
                                <?php else: ?>
                                    <span class="text-success">Free</span>
                                <?php endif; ?>
                            </td>
                            <td class="transfer-date">
                                <?php echo formatDate($transfer['created_at'], 'M j, Y'); ?>
                                <div class="transfer-time"><?php echo formatDate($transfer['created_at'], 'g:i A'); ?></div>
                            </td>
                            <td class="status">
                                <span class="status-badge status-<?php echo $transfer['status']; ?>">
                                    <?php echo ucfirst($transfer['status']); ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination-wrapper">
                <nav aria-label="Transfer pagination">
                    <ul class="pagination">
                        <!-- Previous Page -->
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- Page Numbers -->
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);

                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <!-- Next Page -->
                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>

            <?php else: ?>
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No outgoing transfers found</h5>
                <p class="text-muted">
                    <?php if (!empty($date_from) || !empty($date_to) || !empty($transfer_type) || !empty($amount_min) || !empty($amount_max)): ?>
                        Try adjusting your filters to see more results.
                    <?php else: ?>
                        Your transfer statements will appear here once you start sending money.
                    <?php endif; ?>
                </p>
                <?php if (!empty($date_from) || !empty($date_to) || !empty($transfer_type) || !empty($amount_min) || !empty($amount_max)): ?>
                <button class="btn btn-outline-primary" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>Clear Filters
                </button>
                <?php endif; ?>
                <a href="../transfers/" class="btn btn-primary ms-2">
                    <i class="fas fa-paper-plane me-2"></i>Send Money Now
                </a>
            </div>
            <?php endif; ?>
        </div>

        </div> <!-- End Content Container -->

    </div> <!-- End Main Content -->

    <!-- Bank Receipt Style Modal -->
    <div id="transferModal" class="receipt-modal">
        <div class="modal-overlay" onclick="closeModal()"></div>
        <div class="receipt-container">
            <!-- Receipt Header -->
            <div class="receipt-header">
                <div class="bank-logo">
                    <i class="fas fa-university"></i>
                    <span class="bank-name">PremierBank Pro</span>
                </div>
                <div class="receipt-title">TRANSFER RECEIPT</div>
                <button class="receipt-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Receipt Body -->
            <div class="receipt-body">
                <div id="modalContent" class="receipt-content">
                    <!-- Content will be inserted here -->
                </div>
            </div>

            <!-- Receipt Footer -->
            <div class="receipt-footer">
                <div class="receipt-actions">
                    <button class="btn-print" onclick="printReceipt()">
                        <i class="fas fa-print"></i> Print Receipt
                    </button>
                    <button class="btn-download" onclick="downloadReceipt()">
                        <i class="fas fa-download"></i> Download PDF
                    </button>
                    <button class="btn-close-receipt" onclick="closeModal()">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
                <div class="receipt-disclaimer">
                    <small>This is an electronic transfer receipt. Please retain for your records.</small>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<!-- Include Statements JavaScript -->
<script src="statements.js"></script>
