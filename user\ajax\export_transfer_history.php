<?php
session_start();
require_once '../../config/database.php';
require_once '../../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

$user_id = $_SESSION['user_id'];

// Get database connection
$db = Database::getInstance();
$connection = $db->getConnection();

// Get user data
$stmt = $connection->prepare("SELECT * FROM accounts WHERE id = ?");
$stmt->bind_param('i', $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Get filter parameters from POST
$date_from = isset($_POST['date_from']) ? $_POST['date_from'] : '';
$date_to = isset($_POST['date_to']) ? $_POST['date_to'] : '';
$type_filter = isset($_POST['type']) ? $_POST['type'] : '';
$amount_from = isset($_POST['amount_from']) ? (float)$_POST['amount_from'] : 0;
$amount_to = isset($_POST['amount_to']) ? (float)$_POST['amount_to'] : 0;

// Build WHERE conditions for both tables
$where_conditions = [];
$params = [];

// User ID condition
$where_conditions[] = "sender_id = ?";
$params[] = $user_id;

// Date filters
if ($date_from) {
    $where_conditions[] = "DATE(created_at) >= ?";
    $params[] = $date_from;
}
if ($date_to) {
    $where_conditions[] = "DATE(created_at) <= ?";
    $params[] = $date_to;
}

// Amount filters
if ($amount_from > 0) {
    $where_conditions[] = "amount >= ?";
    $params[] = $amount_from;
}
if ($amount_to > 0) {
    $where_conditions[] = "amount <= ?";
    $params[] = $amount_to;
}

$where_clause = implode(' AND ', $where_conditions);

// Build the queries with correct column names
$local_query = "
    SELECT 
        id,
        beneficiary_account_name as recipient_name,
        beneficiary_account_number as recipient_account,
        amount,
        narration as description,
        transaction_id as reference_number,
        status,
        created_at,
        'Local Transfer' as transfer_type
    FROM local_transfers 
    WHERE $where_clause
";

$interbank_query = "
    SELECT 
        it.id,
        CONCAT(a.first_name, ' ', a.last_name) as recipient_name,
        a.account_number as recipient_account,
        it.amount,
        it.narration as description,
        it.transaction_id as reference_number,
        it.status,
        it.created_at,
        'Interbank Transfer' as transfer_type
    FROM interbank_transfers it
    JOIN accounts a ON it.recipient_id = a.id 
    WHERE $where_clause
";

// Execute queries based on type filter
$transfers = [];

if (!$type_filter || $type_filter === 'all' || $type_filter === 'local') {
    $local_stmt = $connection->prepare($local_query);
    if (!empty($params)) {
        $types = str_repeat('s', count($params));
        $local_stmt->bind_param($types, ...$params);
    }
    $local_stmt->execute();
    $local_result = $local_stmt->get_result();
    while ($row = $local_result->fetch_assoc()) {
        $transfers[] = $row;
    }
}

if (!$type_filter || $type_filter === 'all' || $type_filter === 'interbank') {
    $interbank_stmt = $connection->prepare($interbank_query);
    if (!empty($params)) {
        $types = str_repeat('s', count($params));
        $interbank_stmt->bind_param($types, ...$params);
    }
    $interbank_stmt->execute();
    $interbank_result = $interbank_stmt->get_result();
    while ($row = $interbank_result->fetch_assoc()) {
        $transfers[] = $row;
    }
}

// Sort by created_at descending
usort($transfers, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Calculate totals
$total_amount = 0;
foreach ($transfers as $transfer) {
    if ($transfer['status'] === 'completed') {
        $total_amount += $transfer['amount'];
    }
}

// Set headers for PDF download
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="transfer_history_' . date('Y-m-d') . '.pdf"');
header('Cache-Control: private, max-age=0, must-revalidate');
header('Pragma: public');

// Generate PDF content using HTML to PDF conversion
// For simplicity, we'll generate HTML that can be converted to PDF
// In a production environment, you might want to use a library like TCPDF or mPDF

$html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Transfer History Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .user-info {
            margin-bottom: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .filters {
            margin-bottom: 20px;
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
        }
        .summary {
            margin-bottom: 20px;
            background-color: #d4edda;
            padding: 15px;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .status-completed {
            color: #155724;
            background-color: #d4edda;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .status-pending {
            color: #856404;
            background-color: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .status-failed {
            color: #721c24;
            background-color: #f8d7da;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .amount {
            font-weight: bold;
            color: #dc3545;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Transfer History Report</h1>
        <p>Generated on ' . date('F j, Y \a\t g:i A') . '</p>
    </div>
    
    <div class="user-info">
        <h3>Account Holder Information</h3>
        <p><strong>Name:</strong> ' . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . '</p>
        <p><strong>Account Number:</strong> ' . htmlspecialchars($user['account_number']) . '</p>
        <p><strong>Email:</strong> ' . htmlspecialchars($user['email']) . '</p>
    </div>';

// Add filters information if any filters were applied
if ($date_from || $date_to || $type_filter || $amount_from > 0 || $amount_to > 0) {
    $html .= '<div class="filters">
        <h3>Applied Filters</h3>';
    
    if ($date_from) {
        $html .= '<p><strong>Date From:</strong> ' . htmlspecialchars($date_from) . '</p>';
    }
    if ($date_to) {
        $html .= '<p><strong>Date To:</strong> ' . htmlspecialchars($date_to) . '</p>';
    }
    if ($type_filter) {
        $type_display = $type_filter === 'local' ? 'Local Transfer' : ($type_filter === 'interbank' ? 'Interbank Transfer' : 'All Types');
        $html .= '<p><strong>Transfer Type:</strong> ' . $type_display . '</p>';
    }
    if ($amount_from > 0) {
        $html .= '<p><strong>Minimum Amount:</strong> $' . number_format($amount_from, 2) . '</p>';
    }
    if ($amount_to > 0) {
        $html .= '<p><strong>Maximum Amount:</strong> $' . number_format($amount_to, 2) . '</p>';
    }
    
    $html .= '</div>';
}

$html .= '<div class="summary">
        <h3>Summary</h3>
        <p><strong>Total Transfers:</strong> ' . count($transfers) . '</p>
        <p><strong>Total Amount (Completed):</strong> $' . number_format($total_amount, 2) . '</p>
    </div>
    
    <h3>Transfer Details</h3>';

if (empty($transfers)) {
    $html .= '<p>No transfers found matching the specified criteria.</p>';
} else {
    $html .= '<table>
        <thead>
            <tr>
                <th>Type</th>
                <th>Recipient</th>
                <th>Account</th>
                <th>Description</th>
                <th>Reference</th>
                <th>Amount</th>
                <th>Date</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>';
    
    foreach ($transfers as $transfer) {
        $html .= '<tr>
            <td>' . htmlspecialchars($transfer['transfer_type']) . '</td>
            <td>' . htmlspecialchars($transfer['recipient_name']) . '</td>
            <td>' . htmlspecialchars($transfer['recipient_account']) . '</td>
            <td>' . htmlspecialchars($transfer['description'] ?: 'N/A') . '</td>
            <td>' . htmlspecialchars($transfer['reference_number']) . '</td>
            <td class="amount">-$' . number_format($transfer['amount'], 2) . '</td>
            <td>' . date('M d, Y H:i', strtotime($transfer['created_at'])) . '</td>
            <td><span class="status-' . strtolower($transfer['status']) . '">' . ucfirst($transfer['status']) . '</span></td>
        </tr>';
    }
    
    $html .= '</tbody>
    </table>';
}

$html .= '<div class="footer">
        <p>This report was generated electronically and contains ' . count($transfers) . ' transfer record(s).</p>
        <p>For any questions regarding this report, please contact customer support.</p>
    </div>
</body>
</html>';

// For this implementation, we'll output HTML that can be saved as PDF
// In a production environment, you would use a proper PDF library
echo $html;

// Alternative: If you have a PDF library like TCPDF or mPDF installed, you can use it here
// Example with TCPDF:
/*
require_once('../../vendor/tcpdf/tcpdf.php');

$pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Banking System');
$pdf->SetTitle('Transfer History Report');
$pdf->SetSubject('Transfer History');
$pdf->SetKeywords('Transfer, History, Banking');

$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
$pdf->SetFooterMargin(PDF_MARGIN_FOOTER);
$pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

$pdf->AddPage();
$pdf->writeHTML($html, true, false, true, false, '');

$pdf->Output('transfer_history_' . date('Y-m-d') . '.pdf', 'D');
*/
?>