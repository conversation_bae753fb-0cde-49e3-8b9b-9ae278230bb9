<?php
session_start();
require_once '../../config/database.php';
require_once '../../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo 'Unauthorized';
    exit();
}

$user_id = $_SESSION['user_id'];
$transfer_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$transfer_type = isset($_GET['type']) ? $_GET['type'] : '';

if (!$transfer_id || !$transfer_type) {
    http_response_code(400);
    echo 'Invalid parameters';
    exit();
}

// Get database connection
$db = Database::getInstance();
$connection = $db->getConnection();

// Determine which table to query based on transfer type
if ($transfer_type === 'local_transfer') {
    $table = 'local_transfers';
    $type_display = 'Local Transfer';
    
    // Get transfer details for local transfer
    $stmt = $connection->prepare("SELECT *, beneficiary_account_name as recipient_name, beneficiary_account_number as recipient_account, narration as description FROM local_transfers WHERE id = ? AND sender_id = ?");
    $stmt->bind_param('ii', $transfer_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $transfer = $result->fetch_assoc();
    
} elseif ($transfer_type === 'interbank_transfer') {
    $table = 'interbank_transfers';
    $type_display = 'Interbank Transfer';
    
    // Get transfer details for interbank transfer with recipient info
    $stmt = $connection->prepare("
        SELECT it.*, 
               CONCAT(a.first_name, ' ', a.last_name) as recipient_name,
               a.account_number as recipient_account,
               it.narration as description
        FROM interbank_transfers it 
        JOIN accounts a ON it.recipient_id = a.id 
        WHERE it.id = ? AND it.sender_id = ?
    ");
    $stmt->bind_param('ii', $transfer_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $transfer = $result->fetch_assoc();
    
} else {
    http_response_code(400);
    echo 'Invalid transfer type';
    exit();
}

if (!$transfer) {
    http_response_code(404);
    echo 'Transfer not found';
    exit();
}

// Get sender details
$sender_stmt = $connection->prepare("SELECT first_name, last_name, account_number FROM accounts WHERE id = ?");
$sender_stmt->bind_param('i', $user_id);
$sender_stmt->execute();
$result = $sender_stmt->get_result();
$sender = $result->fetch_assoc();

// Format the transfer details as a receipt
?>
<div class="transfer-receipt">
    <div class="receipt-header text-center mb-4">
        <h4 class="text-primary">Transfer Receipt</h4>
        <p class="text-muted">Reference: <?php echo htmlspecialchars($transfer['transaction_id'] ?? 'N/A'); ?></p>
    </div>
    
    <div class="receipt-body">
        <div class="row mb-3">
            <div class="col-6">
                <strong>Transfer Type:</strong>
            </div>
            <div class="col-6">
                <span class="badge bg-primary"><?php echo $type_display; ?></span>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>Amount:</strong>
            </div>
            <div class="col-6">
                <span class="text-danger fw-bold">$<?php echo number_format($transfer['amount'], 2); ?></span>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>Status:</strong>
            </div>
            <div class="col-6">
                <?php
                $status_class = '';
                switch (strtolower($transfer['status'])) {
                    case 'completed':
                        $status_class = 'bg-success';
                        break;
                    case 'pending':
                        $status_class = 'bg-warning';
                        break;
                    case 'failed':
                        $status_class = 'bg-danger';
                        break;
                    default:
                        $status_class = 'bg-secondary';
                }
                ?>
                <span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($transfer['status']); ?></span>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>Date & Time:</strong>
            </div>
            <div class="col-6">
                <?php echo date('F j, Y \a\t g:i A', strtotime($transfer['created_at'])); ?>
            </div>
        </div>
        
        <hr>
        
        <h6 class="text-primary mb-3">Sender Information</h6>
        <div class="row mb-2">
            <div class="col-6">
                <strong>Name:</strong>
            </div>
            <div class="col-6">
                <?php echo htmlspecialchars($sender['first_name'] . ' ' . $sender['last_name']); ?>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>Account Number:</strong>
            </div>
            <div class="col-6">
                <?php echo htmlspecialchars($sender['account_number']); ?>
            </div>
        </div>
        
        <hr>
        
        <h6 class="text-primary mb-3">Recipient Information</h6>
        <div class="row mb-2">
            <div class="col-6">
                <strong>Name:</strong>
            </div>
            <div class="col-6">
                <?php echo htmlspecialchars($transfer['recipient_name']); ?>
            </div>
        </div>
        
        <div class="row mb-2">
            <div class="col-6">
                <strong>Account Number:</strong>
            </div>
            <div class="col-6">
                <?php echo htmlspecialchars($transfer['recipient_account']); ?>
            </div>
        </div>
        
        <?php if (!empty($transfer['recipient_bank'])): ?>
        <div class="row mb-2">
            <div class="col-6">
                <strong>Bank:</strong>
            </div>
            <div class="col-6">
                <?php echo htmlspecialchars($transfer['recipient_bank']); ?>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($transfer['recipient_routing'])): ?>
        <div class="row mb-3">
            <div class="col-6">
                <strong>Routing Number:</strong>
            </div>
            <div class="col-6">
                <?php echo htmlspecialchars($transfer['recipient_routing']); ?>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($transfer['description'])): ?>
        <hr>
        <h6 class="text-primary mb-3">Transfer Details</h6>
        <div class="row mb-3">
            <div class="col-6">
                <strong>Description:</strong>
            </div>
            <div class="col-6">
                <?php echo htmlspecialchars($transfer['description']); ?>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($transfer['fee'])): ?>
        <hr>
        <div class="row mb-2">
            <div class="col-6">
                <strong>Transfer Fee:</strong>
            </div>
            <div class="col-6">
                $<?php echo number_format($transfer['fee'], 2); ?>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-6">
                <strong>Total Amount:</strong>
            </div>
            <div class="col-6">
                <span class="fw-bold">$<?php echo number_format($transfer['amount'] + $transfer['fee'], 2); ?></span>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <div class="receipt-footer text-center mt-4">
        <small class="text-muted">
            This is an electronic receipt generated on <?php echo date('F j, Y \a\t g:i A'); ?>
        </small>
    </div>
</div>

<style>
.transfer-receipt {
    font-family: 'Courier New', monospace;
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
}

.receipt-header {
    border-bottom: 2px solid #007bff;
    padding-bottom: 15px;
}

.receipt-body .row {
    border-bottom: 1px dotted #ccc;
    padding: 8px 0;
}

.receipt-body .row:last-child {
    border-bottom: none;
}

.receipt-footer {
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

@media print {
    .modal-header, .modal-footer {
        display: none !important;
    }
    
    .transfer-receipt {
        border: none;
        box-shadow: none;
    }
}
</style>