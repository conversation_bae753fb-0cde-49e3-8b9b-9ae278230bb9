<?php
/**
 * Download Certificate PDF
 * Generates and downloads a PDF certificate for approved IRS applications
 */

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include required files
require_once '../../config/config.php';

// Get application ID from URL
$application_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$application_id) {
    header('Location: status.php?error=invalid_application');
    exit();
}

try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Get application details with user verification
    $query = "SELECT ia.*, a.first_name, a.last_name, a.email, a.account_number 
              FROM irs_applications ia 
              LEFT JOIN accounts a ON ia.account_id = a.id 
              WHERE ia.id = ? AND ia.account_id = ? AND ia.status = 'approved'";
    
    $result = $db->query($query, [$application_id, $user_id]);
    $application = $result->fetch_assoc();
    
    if (!$application) {
        header('Location: status.php?error=application_not_found');
        exit();
    }
    
    // Generate PDF content
    $pdf_content = generateCertificatePDF($application);
    
    // Set headers for PDF download
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="IRS_Certificate_' . $application['application_number'] . '.pdf"');
    header('Content-Length: ' . strlen($pdf_content));
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');
    
    echo $pdf_content;
    exit();
    
} catch (Exception $e) {
    error_log("Certificate download error: " . $e->getMessage());
    header('Location: status.php?error=download_failed');
    exit();
}

/**
 * Generate PDF certificate content
 * For now, this creates a simple HTML-to-PDF solution
 * In production, you might want to use a proper PDF library like TCPDF or FPDF
 */
function generateCertificatePDF($application) {
    // Simple HTML-based PDF generation
    // This will work with most browsers' print-to-PDF functionality
    
    $html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>IRS Application Certificate</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: white;
            color: #333;
        }
        .certificate {
            max-width: 800px;
            margin: 0 auto;
            border: 3px solid #206bc4;
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        .header {
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #206bc4;
            margin-bottom: 10px;
        }
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
        }
        .content {
            text-align: left;
            margin: 30px 0;
        }
        .field {
            margin: 15px 0;
            padding: 10px;
            background: rgba(32, 107, 196, 0.05);
            border-left: 4px solid #206bc4;
        }
        .field-label {
            font-weight: bold;
            color: #333;
            display: inline-block;
            width: 200px;
        }
        .field-value {
            color: #555;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #888;
        }
        .signature-section {
            margin-top: 50px;
            text-align: right;
        }
        .signature-line {
            border-top: 1px solid #333;
            width: 200px;
            margin: 20px 0 5px auto;
        }
        .date {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="header">
            <div class="logo">PremierBank Pro</div>
            <div class="title">IRS Application Certificate</div>
            <div class="subtitle">Certificate of Approved Application</div>
        </div>
        
        <div class="content">
            <div class="field">
                <span class="field-label">Application Number:</span>
                <span class="field-value">' . htmlspecialchars($application['application_number']) . '</span>
            </div>
            
            <div class="field">
                <span class="field-label">Applicant Name:</span>
                <span class="field-value">' . htmlspecialchars($application['first_name'] . ' ' . $application['last_name']) . '</span>
            </div>
            
            <div class="field">
                <span class="field-label">Account Number:</span>
                <span class="field-value">' . htmlspecialchars($application['account_number']) . '</span>
            </div>
            
            <div class="field">
                <span class="field-label">Tax Year:</span>
                <span class="field-value">' . htmlspecialchars($application['tax_year']) . '</span>
            </div>
            
            <div class="field">
                <span class="field-label">Filing Status:</span>
                <span class="field-value">' . htmlspecialchars(ucfirst($application['filing_status'])) . '</span>
            </div>
            
            <div class="field">
                <span class="field-label">Annual Income:</span>
                <span class="field-value">$' . number_format($application['annual_income'], 2) . '</span>
            </div>
            
            <div class="field">
                <span class="field-label">Application Date:</span>
                <span class="field-value">' . date('F j, Y', strtotime($application['created_at'])) . '</span>
            </div>
            
            <div class="field">
                <span class="field-label">Approval Date:</span>
                <span class="field-value">' . date('F j, Y', strtotime($application['reviewed_at'])) . '</span>
            </div>
            
            <div class="field">
                <span class="field-label">Status:</span>
                <span class="field-value" style="color: #28a745; font-weight: bold;">APPROVED</span>
            </div>
        </div>
        
        <div class="signature-section">
            <div class="signature-line"></div>
            <div style="font-size: 12px; color: #666;">Authorized Signature</div>
            <div class="date">Date: ' . date('F j, Y') . '</div>
        </div>
        
        <div class="footer">
            <p>This certificate confirms that the above IRS application has been reviewed and approved.</p>
            <p>Certificate generated on ' . date('F j, Y \\a\\t g:i A') . '</p>
            <p>© ' . date('Y') . ' PremierBank Pro. All rights reserved.</p>
        </div>
    </div>
    
    <script>
        // Auto-trigger print dialog for PDF generation
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>';
    
    return $html;
}
?>