<?php
/**
 * AJAX endpoint for fetching application details
 */

// Set content type to JSON
header('Content-Type: application/json');

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Include database connection
require_once __DIR__ . '/../../config/config.php';

// Get database connection
$db = getDB();
$user_id = $_SESSION['user_id'];

// Validate input
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid application ID']);
    exit();
}

$application_id = intval($_GET['id']);

try {
    // Get application details - ensure it belongs to the current user
    $query = "SELECT 
        id, application_number, tax_year, filing_status, annual_income,
        status, created_at, reviewed_at, admin_notes, estimated_refund,
        idme_verification_status
        FROM irs_applications 
        WHERE id = ? AND account_id = ?";
    
    $result = $db->query($query, [$application_id, $user_id]);
    $application = $result->fetch_assoc();
    
    if (!$application) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Application not found']);
        exit();
    }
    
    // Return application details
    echo json_encode([
        'success' => true,
        'application' => $application
    ]);
    
} catch (Exception $e) {
    error_log("Error fetching application details: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
