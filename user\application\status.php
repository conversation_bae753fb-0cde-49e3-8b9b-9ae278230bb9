<?php
/**
 * IRS Application Status Page
 * Allow users to view the status of their IRS tax return assistance applications
 */

// Set page variables
$page_title = 'IRS Application Status';
$current_page = 'irs';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get database connection
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Get user's IRS applications
$applications_query = "SELECT 
    id, application_number, tax_year, filing_status, annual_income,
    status, created_at, reviewed_at, admin_notes, estimated_refund,
    idme_verification_status
    FROM irs_applications 
    WHERE account_id = ? 
    ORDER BY created_at DESC";

$applications_result = $db->query($applications_query, [$user_id]);
$applications = [];
while ($row = $applications_result->fetch_assoc()) {
    $applications[] = $row;
}

// Calculate stats
$total_applications = count($applications);
$pending_applications = count(array_filter($applications, function($a) { return $a['status'] === 'pending'; }));
$approved_applications = count(array_filter($applications, function($a) { return $a['status'] === 'approved'; }));
$declined_applications = count(array_filter($applications, function($a) { return $a['status'] === 'declined'; }));

// Calculate total estimated refund for approved applications
$total_estimated_refund = 0;
foreach ($applications as $application) {
    if ($application['status'] === 'approved' && $application['estimated_refund']) {
        $total_estimated_refund += $application['estimated_refund'];
    }
}

// Include header
require_once '../shared/header.php';
?>



<!-- Include Application CSS -->
<link rel="stylesheet" href="irs.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>

    /* Balance Overview Cards */
    .balance-overview-new {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .balance-card-new {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        border: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .balance-card-new:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        border-color: var(--primary-color);
    }

    .balance-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        flex-shrink: 0;
    }

    .balance-info {
        flex: 1;
    }

    .balance-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .balance-amount {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
        line-height: 1;
    }

    .balance-subtitle {
        font-size: 0.75rem;
        color: var(--text-muted);
        font-weight: 500;
    }

    .status-card {
        border-left: 4px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .status-card.pending {
        border-left-color: var(--warning-color);
        background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
    }

    .status-card.approved {
        border-left-color: var(--success-color);
        background: linear-gradient(135deg, #d1e7dd 0%, #ffffff 100%);
    }

    .status-card.declined {
        border-left-color: var(--danger-color);
        background: linear-gradient(135deg, #f8d7da 0%, #ffffff 100%);
    }

    .timeline-item {
        position: relative;
        padding-left: 2rem;
        margin-bottom: 1rem;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0.5rem;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--border-color);
    }

    .timeline-item.completed::before {
        background: var(--success-color);
    }

    .timeline-item.current::before {
        background: var(--warning-color);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(245, 158, 11, 0); }
        100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0); }
    }
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1; width: 100%;">
        
        <!-- IRS Applications Hero Section -->
        <div class="applications-hero mb-4" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); border-radius: 12px; padding: 2rem; color: white;">
            <div class="hero-content" style="display: flex; justify-content: space-between; align-items: center; gap: 2rem;">
                <div class="hero-main" style="flex: 1;">
                    <div class="hero-title" style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem;">IRS Application Status</div>
                    <div class="hero-subtitle" style="font-size: 1.125rem; opacity: 0.9; margin-bottom: 1rem;">Track the progress of your tax return assistance applications</div>
                    <div class="hero-stats" style="font-size: 0.875rem; opacity: 0.8;">
                        Account: <?php echo htmlspecialchars($user['account_number']); ?> • Total Applications: <?php echo $total_applications; ?> • Approved: <?php echo $approved_applications; ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="apply.php" class="btn btn-outline-light">
                        <i class="fas fa-plus me-2"></i>New Application
                    </a>
                </div>
            </div>
        </div>

        <!-- Success Message -->
        <?php if (isset($_GET['submitted']) && $_GET['submitted'] == '1'): ?>
        <div class="alert alert-success" role="alert" style="margin-bottom: 2rem;">
            <div class="d-flex">
                <div><i class="fas fa-check-circle me-2"></i></div>
                <div>
                    <h4 class="alert-title">Application Submitted Successfully!</h4>
                    <div class="text-muted">
                        Your IRS tax return assistance application has been submitted and is now pending review. 
                        <?php if (isset($_GET['id'])): ?>
                            You can track its progress below.
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Total Applications Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total Applications</div>
                    <div class="balance-amount" style="color: var(--primary-color);">
                        <?php echo $total_applications; ?>
                    </div>
                    <div class="balance-subtitle">Applications Submitted</div>
                </div>
            </div>

            <!-- Pending Applications Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Pending</div>
                    <div class="balance-amount" style="color: var(--warning-color);">
                        <?php echo $pending_applications; ?>
                    </div>
                    <div class="balance-subtitle">Under Review</div>
                </div>
            </div>

            <!-- Approved Applications Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Approved</div>
                    <div class="balance-amount" style="color: var(--success-color);">
                        <?php echo $approved_applications; ?>
                    </div>
                    <div class="balance-subtitle">Successfully Approved</div>
                </div>
            </div>

            <!-- Declined Applications Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Declined</div>
                    <div class="balance-amount" style="color: var(--danger-color);">
                        <?php echo $declined_applications; ?>
                    </div>
                    <div class="balance-subtitle">Applications Declined</div>
                </div>
            </div>
        </div>

        <!-- Applications Section -->
        <div class="applications-section">
            <div class="section-header mb-4">
                <h3><i class="fas fa-file-alt me-2"></i>Your IRS Applications</h3>
                <p class="text-muted">View and track the status of your tax return assistance applications</p>
            </div>
            <?php if (empty($applications)): ?>
                <div class="empty-state" style="text-align: center; padding: 3rem; background: white; border-radius: 12px; border: 1px solid var(--border-color);">
                    <div class="empty-icon" style="font-size: 4rem; color: var(--text-muted); margin-bottom: 1rem;">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h4>No Applications Found</h4>
                    <p class="text-muted">You haven't submitted any IRS tax return assistance applications yet.</p>
                    <a href="apply.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Submit Your First Application
                    </a>
                </div>
            <?php else: ?>
                <!-- Compact Table Layout -->
                <div class="card">
                    <div class="table-responsive">
                        <table class="table table-vcenter table-hover">
                            <thead>
                                <tr>
                                    <th class="w-1">#</th>
                                    <th>Application Details</th>
                                    <th>Tax Year</th>
                                    <th>Filing Status</th>
                                    <th>Annual Income</th>
                                    <th>Estimated Refund</th>
                                    <th>Status</th>
                                    <th>Submitted Date</th>
                                    <th class="w-1">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $row_number = 1;
                                foreach ($applications as $application):
                                ?>
                                <tr>
                                    <!-- Row Number -->
                                    <td>
                                        <span class="text-muted"><?php echo $row_number++; ?></span>
                                    </td>

                                    <!-- Application Details -->
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div class="fw-bold">
                                                <i class="fas fa-file-alt me-1"></i>
                                                #<?php echo htmlspecialchars($application['application_number']); ?>
                                            </div>
                                            <small class="text-muted">
                                                ID.me: 
                                                <span class="badge bg-<?php echo $application['idme_verification_status'] === 'verified' ? 'success' : 'warning'; ?> badge-sm">
                                                    <?php echo ucfirst($application['idme_verification_status'] ?? 'pending'); ?>
                                                </span>
                                            </small>
                                        </div>
                                    </td>

                                    <!-- Tax Year -->
                                    <td>
                                        <span class="fw-bold"><?php echo htmlspecialchars($application['tax_year']); ?></span>
                                    </td>

                                    <!-- Filing Status -->
                                    <td>
                                        <div class="text-truncate" style="max-width: 120px;" title="<?php echo ucwords(str_replace('_', ' ', $application['filing_status'])); ?>">
                                            <?php echo ucwords(str_replace('_', ' ', $application['filing_status'])); ?>
                                        </div>
                                    </td>

                                    <!-- Annual Income -->
                                    <td>
                                        <span class="fw-bold text-primary">
                                            $<?php echo number_format($application['annual_income'], 2); ?>
                                        </span>
                                    </td>

                                    <!-- Estimated Refund -->
                                    <td>
                                        <?php if ($application['estimated_refund']): ?>
                                            <span class="fw-bold text-success">
                                                $<?php echo number_format($application['estimated_refund'], 2); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>

                                    <!-- Status -->
                                    <td>
                                        <?php
                                        $status_colors = [
                                            'pending' => 'warning',
                                            'approved' => 'success',
                                            'declined' => 'danger'
                                        ];
                                        $status_color = $status_colors[$application['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                            <?php echo ucfirst($application['status']); ?>
                                        </span>
                                    </td>

                                    <!-- Submitted Date -->
                                    <td>
                                        <div class="d-flex flex-column">
                                            <div style="font-size: 0.9rem;"><?php echo date('M j, Y', strtotime($application['created_at'])); ?></div>
                                            <small class="text-muted"><?php echo date('g:i A', strtotime($application['created_at'])); ?></small>
                                        </div>
                                    </td>

                                    <!-- Actions -->
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewApplicationDetails('<?php echo $application['id']; ?>')" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($application['status'] === 'approved'): ?>
                                            <button class="btn btn-sm btn-outline-success" onclick="downloadCertificate('<?php echo $application['id']; ?>')" title="Download Certificate">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        </div> <!-- End Content Container -->

    </div> <!-- End Main Content -->

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<!-- Application Details Modal -->
<div class="modal fade" id="applicationDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>Application Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="applicationDetailsContent">
                <!-- Application details content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function viewApplicationDetails(applicationId) {
    // Show loading state
    const modal = document.getElementById('applicationDetailsModal');
    const content = document.getElementById('applicationDetailsContent');

    content.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading application details...</p>
        </div>
    `;

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Fetch application details via AJAX
    fetch(`get-application-details.php?id=${applicationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = generateApplicationDetailsHTML(data.application);
            } else {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading application details: ${data.message || 'Unknown error'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error fetching application details:', error);
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Failed to load application details. Please try again.
                </div>
            `;
        });
}

function generateApplicationDetailsHTML(application) {
    const statusColors = {
        'pending': 'warning',
        'approved': 'success',
        'declined': 'danger'
    };

    const statusColor = statusColors[application.status] || 'secondary';

    return `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-info-circle me-2"></i>Application Information
                </h6>
                <table class="table table-borderless table-sm">
                    <tr>
                        <td class="fw-bold">Application Number:</td>
                        <td>#${application.application_number}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Tax Year:</td>
                        <td>${application.tax_year}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Filing Status:</td>
                        <td>${application.filing_status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Annual Income:</td>
                        <td>$${parseFloat(application.annual_income).toLocaleString()}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Status:</td>
                        <td><span class="badge bg-${statusColor}">${application.status.charAt(0).toUpperCase() + application.status.slice(1)}</span></td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-calendar-alt me-2"></i>Timeline
                </h6>
                <table class="table table-borderless table-sm">
                    <tr>
                        <td class="fw-bold">Submitted:</td>
                        <td>${new Date(application.created_at).toLocaleDateString()} at ${new Date(application.created_at).toLocaleTimeString()}</td>
                    </tr>
                    ${application.reviewed_at ? `
                    <tr>
                        <td class="fw-bold">Reviewed:</td>
                        <td>${new Date(application.reviewed_at).toLocaleDateString()} at ${new Date(application.reviewed_at).toLocaleTimeString()}</td>
                    </tr>
                    ` : ''}
                    ${application.estimated_refund ? `
                    <tr>
                        <td class="fw-bold">Estimated Refund:</td>
                        <td class="text-success fw-bold">$${parseFloat(application.estimated_refund).toLocaleString()}</td>
                    </tr>
                    ` : ''}
                </table>

                <h6 class="text-primary mb-3 mt-4">
                    <i class="fas fa-shield-alt me-2"></i>Verification Status
                </h6>
                <table class="table table-borderless table-sm">
                    <tr>
                        <td class="fw-bold">ID.me Status:</td>
                        <td>
                            <span class="badge bg-${application.idme_verification_status === 'verified' ? 'success' : 'warning'}">
                                ${application.idme_verification_status ? application.idme_verification_status.charAt(0).toUpperCase() + application.idme_verification_status.slice(1) : 'Pending'}
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        ${application.admin_notes ? `
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-sticky-note me-2"></i>Admin Notes
                </h6>
                <div class="alert alert-info">
                    ${application.admin_notes}
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

function downloadCertificate(applicationId) {
    // Create a temporary link to download the certificate
    const link = document.createElement('a');
    link.href = `download-certificate.php?id=${applicationId}`;
    link.download = `irs-certificate-${applicationId}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
</script>