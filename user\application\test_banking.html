<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking Functionality Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .pass { color: green; }
        .fail { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Banking Functionality Test Results</h1>
    
    <div class="test-section">
        <h2>Test 1: Check if apply.php loads without errors</h2>
        <p class="info">Navigate to: <a href="apply.php" target="_blank">apply.php</a></p>
        <p>✓ Check if page loads</p>
        <p>✓ Check if banking section is visible</p>
        <p>✓ Check if banking_option dropdown is present</p>
    </div>

    <div class="test-section">
        <h2>Test 2: JavaScript Functionality</h2>
        <p class="info">Test the banking option toggle functionality:</p>
        <ol>
            <li>Select "Use Existing Bank Account" - should show existing bank info section</li>
            <li>Select "Use External Bank Account" - should show external bank fields</li>
            <li>Select default option - should hide both sections</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Test 3: Form Validation</h2>
        <p class="info">Test form validation:</p>
        <ol>
            <li>Try submitting with "Use External Bank Account" selected but fields empty</li>
            <li>Fill in external bank fields and submit</li>
            <li>Try submitting with "Use Existing Bank Account" selected</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Test 4: Database Integration</h2>
        <p class="info">After successful form submission:</p>
        <ol>
            <li>Check if data is saved to irs_applications table</li>
            <li>Verify banking_option, bank_name, and account_holder_name are populated</li>
        </ol>
    </div>

    <script>
        // Test if the main form exists and has the banking fields
        window.addEventListener('load', function() {
            const results = [];
            
            // Test if we can access the apply.php form via iframe or fetch
            fetch('apply.php')
                .then(response => response.text())
                .then(html => {
                    // Check if banking_option field exists
                    if (html.includes('id="banking_option"')) {
                        results.push('✓ banking_option field found');
                    } else {
                        results.push('✗ banking_option field NOT found');
                    }
                    
                    // Check if bank_name field exists
                    if (html.includes('name="bank_name"')) {
                        results.push('✓ bank_name field found');
                    } else {
                        results.push('✗ bank_name field NOT found');
                    }
                    
                    // Check if account_holder_name field exists
                    if (html.includes('name="account_holder_name"')) {
                        results.push('✓ account_holder_name field found');
                    } else {
                        results.push('✗ account_holder_name field NOT found');
                    }
                    
                    // Check if irs.js is included
                    if (html.includes('irs.js')) {
                        results.push('✓ irs.js script included');
                    } else {
                        results.push('✗ irs.js script NOT included');
                    }
                    
                    // Display results
                    const resultsDiv = document.createElement('div');
                    resultsDiv.className = 'test-section';
                    resultsDiv.innerHTML = '<h2>Automated Test Results</h2>' + 
                        results.map(r => '<p class="' + (r.includes('✓') ? 'pass' : 'fail') + '">' + r + '</p>').join('');
                    document.body.appendChild(resultsDiv);
                })
                .catch(error => {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'test-section';
                    errorDiv.innerHTML = '<h2>Test Error</h2><p class="fail">Could not load apply.php: ' + error.message + '</p>';
                    document.body.appendChild(errorDiv);
                });
        });
    </script>
</body>
</html>