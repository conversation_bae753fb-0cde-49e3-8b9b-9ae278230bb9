<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banking JavaScript Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="email"] { width: 300px; padding: 8px; border: 1px solid #ccc; }
        input[type="radio"] { margin-right: 5px; }
        .radio-group { margin-bottom: 10px; }
        .hidden { display: none; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Banking JavaScript Functionality Test</h1>
    
    <div id="test-results"></div>
    
    <form id="test-form">
        <div class="form-group">
            <label>Banking Option:</label>
            <div class="radio-group">
                <input type="radio" id="existing" name="banking_option" value="existing">
                <label for="existing">Use existing banking information</label>
            </div>
            <div class="radio-group">
                <input type="radio" id="external" name="banking_option" value="external">
                <label for="external">Use external banking information</label>
            </div>
        </div>
        
        <div id="external-bank-fields" class="hidden">
            <div class="form-group">
                <label for="bank_name">Bank Name:</label>
                <input type="text" id="bank_name" name="bank_name">
            </div>
            <div class="form-group">
                <label for="account_holder_name">Account Holder Name:</label>
                <input type="text" id="account_holder_name" name="account_holder_name">
            </div>
        </div>
        
        <div class="form-group">
            <label for="bank_account_number">Bank Account Number:</label>
            <input type="text" id="bank_account_number" name="bank_account_number">
        </div>
        
        <div class="form-group">
            <label for="routing_number">Routing Number:</label>
            <input type="text" id="routing_number" name="routing_number">
        </div>
    </form>
    
    <script src="irs.js"></script>
    <script>
        // Test results container
        const resultsContainer = document.getElementById('test-results');
        
        function addTestResult(message, isSuccess) {
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.textContent = message;
            resultsContainer.appendChild(div);
        }
        
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('DOM Content Loaded', true);
            
            // Test 1: Check if banking fields are initially hidden
            const externalFields = document.getElementById('external-bank-fields');
            if (externalFields && externalFields.classList.contains('hidden')) {
                addTestResult('✓ External banking fields are initially hidden', true);
            } else {
                addTestResult('✗ External banking fields should be initially hidden', false);
            }
            
            // Test 2: Test selecting "existing" option
            const existingRadio = document.getElementById('existing');
            if (existingRadio) {
                existingRadio.click();
                setTimeout(() => {
                    if (externalFields.classList.contains('hidden')) {
                        addTestResult('✓ External fields remain hidden when "existing" is selected', true);
                    } else {
                        addTestResult('✗ External fields should be hidden when "existing" is selected', false);
                    }
                }, 100);
            }
            
            // Test 3: Test selecting "external" option
            const externalRadio = document.getElementById('external');
            if (externalRadio) {
                setTimeout(() => {
                    externalRadio.click();
                    setTimeout(() => {
                        if (!externalFields.classList.contains('hidden')) {
                            addTestResult('✓ External fields are shown when "external" is selected', true);
                        } else {
                            addTestResult('✗ External fields should be shown when "external" is selected', false);
                        }
                    }, 100);
                }, 200);
            }
            
            // Test 4: Check if irs.js functions are available
            setTimeout(() => {
                if (typeof toggleBankFields === 'function') {
                    addTestResult('✓ toggleBankFields function is available', true);
                } else {
                    addTestResult('✗ toggleBankFields function is not available', false);
                }
                
                if (typeof initializeBankingHandler === 'function') {
                    addTestResult('✓ initializeBankingHandler function is available', true);
                } else {
                    addTestResult('✗ initializeBankingHandler function is not available', false);
                }
            }, 300);
        });
    </script>
</body>
</html>