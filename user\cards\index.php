<?php
/**
 * My Cards Page
 * Display user's cards and their balances
 */

// Set page variables
$page_title = 'My Cards';
$current_page = 'cards';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Get user's cards (virtual cards from virtual_cards table)
$cards = [];

// Main account card
$cards[] = [
    'id' => 'main_account',
    'type' => 'Main Account',
    'name' => $user['first_name'] . ' ' . $user['last_name'],
    'number' => $user['account_number'],
    'balance' => $user['balance'],
    'currency' => $user['currency'],
    'status' => 'active',
    'created_at' => $user['created_at'],
    'is_primary' => true
];

// Get virtual cards from virtual_cards table
try {
    $virtual_cards_query = "SELECT * FROM virtual_cards WHERE account_id = ? ORDER BY created_at DESC";
    $virtual_cards_result = $db->query($virtual_cards_query, [$user_id]);

    while ($virtual_card = $virtual_cards_result->fetch_assoc()) {
        $cards[] = [
            'id' => $virtual_card['card_id'],
            'type' => 'Virtual Card',
            'name' => $virtual_card['card_name'] ?? ($user['first_name'] . ' ' . $user['last_name']),
            'number' => $virtual_card['card_number'],
            'balance' => $virtual_card['card_balance'] ?? $virtual_card['balance'] ?? 0,
            'currency' => $virtual_card['currency'] ?? $user['currency'],
            'status' => $virtual_card['status'] ?? 'active',
            'created_at' => $virtual_card['created_at'],
            'is_primary' => false,
            'cvv' => $virtual_card['cvv'] ?? '***',
            'expiry_date' => $virtual_card['expiry_date'] ?? date('Y-m-d', strtotime('+3 years')),
            'card_type' => $virtual_card['card_type'] ?? 'Visa'
        ];
    }
} catch (Exception $e) {
    // Table might not exist, continue without error
    error_log("Virtual cards query error: " . $e->getMessage());
}

// Note: Virtual cards are now loaded from virtual_cards table above
// Removed duplicate virtual card logic to prevent showing extra cards

// Set page title
$page_title = 'My Cards';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Cards CSS -->
<link rel="stylesheet" href="cards.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Cards Hero Section -->
        <div class="cards-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">My Cards</div>
                    <div class="hero-subtitle">Manage your cards and view balances</div>
                    <div class="hero-stats">
                        Total Cards: <?php echo count($cards); ?> • Total Balance: <?php echo formatCurrency(array_sum(array_column($cards, 'balance')), $user['currency']); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="request-card.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Request New Card
                    </a>
                </div>
            </div>
        </div>

        <!-- Cards Container -->
        <div class="cards-container">
        <!-- Cards Overview -->
        <div class="cards-overview">
            <div class="overview-stats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo count($cards); ?></div>
                        <div class="stat-label">Active Cards</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value"><?php echo formatCurrency(array_sum(array_column($cards, 'balance')), $user['currency']); ?></div>
                        <div class="stat-label">Total Balance</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cards Grid -->
        <div class="cards-grid">
            <?php foreach ($cards as $card): ?>
            <div class="card-item <?php echo $card['is_primary'] ? 'primary-card' : 'secondary-card'; ?>" data-card-id="<?php echo $card['id']; ?>">
                <!-- Card Visual -->
                <div class="card-visual">
                    <div class="card-background">
                        <div class="card-pattern"></div>
                        <div class="card-shine"></div>
                    </div>
                    
                    <div class="card-content">
                        <!-- Card Header -->
                        <div class="card-header">
                            <div class="card-type">
                                <?php echo htmlspecialchars($card['type']); ?>
                                <?php if ($card['is_primary']): ?>
                                <span class="primary-badge">PRIMARY</span>
                                <?php endif; ?>
                            </div>
                            <div class="card-logo">
                                <i class="fas fa-university"></i>
                            </div>
                        </div>
                        
                        <!-- Card Number -->
                        <div class="card-number">
                            <span class="number-group">****</span>
                            <span class="number-group">****</span>
                            <span class="number-group">****</span>
                            <span class="number-group"><?php echo substr($card['number'], -4); ?></span>
                        </div>
                        
                        <!-- Card Info -->
                        <div class="card-info">
                            <div class="card-holder">
                                <div class="label">CARD HOLDER</div>
                                <div class="value"><?php echo strtoupper(htmlspecialchars($card['name'])); ?></div>
                            </div>
                            <div class="card-expiry">
                                <div class="label">VALID THRU</div>
                                <div class="value"><?php echo date('m/y', strtotime('+3 years')); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Card Details -->
                <div class="card-details">
                    <div class="balance-section">
                        <div class="balance-label">Available Balance</div>
                        <div class="balance-amount"><?php echo formatCurrency($card['balance'], $card['currency']); ?></div>
                    </div>
                    
                    <div class="card-actions">
                        <button class="btn btn-primary btn-sm" onclick="viewCardDetails('<?php echo $card['id']; ?>')">
                            <i class="fas fa-eye me-1"></i>View Details
                        </button>
                        <?php if (!$card['is_primary']): ?>
                        <a href="manage-card.php?id=<?php echo $card['id']; ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-cog me-1"></i>Manage
                        </a>
                        <?php endif; ?>
                    </div>
                    
                    <div class="card-status">
                        <span class="status-indicator status-<?php echo $card['status']; ?>">
                            <i class="fas fa-circle me-1"></i><?php echo ucfirst($card['status']); ?>
                        </span>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>Quick Actions</h3>
            <div class="actions-grid">
                <div class="action-item" onclick="window.location.href='../accounts/'">
                    <div class="action-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="action-content">
                        <h4>Transfer Between Cards</h4>
                        <p>Move money between your accounts and cards</p>
                    </div>
                </div>
                
                <div class="action-item" onclick="window.location.href='../transactions/'">
                    <div class="action-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="action-content">
                        <h4>Transaction History</h4>
                        <p>View your card transaction history</p>
                    </div>
                </div>
                
                <div class="action-item" onclick="requestNewCard()">
                    <div class="action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="action-content">
                        <h4>Request New Card</h4>
                        <p>Apply for additional cards</p>
                    </div>
                </div>
                
                <div class="action-item" onclick="window.location.href='../support/'">
                    <div class="action-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="action-content">
                        <h4>Card Support</h4>
                        <p>Get help with your cards</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div> <!-- End Content Container -->
</div>

</div>

<!-- Card Details Modal - Compact Version -->
<div class="modal fade" id="cardDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-md" style="max-width: 500px; width: 90%;">
        <div class="modal-content" style="border-radius: 12px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.15);">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; border-radius: 12px 12px 0 0; border-bottom: none; padding: 1rem 1.5rem;">
                <h5 class="modal-title" style="font-weight: 600;">
                    <i class="fas fa-credit-card me-2"></i>Card Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1); opacity: 0.8;"></button>
            </div>
            <div class="modal-body" id="cardDetailsContent" style="padding: 1.25rem 1.75rem;">
                <!-- Card details will be loaded here -->
            </div>
        </div>
    </div>
</div>

</div> <!-- End Main Content Wrapper -->

<!-- User Footer Component -->
<?php require_once '../shared/user_footer.php'; ?>

<script src="cards.js" defer></script>

<script>
// Pass PHP data to JavaScript
window.cardsData = <?php echo json_encode($cards); ?>;
</script>
