<?php
/**
 * Crypto Deposit Page - Completely Rewritten
 * Allow users to submit crypto deposit requests using payment methods
 */

// Set page variables
$page_title = 'Crypto Deposits';
$current_page = 'deposit';
$current_dir = 'crypto';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $crypto_wallet_id = trim($_POST['crypto_wallet']);
        $amount = floatval($_POST['amount']);
        $payment_method_id = trim($_POST['payment_method']);
        $user_transaction_hash = trim($_POST['user_transaction_hash'] ?? '');
        $receipt_file = null;
        $deposit_note = trim($_POST['deposit_note'] ?? '');
        
        // Validation
        if (empty($crypto_wallet_id) || empty($payment_method_id) || $amount <= 0) {
            throw new Exception("Please fill in all required fields with valid values.");
        }
        
        // Validate crypto wallet - check if it belongs to user
        $wallet_check_query = "SELECT * FROM crypto_wallets WHERE wallet_id = ? AND account_id = ? AND status = 'active'";
        $wallet_check_result = $db->query($wallet_check_query, [$crypto_wallet_id, $user_id]);
        $selected_wallet = $wallet_check_result->fetch_assoc();
        
        if (!$selected_wallet) {
            throw new Exception("Invalid crypto wallet selected.");
        }
        
        // Validate payment method
        $payment_method_query = "SELECT * FROM payment_methods WHERE id = ? AND is_active = 1";
        $payment_method_result = $db->query($payment_method_query, [$payment_method_id]);
        $selected_payment_method = $payment_method_result->fetch_assoc();
        
        if (!$selected_payment_method) {
            throw new Exception("Invalid payment method selected.");
        }
        
        $crypto_type = $selected_wallet['cryptocurrency'];
        
        // Validate amount (minimum and maximum limits)
        if ($amount < 0.001) {
            throw new Exception("Minimum deposit amount is 0.001 " . $crypto_type);
        }
        if ($amount > 100000) {
            throw new Exception("Maximum deposit amount is 100,000 " . $crypto_type . " per transaction.");
        }
        
        // Handle file upload
        if (isset($_FILES['receipt_file']) && $_FILES['receipt_file']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = __DIR__ . '/../../uploads/crypto_receipts/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['receipt_file']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'pdf'];
            
            if (!in_array($file_extension, $allowed_extensions)) {
                throw new Exception("Invalid file type. Only JPG, PNG, and PDF files are allowed.");
            }
            
            if ($_FILES['receipt_file']['size'] > 5 * 1024 * 1024) { // 5MB limit
                throw new Exception("File size too large. Maximum size is 5MB.");
            }
            
            $filename = 'receipt_' . $user_id . '_' . time() . '.' . $file_extension;
            $file_path = $upload_dir . $filename;
            
            if (move_uploaded_file($_FILES['receipt_file']['tmp_name'], $file_path)) {
                $receipt_file = 'uploads/crypto_receipts/' . $filename;
            } else {
                throw new Exception("Failed to upload receipt file.");
            }
        }
        
        // Generate deposit number
        $deposit_number = 'DEP_' . strtoupper(bin2hex(random_bytes(8)));
        
        // Calculate USD equivalent (using a simple rate for demo - in production, use real exchange rates)
        $exchange_rates = [
            'BTC' => 45000, 'ETH' => 3000, 'LTC' => 100, 'XRP' => 0.5, 'ADA' => 0.4, 'DOT' => 6, 'USDT' => 1
        ];
        $exchange_rate = $exchange_rates[$crypto_type] ?? 1;
        $usd_equivalent = $amount * $exchange_rate;
        
        // Insert crypto deposit request using correct table structure (without deposit_note)
        $insert_query = "INSERT INTO crypto_deposits (
            account_id, deposit_number, wallet_id, cryptocurrency, deposit_amount,
            usd_equivalent, exchange_rate, admin_wallet_address, user_transaction_hash,
            receipt_file_path, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";

        $deposit_id = $db->insert($insert_query, [
            $user_id, $deposit_number, $crypto_wallet_id, $crypto_type, $amount,
            $usd_equivalent, $exchange_rate, $selected_payment_method['method_name'],
            $user_transaction_hash, $receipt_file
        ]);
        
        if ($deposit_id) {
            $success_message = "Crypto deposit request submitted successfully! Reference: " . $deposit_number;
        } else {
            throw new Exception("Failed to submit deposit request. Please try again.");
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's crypto wallets
$user_crypto_wallets = [];
try {
    $wallets_query = "SELECT * FROM crypto_wallets WHERE account_id = ? AND status = 'active' ORDER BY cryptocurrency";
    $wallets_result = $db->query($wallets_query, [$user_id]);
    if ($wallets_result) {
        while ($row = $wallets_result->fetch_assoc()) {
            $user_crypto_wallets[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Crypto wallets query error: " . $e->getMessage());
}

// Get available payment methods
$payment_methods = [];
try {
    $payment_methods_query = "SELECT * FROM payment_methods WHERE is_active = 1 ORDER BY method_name";
    $payment_methods_result = $db->query($payment_methods_query);
    if ($payment_methods_result) {
        while ($row = $payment_methods_result->fetch_assoc()) {
            $payment_methods[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Payment methods query error: " . $e->getMessage());
}

// Get recent deposits for stats
$recent_deposits = [];
$total_deposits = 0;
$pending_deposits = 0;
$approved_deposits = 0;
try {
    $deposits_query = "SELECT * FROM crypto_deposits WHERE account_id = ? ORDER BY created_at DESC LIMIT 5";
    $deposits_result = $db->query($deposits_query, [$user_id]);
    if ($deposits_result) {
        while ($row = $deposits_result->fetch_assoc()) {
            $recent_deposits[] = $row;
        }
    }
    
    // Get stats
    $stats_query = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved
        FROM crypto_deposits WHERE account_id = ?";
    $stats_result = $db->query($stats_query, [$user_id]);
    $stats = $stats_result->fetch_assoc();
    $total_deposits = $stats['total'];
    $pending_deposits = $stats['pending'];
    $approved_deposits = $stats['approved'];
} catch (Exception $e) {
    error_log("Deposits query error: " . $e->getMessage());
}

// Include header
require_once '../shared/header.php';
?>

<!-- Include Crypto CSS -->
<link rel="stylesheet" href="crypto.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
    
    /* Crypto Deposit Hero Section */
    .crypto-deposit-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        border-radius: 16px;
        padding: 1.5rem 2rem;
        color: white;
        margin-bottom: 2rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        position: relative;
        overflow: hidden;
    }

    .crypto-deposit-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    .crypto-deposit-hero .hero-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 1;
    }

    .crypto-deposit-hero .hero-main {
        flex: 1;
    }

    .crypto-deposit-hero .hero-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .crypto-deposit-hero .hero-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
    }

    .crypto-deposit-hero .hero-stats {
        font-size: 0.9rem;
        opacity: 0.8;
        font-weight: 500;
    }

    .crypto-deposit-hero .hero-actions {
        display: flex;
        gap: 0.75rem;
    }

    .crypto-deposit-hero .hero-actions .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .crypto-deposit-hero .hero-actions .btn-outline-primary {
        background: white !important;
        border: 2px solid var(--primary-color) !important;
        color: var(--primary-color) !important;
    }

    .crypto-deposit-hero .hero-actions .btn-outline-primary:hover {
        background: var(--primary-color) !important;
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .crypto-deposit-hero .hero-actions .btn-primary {
        background: rgba(255,255,255,0.2) !important;
        border: 2px solid rgba(255,255,255,0.3) !important;
        color: white !important;
    }

    .crypto-deposit-hero .hero-actions .btn-primary:hover {
        background: rgba(255,255,255,0.3) !important;
        border-color: rgba(255,255,255,0.5) !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    /* Payment Method Details */
    .payment-method-details {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .payment-method-details h6 {
        color: var(--primary-color);
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .payment-details-content {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 1rem;
    }

    @media (max-width: 768px) {
        .crypto-deposit-hero .hero-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }
        
        .crypto-deposit-hero .hero-actions {
            justify-content: center;
        }
    }
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Crypto Deposit Hero Section -->
        <div class="crypto-deposit-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Crypto Deposits</div>
                    <div class="hero-subtitle">Deposit cryptocurrency to your account using secure payment methods</div>
                    <div class="hero-stats">
                        Total Deposits: <?php echo $total_deposits; ?> • Pending: <?php echo $pending_deposits; ?> • Approved: <?php echo $approved_deposits; ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="request-wallet.php" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>Request Wallet
                    </a>
                    <a href="../dashboard/" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="balance-overview-new mb-4">
            <!-- Total Deposits Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);">
                    <i class="fas fa-download"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total Deposits</div>
                    <div class="balance-amount" style="color: var(--primary-color);">
                        <?php echo $total_deposits; ?>
                    </div>
                    <div class="balance-subtitle">All Time</div>
                </div>
            </div>

            <!-- Pending Deposits Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Pending</div>
                    <div class="balance-amount" style="color: var(--warning-color);">
                        <?php echo $pending_deposits; ?>
                    </div>
                    <div class="balance-subtitle">Awaiting Approval</div>
                </div>
            </div>

            <!-- Approved Deposits Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Approved</div>
                    <div class="balance-amount" style="color: var(--success-color);">
                        <?php echo $approved_deposits; ?>
                    </div>
                    <div class="balance-subtitle">Successfully Processed</div>
                </div>
            </div>

            <!-- Active Wallets Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--info-color) 0%, #0369a1 100%);">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Active Wallets</div>
                    <div class="balance-amount" style="color: var(--info-color);">
                        <?php echo count($user_crypto_wallets); ?>
                    </div>
                    <div class="balance-subtitle">Available for Deposits</div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Main Form Section - Optimized for 14-inch laptop -->
        <div class="row">
            <!-- Left Side - Deposit Form -->
            <div class="col-lg-7">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus-circle me-2" style="color: var(--primary-color);"></i>
                            New Crypto Deposit
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($user_crypto_wallets)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            You don't have any active crypto wallets.
                            <a href="request-wallet.php" class="alert-link">Request a wallet</a> first to make deposits.
                        </div>
                        <?php else: ?>
                        <form method="POST" enctype="multipart/form-data" id="depositForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="crypto_wallet" class="form-label">Select Crypto Wallet</label>
                                        <select class="form-select" id="crypto_wallet" name="crypto_wallet" required>
                                            <option value="">Choose your wallet...</option>
                                            <?php foreach ($user_crypto_wallets as $wallet): ?>
                                            <option value="<?php echo $wallet['wallet_id']; ?>"
                                                    data-crypto="<?php echo $wallet['cryptocurrency']; ?>"
                                                    data-balance="<?php echo $wallet['wallet_balance']; ?>">
                                                <?php echo $wallet['wallet_name']; ?>
                                                (<?php echo $wallet['cryptocurrency']; ?> -
                                                Balance: <?php echo number_format($wallet['wallet_balance'], 8); ?>)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">Deposit Amount</label>
                                        <input type="number" class="form-control" id="amount" name="amount"
                                               min="0.001" max="100000" step="0.00000001"
                                               placeholder="Enter amount" required>
                                        <div class="form-text">Minimum: 0.001 • Maximum: 100,000</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select" id="payment_method" name="payment_method" required onchange="showPaymentDetails()">
                                    <option value="">Select payment method...</option>
                                    <?php foreach ($payment_methods as $method): ?>
                                    <option value="<?php echo $method['id']; ?>"
                                            data-method-details='<?php echo json_encode($method); ?>'>
                                        <?php echo htmlspecialchars($method['method_name']); ?>
                                        <?php if ($method['processing_fee'] > 0): ?>
                                            (Fee: <?php echo $method['fee_type'] === 'percentage' ? $method['processing_fee'] . '%' : '$' . number_format($method['processing_fee'], 2); ?>)
                                        <?php endif; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="user_transaction_hash" class="form-label">Transaction Hash (Optional)</label>
                                <input type="text" class="form-control" id="user_transaction_hash" name="user_transaction_hash"
                                       placeholder="Enter transaction hash if you've already sent payment">
                                <div class="form-text">If you've already made the payment, enter the transaction hash here</div>
                            </div>

                            <div class="mb-3">
                                <label for="receipt_file" class="form-label">Upload Receipt/Proof (Optional)</label>
                                <input type="file" class="form-control" id="receipt_file" name="receipt_file"
                                       accept=".jpg,.jpeg,.png,.pdf">
                                <div class="form-text">Upload payment receipt or proof (JPG, PNG, PDF - Max 5MB)</div>
                            </div>

                            <div class="mb-3">
                                <label for="deposit_note" class="form-label">Additional Notes (Optional)</label>
                                <textarea class="form-control" id="deposit_note" name="deposit_note" rows="3"
                                          placeholder="Any additional information about this deposit..."></textarea>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Deposit Request
                                </button>
                                <button type="reset" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-2"></i>Reset Form
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Right Side - Payment Method Details -->
            <div class="col-lg-5">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-credit-card me-2" style="color: var(--primary-color);"></i>
                            Payment Details
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="payment-method-section" style="display: none;">
                            <div id="payment-method-content">
                                <!-- Payment method details will be displayed here -->
                            </div>
                        </div>
                        <div id="no-payment-method" class="text-center text-muted py-4">
                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                            <p>Select a payment method to view payment details</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        </div> <!-- End Content Container -->
    </div> <!-- End Main Content -->
</div> <!-- End Main Content Wrapper -->

<!-- JavaScript for Payment Method Details -->
<script>
function showPaymentDetails() {
    const select = document.getElementById('payment_method');
    const paymentSection = document.getElementById('payment-method-section');
    const paymentContent = document.getElementById('payment-method-content');
    const noPaymentMethod = document.getElementById('no-payment-method');

    if (select.value) {
        const selectedOption = select.options[select.selectedIndex];
        const methodDetails = JSON.parse(selectedOption.getAttribute('data-method-details'));

        let accountDetails = '';
        try {
            const details = JSON.parse(methodDetails.account_details);
            if (methodDetails.method_type === 'bank_account') {
                accountDetails = `
                    <div class="payment-details-content">
                        <h6><i class="fas fa-university me-2"></i>Bank Transfer Details</h6>
                        <div class="row g-2">
                            <div class="col-6"><strong>Bank Name:</strong></div>
                            <div class="col-6">${details.bank_name || 'N/A'}</div>
                            <div class="col-6"><strong>Account Name:</strong></div>
                            <div class="col-6">${details.account_name || 'N/A'}</div>
                            <div class="col-6"><strong>Account Number:</strong></div>
                            <div class="col-6">
                                <span id="account-number">${details.account_number || 'N/A'}</span>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('${details.account_number}', this)">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <div class="col-6"><strong>Routing Number:</strong></div>
                            <div class="col-6">
                                <span id="routing-number">${details.routing_number || 'N/A'}</span>
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="copyToClipboard('${details.routing_number}', this)">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            } else if (methodDetails.method_type === 'crypto_wallet') {
                accountDetails = `
                    <div class="payment-details-content">
                        <h6><i class="fas fa-wallet me-2"></i>Crypto Wallet Details</h6>
                        <div class="row g-2">
                            <div class="col-12"><strong>Wallet Address:</strong></div>
                            <div class="col-12">
                                <div class="input-group">
                                    <input type="text" class="form-control" value="${details.wallet_address || 'N/A'}" readonly>
                                    <button type="button" class="btn btn-outline-primary" onclick="copyToClipboard('${details.wallet_address}', this)">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-6"><strong>Network:</strong></div>
                            <div class="col-6">${details.network || 'N/A'}</div>
                        </div>
                    </div>
                `;
            } else {
                accountDetails = `
                    <div class="payment-details-content">
                        <h6><i class="fas fa-credit-card me-2"></i>Payment Method</h6>
                        <p><strong>${methodDetails.method_name}</strong></p>
                        <p class="text-muted">${methodDetails.description || 'No additional details available.'}</p>
                    </div>
                `;
            }
        } catch (e) {
            accountDetails = `
                <div class="payment-details-content">
                    <h6><i class="fas fa-credit-card me-2"></i>Payment Method</h6>
                    <p><strong>${methodDetails.method_name}</strong></p>
                    <p class="text-muted">${methodDetails.description || 'No additional details available.'}</p>
                </div>
            `;
        }

        paymentContent.innerHTML = `
            ${accountDetails}
            <div class="mt-3 p-3 bg-light rounded">
                <h6 class="text-primary"><i class="fas fa-info-circle me-2"></i>Instructions</h6>
                <ol class="mb-0">
                    <li>Copy the payment details above</li>
                    <li>Make your payment using these details</li>
                    <li>Enter the transaction hash (if available)</li>
                    <li>Upload payment receipt (optional)</li>
                    <li>Submit this form for admin approval</li>
                </ol>
            </div>
        `;

        paymentSection.style.display = 'block';
        noPaymentMethod.style.display = 'none';
    } else {
        paymentSection.style.display = 'none';
        noPaymentMethod.style.display = 'block';
    }
}

function copyToClipboard(text, button) {
    navigator.clipboard.writeText(text).then(function() {
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.remove('btn-outline-primary');
        button.classList.add('btn-success');

        setTimeout(function() {
            button.innerHTML = originalIcon;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Failed to copy to clipboard');
    });
}

// Form validation
document.getElementById('depositForm')?.addEventListener('submit', function(e) {
    const amount = parseFloat(document.getElementById('amount').value);
    const wallet = document.getElementById('crypto_wallet').value;
    const paymentMethod = document.getElementById('payment_method').value;

    if (!wallet) {
        e.preventDefault();
        alert('Please select a crypto wallet');
        return;
    }

    if (!paymentMethod) {
        e.preventDefault();
        alert('Please select a payment method');
        return;
    }

    if (amount < 0.001 || amount > 100000) {
        e.preventDefault();
        alert('Amount must be between 0.001 and 100,000');
        return;
    }
});
</script>

<?php require_once '../shared/footer.php'; ?>
