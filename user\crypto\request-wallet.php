<?php
/**
 * Crypto Wallet Request Page
 * Allow users to request new crypto wallets or enter existing wallet addresses
 */

// Set page variables
$page_title = 'Request Crypto Wallet';
$current_page = 'request-wallet';
$current_dir = 'crypto';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $cryptocurrency = trim($_POST['cryptocurrency']);
        $wallet_type = trim($_POST['wallet_type']);
        $purpose = trim($_POST['purpose']);
        $existing_address = trim($_POST['existing_address'] ?? '');
        
        // Validation
        if (empty($cryptocurrency) || empty($wallet_type) || empty($purpose)) {
            throw new Exception("Please fill in all required fields.");
        }
        
        // Validate cryptocurrency type
        $valid_crypto_types = ['BTC', 'ETH', 'LTC', 'XRP', 'ADA', 'DOT', 'USDT'];
        if (!in_array($cryptocurrency, $valid_crypto_types)) {
            throw new Exception("Invalid cryptocurrency type selected.");
        }
        
        // Check if user already has a wallet for this cryptocurrency
        $existing_wallet_query = "SELECT COUNT(*) as count FROM crypto_wallets WHERE account_id = ? AND cryptocurrency = ?";
        $existing_wallet_result = $db->query($existing_wallet_query, [$user_id, $cryptocurrency]);
        $existing_count = $existing_wallet_result->fetch_assoc()['count'];
        
        if ($existing_count > 0) {
            throw new Exception("You already have a wallet for " . $cryptocurrency . ". Each user can only have one wallet per cryptocurrency.");
        }
        
        // Check if user has pending application for this cryptocurrency
        $pending_app_query = "SELECT COUNT(*) as count FROM crypto_wallet_applications WHERE account_id = ? AND cryptocurrency = ? AND status = 'pending'";
        $pending_app_result = $db->query($pending_app_query, [$user_id, $cryptocurrency]);
        $pending_count = $pending_app_result->fetch_assoc()['count'];
        
        if ($pending_count > 0) {
            throw new Exception("You already have a pending application for " . $cryptocurrency . ". Please wait for admin approval.");
        }
        
        // Insert wallet application (using correct database columns)
        $insert_query = "INSERT INTO crypto_wallet_applications (
            account_id, cryptocurrency, wallet_type, purpose, status, applied_at
        ) VALUES (?, ?, ?, ?, 'pending', NOW())";

        $application_id = $db->insert($insert_query, [
            $user_id, $cryptocurrency, $wallet_type, $purpose
        ]);
        
        if ($application_id) {
            $success_message = "Crypto wallet request submitted successfully! Application ID: " . $application_id;
        } else {
            throw new Exception("Failed to submit wallet request. Please try again.");
        }
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's existing applications
$applications_query = "SELECT * FROM crypto_wallet_applications WHERE account_id = ? ORDER BY applied_at DESC";
$applications_result = $db->query($applications_query, [$user_id]);
$applications = [];
while ($row = $applications_result->fetch_assoc()) {
    $applications[] = $row;
}

// Get user's existing wallets
$wallets_query = "SELECT * FROM crypto_wallets WHERE account_id = ? ORDER BY cryptocurrency";
$wallets_result = $db->query($wallets_query, [$user_id]);
$existing_wallets = [];
while ($row = $wallets_result->fetch_assoc()) {
    $existing_wallets[] = $row;
}

// Include header
require_once '../shared/header.php';
?>

<!-- Include Crypto CSS -->
<link rel="stylesheet" href="crypto.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>

    /* Crypto Wallet Request Hero Section */
    .crypto-wallet-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        border-radius: 16px;
        padding: 1.5rem 2rem;
        color: white;
        margin-bottom: 2rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        position: relative;
        overflow: hidden;
    }

    .crypto-wallet-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
        pointer-events: none;
    }

    .crypto-wallet-hero .hero-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 1;
    }

    .crypto-wallet-hero .hero-main {
        flex: 1;
    }

    .crypto-wallet-hero .hero-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .crypto-wallet-hero .hero-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
    }

    .crypto-wallet-hero .hero-stats {
        font-size: 0.9rem;
        opacity: 0.8;
        font-weight: 500;
    }

    .crypto-wallet-hero .hero-actions {
        display: flex;
        gap: 0.75rem;
    }

    .crypto-wallet-hero .hero-actions .btn-outline-primary {
        background: white !important;
        border: 2px solid var(--primary-color) !important;
        color: var(--primary-color) !important;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .crypto-wallet-hero .hero-actions .btn-outline-primary:hover {
        background: var(--primary-color) !important;
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .crypto-wallet-hero .hero-actions .btn-primary {
        background: rgba(255,255,255,0.2) !important;
        border: 2px solid rgba(255,255,255,0.3) !important;
        color: white !important;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .crypto-wallet-hero .hero-actions .btn-primary:hover {
        background: rgba(255,255,255,0.3) !important;
        border-color: rgba(255,255,255,0.5) !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    @media (max-width: 768px) {
        .crypto-wallet-hero .hero-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .crypto-wallet-hero .hero-actions {
            justify-content: center;
        }
    }
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Crypto Wallet Request Hero Section -->
        <div class="crypto-wallet-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Request Crypto Wallet</div>
                    <div class="hero-subtitle">Apply for a new cryptocurrency wallet or register an existing one</div>
                    <div class="hero-stats">
                        Existing Wallets: <?php echo count($existing_wallets); ?> • Pending Applications: <?php echo count(array_filter($applications, function($a) { return $a['status'] === 'pending'; })); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="deposit.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-download me-2"></i>Deposit Crypto
                    </a>
                    <a href="../dashboard/" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="balance-overview-new mb-4">
            <!-- Existing Wallets Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Active Wallets</div>
                    <div class="balance-amount" style="color: var(--primary-color);">
                        <?php echo count($existing_wallets); ?>
                    </div>
                    <div class="balance-subtitle">Approved Wallets</div>
                </div>
            </div>

            <!-- Pending Applications Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Pending</div>
                    <div class="balance-amount" style="color: var(--warning-color);">
                        <?php echo count(array_filter($applications, function($a) { return $a['status'] === 'pending'; })); ?>
                    </div>
                    <div class="balance-subtitle">Under Review</div>
                </div>
            </div>

            <!-- Approved Applications Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Approved</div>
                    <div class="balance-amount" style="color: var(--success-color);">
                        <?php echo count(array_filter($applications, function($a) { return $a['status'] === 'approved'; })); ?>
                    </div>
                    <div class="balance-subtitle">Successfully Approved</div>
                </div>
            </div>

            <!-- Rejected Applications Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Rejected</div>
                    <div class="balance-amount" style="color: var(--danger-color);">
                        <?php echo count(array_filter($applications, function($a) { return $a['status'] === 'rejected'; })); ?>
                    </div>
                    <div class="balance-subtitle">Applications Rejected</div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Wallet Request Form -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card crypto-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-plus me-2"></i>
                            New Wallet Request
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" id="walletRequestForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cryptocurrency" class="form-label">Cryptocurrency</label>
                                        <select class="form-select" id="cryptocurrency" name="cryptocurrency" required>
                                            <option value="">Select Cryptocurrency</option>
                                            <option value="BTC">Bitcoin (BTC)</option>
                                            <option value="ETH">Ethereum (ETH)</option>
                                            <option value="LTC">Litecoin (LTC)</option>
                                            <option value="XRP">Ripple (XRP)</option>
                                            <option value="ADA">Cardano (ADA)</option>
                                            <option value="DOT">Polkadot (DOT)</option>
                                            <option value="USDT">Tether (USDT)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="wallet_type" class="form-label">Wallet Type</label>
                                        <select class="form-select" id="wallet_type" name="wallet_type" required>
                                            <option value="">Select Wallet Type</option>
                                            <option value="new_generation">Generate New Wallet</option>
                                            <option value="existing_import">Import Existing Wallet</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3" id="existing_address_section" style="display: none;">
                                <label for="existing_address" class="form-label">Existing Wallet Address</label>
                                <input type="text" class="form-control" id="existing_address" name="existing_address" 
                                       placeholder="Enter your existing wallet address">
                                <div class="form-text">Only required if importing an existing wallet</div>
                            </div>

                            <div class="mb-3">
                                <label for="purpose" class="form-label">Purpose/Description</label>
                                <textarea class="form-control" id="purpose" name="purpose" rows="3" required
                                          placeholder="Describe why you need this wallet and how you plan to use it"></textarea>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="button" class="btn btn-outline-secondary me-md-2" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>Reset Form
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Applications History Sidebar -->
            <div class="col-lg-4">
                <div class="card crypto-card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>
                            Application History
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($applications)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-2x mb-3"></i>
                            <p>No applications yet</p>
                        </div>
                        <?php else: ?>
                        <div class="application-list">
                            <?php foreach ($applications as $application): ?>
                            <div class="application-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="fw-bold"><?php echo htmlspecialchars($application['cryptocurrency']); ?></div>
                                        <div class="text-muted small"><?php echo date('M j, Y', strtotime($application['applied_at'])); ?></div>
                                    </div>
                                    <span class="badge bg-<?php echo $application['status'] === 'approved' ? 'success' : ($application['status'] === 'rejected' ? 'danger' : 'warning'); ?>">
                                        <?php echo ucfirst($application['status']); ?>
                                    </span>
                                </div>
                                <div class="text-muted small mt-1">
                                    Type: <?php echo ucfirst(str_replace('_', ' ', $application['wallet_type'])); ?>
                                </div>
                                <?php if ($application['admin_notes']): ?>
                                <div class="text-muted small mt-1">
                                    <strong>Admin Notes:</strong> <?php echo htmlspecialchars($application['admin_notes']); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        </div> <!-- End Content Container -->

    </div> <!-- End Main Content -->

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<script>
    // Show/hide existing address field based on wallet type
    document.getElementById('wallet_type').addEventListener('change', function() {
        const walletType = this.value;
        const existingAddressSection = document.getElementById('existing_address_section');
        const existingAddressInput = document.getElementById('existing_address');
        
        if (walletType === 'existing_import') {
            existingAddressSection.style.display = 'block';
            existingAddressInput.required = true;
        } else {
            existingAddressSection.style.display = 'none';
            existingAddressInput.required = false;
            existingAddressInput.value = '';
        }
    });

    function resetForm() {
        document.getElementById('walletRequestForm').reset();
        document.getElementById('existing_address_section').style.display = 'none';
        document.getElementById('existing_address').required = false;
    }
</script>

