<?php
/**
 * Enhanced User Dashboard - Professional Banking Interface
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

// Get site settings for branding
$site_name = getBankName();
$site_logo = getSiteLogo();

// Get user data from database
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user account information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

// Get user's virtual cards (all cards, not just one)
$virtual_cards = [];
$total_virtual_card_balance = 0;
try {
    $cards_query = "SELECT * FROM virtual_cards WHERE account_id = ? ORDER BY created_at DESC";
    $cards_result = $db->query($cards_query, [$user_id]);
    while ($card = $cards_result->fetch_assoc()) {
        $virtual_cards[] = $card;
        // Sum up all card balances
        $card_balance = $card['card_balance'] ?? $card['balance'] ?? 0;
        $total_virtual_card_balance += $card_balance;
    }
} catch (Exception $e) {
    // Table might not exist, continue without error
    error_log("Virtual cards query error: " . $e->getMessage());
}

// Get user's KYC documents (ID cards)
$kyc_documents = [];
$kyc_application = null;
try {
    // Get KYC application status
    $kyc_query = "SELECT * FROM kyc_applications WHERE user_id = ? ORDER BY created_at DESC LIMIT 1";
    $kyc_result = $db->query($kyc_query, [$user_id]);
    if ($kyc_result && $kyc_result->num_rows > 0) {
        $kyc_application = $kyc_result->fetch_assoc();
    }

    // Get uploaded KYC documents (ID cards, passports, etc.)
    $docs_query = "SELECT * FROM user_documents WHERE user_id = ? AND document_type IN ('id_card', 'passport', 'drivers_license', 'kyc_selfie') ORDER BY uploaded_at DESC";
    $docs_result = $db->query($docs_query, [$user_id]);
    if ($docs_result) {
        while ($doc = $docs_result->fetch_assoc()) {
            $kyc_documents[] = $doc;
        }
    }
} catch (Exception $e) {
    // Tables might not exist, continue without error
    error_log("KYC documents query error: " . $e->getMessage());
}

// Get user's first card for backward compatibility
$user_card = !empty($virtual_cards) ? $virtual_cards[0] : null;

// Get crypto accounts (all wallets, not just balance)
$crypto_accounts = [];
$total_crypto_balance = 0;
try {
    $crypto_query = "SELECT * FROM crypto_wallets WHERE account_id = ? ORDER BY cryptocurrency";
    $crypto_result = $db->query($crypto_query, [$user_id]);
    while ($crypto = $crypto_result->fetch_assoc()) {
        $crypto_accounts[] = $crypto;
        // Sum up all wallet balances
        $balance = $crypto['wallet_balance'] ?? $crypto['balance'] ?? 0;
        $total_crypto_balance += $balance;
    }
} catch (Exception $e) {
    // Table might not exist, set to 0
    $total_crypto_balance = 0;
}

// Get comprehensive transaction data from multiple sources
$all_transactions = [];

// 1. Account transactions (admin-initiated)
$account_trans_query = "SELECT
                          'account' as source,
                          id,
                          transaction_type,
                          amount,
                          currency,
                          description,
                          reference_number,
                          category,
                          status,
                          processed_by,
                          created_at,
                          CASE
                            WHEN processed_by IS NOT NULL THEN 'Admin'
                            ELSE 'System'
                          END as initiator_type
                        FROM account_transactions
                        WHERE account_id = ?
                        ORDER BY created_at DESC LIMIT 20";
$account_trans_result = $db->query($account_trans_query, [$user_id]);
while ($trans = $account_trans_result->fetch_assoc()) {
    $all_transactions[] = $trans;
}

// 2. Transfer transactions (user-initiated)
$transfer_query = "SELECT
                     'transfer' as source,
                     id,
                     CASE
                       WHEN sender_id = ? THEN 'debit'
                       ELSE 'credit'
                     END as transaction_type,
                     amount,
                     currency,
                     CASE
                       WHEN sender_id = ? THEN CONCAT('Transfer to ', recipient_name, ' (', recipient_account, ')')
                       ELSE CONCAT('Transfer from ', (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = sender_id))
                     END as description,
                     transaction_id as reference_number,
                     transfer_type as category,
                     status,
                     NULL as processed_by,
                     created_at,
                     'User' as initiator_type
                   FROM transfers
                   WHERE (sender_id = ? OR recipient_id = ?)
                   AND status = 'completed'
                   ORDER BY created_at DESC LIMIT 20";
$transfer_result = $db->query($transfer_query, [$user_id, $user_id, $user_id, $user_id]);
while ($trans = $transfer_result->fetch_assoc()) {
    $all_transactions[] = $trans;
}

// 3. Virtual card transactions (if table exists and user has card)
if ($user_card) {
    try {
        $card_trans_query = "SELECT
                               'virtual_card' as source,
                               id,
                               transaction_type,
                               amount,
                               currency,
                               description,
                               reference_number,
                               'virtual_card' as category,
                               status,
                               NULL as processed_by,
                               created_at,
                               'User' as initiator_type
                             FROM virtual_card_transactions
                             WHERE card_id = ?
                             ORDER BY created_at DESC LIMIT 10";
        $card_trans_result = $db->query($card_trans_query, [$user_card['card_id']]);
        while ($trans = $card_trans_result->fetch_assoc()) {
            $all_transactions[] = $trans;
        }
    } catch (Exception $e) {
        // Table might not exist, skip virtual card transactions
    }
}

// Sort all transactions by date
usort($all_transactions, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Take only the most recent 10 transactions for dashboard display
$recent_transactions = array_slice($all_transactions, 0, 10);

// Calculate running balance for transactions
$running_balance = $user['balance'];
foreach ($recent_transactions as &$transaction) {
    $transaction['balance_after'] = $running_balance;
    if ($transaction['transaction_type'] == 'credit') {
        $running_balance -= $transaction['amount'];
    } else {
        $running_balance += $transaction['amount'];
    }
}
unset($transaction);

// Reverse to show oldest first for balance calculation, then reverse back
$recent_transactions = array_reverse($recent_transactions);
$running_balance = $user['balance'];
foreach ($recent_transactions as &$transaction) {
    if ($transaction['transaction_type'] == 'credit') {
        $running_balance -= $transaction['amount'];
    } else {
        $running_balance += $transaction['amount'];
    }
    $transaction['balance_before'] = $running_balance;
}
unset($transaction);
$recent_transactions = array_reverse($recent_transactions);

// Set page title
$page_title = 'Dashboard';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Dashboard CSS -->
<link rel="stylesheet" href="dashboard.css">

<style>
    /* Dynamic CSS Variables */
    <?php echo getInlineDynamicCSS(); ?>
</style>

</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content">

        <!-- Dashboard Hero Section -->
        <div class="dashboard-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-balance">
                        <?php
                        $user_currency = getUserCurrency($user_id);
                        echo formatCurrency($user['balance'], $user_currency);
                        ?>
                    </div>
                    <div class="hero-welcome">
                        Welcome back, <?php echo htmlspecialchars($user['first_name']); ?>!
                    </div>
                    <div class="hero-account-info">
                        Account: <?php echo htmlspecialchars($user['account_number']); ?> • <?php echo ucfirst($user['account_type']); ?> Account
                    </div>
                </div>
                <div class="hero-status">
                    <div class="account-status">
                        <span class="status-label">Account Status</span>
                        <span class="status-badge status-<?php echo strtolower($user['status']); ?>">
                            <?php echo ucfirst($user['status']); ?>
                        </span>
                    </div>
                    <div class="last-login">
                        <span class="time-label">Last Login</span>
                        <span class="time-value"><?php echo date('M d, Y H:i'); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance Overview Section - Redesigned -->
        <div class="balance-overview-new">
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Available Balance</div>
                    <div class="balance-amount">
                        <?php
                        // Use proper currency formatting with user's currency
                        echo formatCurrency($user['balance'], $user['currency']);
                        ?>
                    </div>
                    <div class="balance-subtitle">Ready for transactions</div>
                </div>
            </div>

            <div class="balance-card-new">
                <div class="balance-icon crypto">
                    <i class="fab fa-bitcoin"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Wallet Balance</div>
                    <div class="balance-amount">
                        <?php
                        // Display crypto balance without dollar sign since it's a combination of different cryptocurrencies
                        $formatted_crypto = number_format($total_crypto_balance, 2);
                        echo $formatted_crypto;
                        ?>
                    </div>
                    <div class="balance-subtitle">Combined crypto value</div>
                </div>
            </div>

            <div class="balance-card-new">
                <div class="balance-icon card">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Virtual Card Balance</div>
                    <div class="balance-amount">
                        <?php
                        // Use proper currency formatting with user's currency
                        echo formatCurrency($total_virtual_card_balance, $user['currency']);
                        ?>
                    </div>
                    <div class="balance-subtitle">Total card balances</div>
                </div>
            </div>
        </div>

        <!-- Quick Links Section -->
        <div class="quick-links mb-4">
            <!-- Contextual Prompt -->
            <div class="contextual-prompt mb-3">
                <h4 class="prompt-text text-center">What would you like to do today?</h4>
            </div>
            
            <h3>Quick Links</h3>
            <div class="quick-links-grid">
                <a href="../statements/" class="quick-link">
                    <i class="fas fa-file-alt"></i>
                    <span>Account Statement</span>
                </a>

                <a href="../transfers/wire/" class="quick-link">
                    <i class="fas fa-university"></i>
                    <span>Wire Transfer</span>
                </a>

                <a href="../transfers/crypto/" class="quick-link">
                    <i class="fab fa-bitcoin"></i>
                    <span>BTC Transfer</span>
                </a>

                <a href="../support/" class="quick-link">
                    <i class="fas fa-headset"></i>
                    <span>Contact Support</span>
                </a>
            </div>
        </div>

        <!-- Enhanced Virtual Cards & Crypto Wallets Section -->
        <div class="row row-cards mt-4 mb-4">
            <!-- Virtual Cards with Full Width Display -->
            <div class="col-lg-7">
                <div class="card">
                    <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                        <h3 class="card-title text-white mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            My Virtual Cards
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($virtual_cards)): ?>
                        <!-- Card Tabs -->
                        <div class="card-tabs-container">
                            <div class="card-tabs">
                                <?php foreach ($virtual_cards as $index => $card): ?>
                                <button class="card-tab <?php echo $index === 0 ? 'active' : ''; ?>"
                                        onclick="showCard(<?php echo $index; ?>)"
                                        data-card="<?php echo $index; ?>">
                                    <i class="fas fa-credit-card me-1"></i>
                                    Card <?php echo $index + 1; ?>
                                </button>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Card Content - Full Width Display -->
                        <div class="card-content-container">
                            <?php foreach ($virtual_cards as $index => $card): ?>
                            <div class="card-content <?php echo $index === 0 ? 'active' : ''; ?>" id="card-content-<?php echo $index; ?>">
                                <!-- Card Display - Full Width -->
                                <div class="card-display-full">
                                    <!-- Enhanced Credit Card with Flip Functionality -->
                                    <div class="card-flip-container-full">
                                        <div class="card-flip-inner" id="card-flip-<?php echo $index; ?>" onclick="flipCard(<?php echo $index; ?>)">
                                            <!-- Front of Card -->
                                            <div class="card-front">
                                                <!-- Card Background Pattern -->
                                                <div class="card-pattern"></div>

                                                <!-- Card Header -->
                                                <div class="card-header-content">
                                                    <div class="card-brand-section">
                                                        <!-- Card Brand Logo (Visa/MasterCard) -->
                                                        <div class="card-brand">
                                                            <?php
                                                            $cardType = strtolower($card['card_type'] ?? 'visa');
                                                            if ($cardType === 'visa'): ?>
                                                                <img src="../../icon/visa.png" alt="Visa" class="card-brand-logo">
                                                            <?php elseif ($cardType === 'mastercard'): ?>
                                                                <img src="../../icon/mastercard.png" alt="MasterCard" class="card-brand-logo">
                                                            <?php else: ?>
                                                                <img src="../../icon/visa.png" alt="Card" class="card-brand-logo">
                                                            <?php endif; ?>
                                                        </div>

                                                        <!-- Company Logo positioned under card brand -->
                                                        <?php if (!empty($site_logo)): ?>
                                                        <div class="company-logo-under">
                                                            <img src="../../<?php echo htmlspecialchars($site_logo); ?>" alt="<?php echo htmlspecialchars($site_name); ?>" class="company-logo-main">
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <!-- Chip moved to right side -->
                                                    <div class="card-chip-right">
                                                        <img src="../../icon/chip.png" alt="Chip" class="chip-image">
                                                    </div>

                                                    <div class="card-status">
                                                        <span class="status-badge">
                                                            <?php echo ucfirst($card['status']); ?>
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Card Number - Centered -->
                                                <div class="card-number-centered">
                                                    <?php echo substr($card['card_number'], 0, 4) . ' ' . substr($card['card_number'], 4, 4) . ' ' . substr($card['card_number'], 8, 4) . ' ' . substr($card['card_number'], 12, 4); ?>
                                                </div>

                                                <!-- Card Footer -->
                                                <div class="card-footer-content">
                                                    <div class="card-holder">
                                                        <div class="card-label">CARD HOLDER</div>
                                                        <div class="card-value">
                                                            <?php echo htmlspecialchars($card['card_holder_name']); ?>
                                                        </div>
                                                    </div>
                                                    <div class="card-expiry">
                                                        <div class="card-label">EXPIRES</div>
                                                        <div class="card-value">
                                                            <?php
                                                            if (!empty($card['expiry_date'])) {
                                                                echo date('m/y', strtotime($card['expiry_date']));
                                                            } else {
                                                                echo '--/--';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                </div>



                                                <!-- Click to flip indicator -->
                                                <div class="flip-indicator">
                                                    Click to flip
                                                </div>
                                            </div>

                                            <!-- Back of Card -->
                                            <div class="card-back">
                                                <!-- Magnetic Strip -->
                                                <div class="magnetic-strip"></div>

                                                <!-- Signature Strip and CVV -->
                                                <div class="card-back-content">
                                                    <div class="signature-strip">
                                                        <div class="cvv-box">
                                                            <?php echo $card['cvv'] ?? '***'; ?>
                                                        </div>
                                                    </div>

                                                    <!-- Card Details -->
                                                    <div class="card-back-details">
                                                        <div><strong>Card Number:</strong> <?php echo $card['card_number']; ?></div>
                                                        <div><strong>Valid From:</strong> <?php echo date('m/y', strtotime($card['created_at'])); ?></div>
                                                        <div><strong>Valid Thru:</strong> <?php echo date('m/y', strtotime($card['expiry_date'])); ?></div>
                                                        <div><strong>CVV:</strong> <?php echo $card['cvv'] ?? '***'; ?></div>
                                                    </div>

                                                    <!-- Bank Info -->
                                                    <div class="bank-info">
                                                        <div>PremierBank Pro</div>
                                                        <div>Customer Service: 1-800-PREMIER</div>
                                                    </div>
                                                </div>

                                                <!-- Click to flip indicator -->
                                                <div class="flip-indicator">
                                                    Click to flip
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="text-center text-muted py-5">
                            <div style="border: 2px dashed #dee2e6; border-radius: 12px; padding: 40px; background: #f8f9fa;">
                                <i class="fas fa-credit-card mb-3" style="font-size: 3rem; color: #6c757d;"></i>
                                <h5 class="text-muted">No Virtual Cards</h5>
                                <p class="text-muted mb-0">You don't have any virtual cards yet. Contact support to request a virtual card.</p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- KYC ID Card Display Section -->
                <div class="card mt-3">
                    <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                        <h3 class="card-title text-white mb-0">
                            <i class="fas fa-id-card me-2"></i>
                            KYC Verification Documents
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($kyc_documents)): ?>
                        <div class="kyc-documents-grid">
                            <?php foreach ($kyc_documents as $doc): ?>
                            <div class="kyc-document-item">
                                <div class="document-preview">
                                    <?php
                                    $file_extension = strtolower(pathinfo($doc['file_path'], PATHINFO_EXTENSION));
                                    $is_image = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
                                    ?>

                                    <?php if ($is_image): ?>
                                    <?php
                                    // Simplified path resolution - try multiple possible paths
                                    $original_path = $doc['file_path'];
                                    $possible_paths = [
                                        $original_path,
                                        '../../' . $original_path,
                                        '../../uploads/' . basename($original_path),
                                        '../../uploads/documents/' . basename($original_path),
                                        '../../assets/uploads/' . basename($original_path)
                                    ];

                                    // Find the first working path
                                    $working_path = '';
                                    foreach ($possible_paths as $test_path) {
                                        // Convert to server path for file_exists check
                                        $server_path = str_replace('../../', '', $test_path);
                                        if (file_exists($server_path)) {
                                            $working_path = $test_path;
                                            break;
                                        }
                                    }

                                    // If no working path found, use the original with ../../
                                    if (empty($working_path)) {
                                        $working_path = '../../' . $original_path;
                                    }

                                    // URL encode the filename part to handle spaces and special characters
                                    $path_parts = pathinfo($working_path);
                                    $encoded_filename = rawurlencode($path_parts['basename']);
                                    $final_image_path = $path_parts['dirname'] . '/' . $encoded_filename;
                                    ?>
                                    <img src="<?php echo $final_image_path; ?>"
                                         alt="<?php echo htmlspecialchars($doc['document_name']); ?>"
                                         class="document-image"
                                         onclick="showDocumentModal('<?php echo htmlspecialchars($working_path); ?>', '<?php echo htmlspecialchars($doc['document_name']); ?>')"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div class="document-file-icon" style="display: none;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                        <div class="mt-2 small text-muted">Image not found</div>
                                        <div class="mt-1 small text-muted">Path: <?php echo htmlspecialchars($final_image_path); ?></div>
                                    </div>
                                    <?php else: ?>
                                    <div class="document-file-icon">
                                        <i class="fas fa-file-pdf fa-3x text-danger"></i>
                                        <div class="mt-2 small text-muted">PDF Document</div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="document-overlay">
                                        <span class="badge bg-<?php echo $doc['verification_status'] === 'approved' ? 'success' : ($doc['verification_status'] === 'rejected' ? 'danger' : 'warning'); ?>">
                                            <?php echo ucfirst($doc['verification_status']); ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="document-info">
                                    <div class="document-type">
                                        <?php
                                        $type_labels = [
                                            'id_card' => 'ID Card',
                                            'passport' => 'Passport',
                                            'drivers_license' => 'Driver\'s License',
                                            'kyc_selfie' => 'KYC Selfie'
                                        ];
                                        echo $type_labels[$doc['document_type']] ?? ucfirst(str_replace('_', ' ', $doc['document_type']));
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <!-- Show sample/placeholder when no documents exist -->
                        <div class="kyc-documents-grid">
                            <div class="kyc-document-item">
                                <div class="document-preview">
                                    <div class="document-file-icon">
                                        <i class="fas fa-id-card fa-3x text-muted"></i>
                                        <div class="mt-2 small text-muted">ID Card</div>
                                    </div>
                                    <div class="document-overlay">
                                        <span class="badge bg-warning">Pending</span>
                                    </div>
                                </div>
                                <div class="document-info">
                                    <div class="document-type">Upload Required</div>
                                    <div class="document-date text-muted small">
                                        No documents uploaded yet
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <p class="text-muted mb-0 small">
                                <i class="fas fa-info-circle me-1"></i>
                                Upload your ID documents for account verification.
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Crypto Accounts & Card Details -->
            <div class="col-lg-5">
                <!-- Cryptocurrency Accounts -->
                <div class="card mb-3">
                    <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                        <h3 class="card-title text-white mb-0">
                            <i class="fab fa-bitcoin me-2"></i>
                            My Cryptocurrency Accounts
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($crypto_accounts)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($crypto_accounts as $crypto): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <div class="fw-bold">
                                            <?php
                                            // Handle both table structures
                                            $crypto_type = $crypto['cryptocurrency'] ?? $crypto['crypto_type'] ?? 'Unknown';
                                            echo strtoupper($crypto_type);
                                            ?>
                                        </div>
                                        <small class="text-muted font-monospace crypto-wallet-address"
                                               title="Click to copy full address"
                                               data-full-address="<?php echo htmlspecialchars($crypto['wallet_address'] ?? 'No address'); ?>"
                                               onclick="toggleWalletAddress(this)">
                                            <?php
                                            $wallet_address = $crypto['wallet_address'] ?? 'No address';
                                            if (strlen($wallet_address) > 20) {
                                                echo htmlspecialchars(substr($wallet_address, 0, 8) . '...' . substr($wallet_address, -8));
                                            } else {
                                                echo htmlspecialchars($wallet_address);
                                            }
                                            ?>
                                        </small>
                                        <?php if (!empty($crypto['wallet_name'])): ?>
                                        <div class="text-muted small"><?php echo htmlspecialchars($crypto['wallet_name']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold crypto-balance" title="<?php echo number_format($balance, 8) . ' ' . strtoupper($crypto_type); ?>">
                                            <?php
                                            $balance = $crypto['wallet_balance'] ?? $crypto['balance'] ?? 0;
                                            // Show fewer decimals for better display, full precision on hover
                                            if ($balance >= 1) {
                                                echo number_format($balance, 4);
                                            } else {
                                                echo number_format($balance, 6);
                                            }
                                            ?>
                                            <?php echo strtoupper($crypto_type); ?>
                                        </div>
                                        <?php if (!empty($crypto['usd_equivalent'])): ?>
                                        <small class="text-muted">≈ $<?php echo number_format($crypto['usd_equivalent'], 2); ?></small>
                                        <?php endif; ?>
                                        <div>
                                            <span class="badge bg-<?php echo $crypto['status'] === 'active' ? 'success' : 'danger'; ?>-lt">
                                                <?php echo ucfirst($crypto['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <div style="border: 2px dashed #dee2e6; border-radius: 12px; padding: 30px; background: #f8f9fa;">
                                <i class="fab fa-bitcoin mb-3" style="font-size: 2rem; color: #6c757d;"></i>
                                <h6 class="text-muted">No Cryptocurrency Accounts</h6>
                                <p class="text-muted mb-0 small">Contact support to set up crypto accounts.</p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Card Details Section -->
                <?php if (!empty($virtual_cards)): ?>
                <div class="card">
                    <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                        <h3 class="card-title text-white mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            Card Details
                        </h3>
                    </div>
                    <div class="card-body">
                        <div id="card-details-content">
                            <?php
                            // Show details for the first card by default
                            $card = $virtual_cards[0];
                            ?>
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="detail-item">
                                        <label class="detail-label">Card Number</label>
                                        <div class="detail-value font-monospace">
                                            <?php
                                            $card_number = $card['card_number'];
                                            // Hide middle digits for security
                                            $masked_number = substr($card_number, 0, 4) . ' **** **** ' . substr($card_number, -4);
                                            echo $masked_number;
                                            ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="detail-item">
                                        <label class="detail-label">Card Type</label>
                                        <div class="detail-value">
                                            <?php echo strtoupper($card['card_type'] ?? 'VISA'); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="detail-item">
                                        <label class="detail-label">CVV</label>
                                        <div class="detail-value">
                                            <?php echo $card['cvv'] ?? '***'; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="detail-item">
                                        <label class="detail-label">Expiry Date</label>
                                        <div class="detail-value">
                                            <?php
                                            if (!empty($card['expiry_date'])) {
                                                echo date('m/Y', strtotime($card['expiry_date']));
                                            } else {
                                                echo '--/--';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="detail-item">
                                        <label class="detail-label">Status</label>
                                        <div class="detail-value">
                                            <span class="badge bg-<?php echo $card['status'] === 'active' ? 'success' : 'danger'; ?>">
                                                <?php echo ucfirst($card['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="detail-item">
                                        <label class="detail-label">Card Limit</label>
                                        <div class="detail-value text-success">
                                            $<?php echo number_format($card['card_limit'] ?? 5000, 2); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <div class="detail-item">
                                        <label class="detail-label">Available Balance</label>
                                        <div class="detail-value text-primary">
                                            $<?php echo number_format($card['balance'] ?? 0, 2); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="detail-item">
                                        <label class="detail-label">Card Holder</label>
                                        <div class="detail-value">
                                            <?php echo htmlspecialchars($card['card_holder_name']); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Banking Services Section -->
        <div class="row g-4 mb-4">
            <div class="col-md-4">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-globe-americas"></i>
                    </div>
                    <div class="service-content">
                        <h5>Wire Transfer</h5>
                        <p class="text-muted">Send money internationally with secure wire transfers to banks worldwide with competitive exchange rates.</p>
                        <a href="../transfers/wire/" class="btn btn-primary btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>
                            Send Wire
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="service-content">
                        <h5>Local Transfer</h5>
                        <p class="text-muted">Transfer funds to domestic banks quickly and securely with instant processing and low fees.</p>
                        <a href="../transfers/local/" class="btn btn-primary btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>
                            Transfer Now
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="service-content">
                        <h5>Bill Payment</h5>
                        <p class="text-muted">Pay your bills online with ease. Schedule payments, set up auto-pay, and manage all your bills in one place.</p>
                        <a href="../bills/" class="btn btn-primary btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>
                            Pay Bills
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- KYC & Account Information Section - Controlled by Super Admin Settings -->
        <?php
        // Check if sections should be displayed (default: enabled)
        $show_kyc_section = getSuperAdminSetting('show_kyc_section', '1') === '1';
        $show_account_info_section = getSuperAdminSetting('show_account_info_section', '1') === '1';

        if ($show_kyc_section || $show_account_info_section):
        ?>
        <div class="row row-cards mt-4 mb-4">
            <?php if ($show_kyc_section): ?>
            <!-- KYC Verification Status -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                        <h3 class="card-title text-white mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            KYC Status
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Verification Status</label>
                                    <div class="detail-value">
                                        <span class="badge bg-<?php echo $user['kyc_status'] === 'verified' ? 'success' : ($user['kyc_status'] === 'rejected' ? 'danger' : 'warning'); ?> fs-6">
                                            <?php echo ucfirst($user['kyc_status'] ?? 'pending'); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <?php if ($kyc_application): ?>
                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Application Level</label>
                                    <div class="detail-value">
                                        <?php echo ucfirst($kyc_application['approval_level'] ?? 'basic'); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Application Status</label>
                                    <div class="detail-value">
                                        <?php
                                        $status_labels = [
                                            'pending' => 'Submitted',
                                            'under_review' => 'Under Review',
                                            'approved' => 'Approved',
                                            'verified' => 'Verified',
                                            'rejected' => 'Rejected'
                                        ];
                                        $current_status = $kyc_application['application_status'] ?? 'pending';
                                        echo $status_labels[$current_status] ?? ucfirst(str_replace('_', ' ', $current_status));
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <?php if ($kyc_application['reviewed_at']): ?>
                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Review Status</label>
                                    <div class="detail-value">
                                        <span class="badge bg-<?php echo $kyc_application['application_status'] === 'approved' ? 'success' : ($kyc_application['application_status'] === 'rejected' ? 'danger' : 'warning'); ?>">
                                            Reviewed
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            <?php endif; ?>

                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Documents Uploaded</label>
                                    <div class="detail-value">
                                        <span class="badge bg-info"><?php echo count($kyc_documents); ?> documents</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($show_account_info_section): ?>
            <!-- Account Information -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header border-0" style="background: var(--card-header-gradient, linear-gradient(135deg, var(--primary-color, #206bc4) 0%, var(--primary-dark, #1a5490) 100%));">
                        <h3 class="card-title text-white mb-0">
                            <i class="fas fa-university me-2"></i>
                            Account Info
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Account Type</label>
                                    <div class="detail-value">
                                        <?php echo ucfirst($user['account_type'] ?? 'savings'); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Currency</label>
                                    <div class="detail-value">
                                        <?php echo strtoupper($user['currency'] ?? 'USD'); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Account Number</label>
                                    <div class="detail-value font-monospace">
                                        <?php echo htmlspecialchars($user['account_number']); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Status</label>
                                    <div class="detail-value">
                                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?>">
                                            <?php echo ucfirst($user['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <?php if (!empty($user['occupation'])): ?>
                            <div class="col-12">
                                <div class="detail-item">
                                    <label class="detail-label">Occupation</label>
                                    <div class="detail-value">
                                        <?php echo htmlspecialchars($user['occupation']); ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Recent Transactions Section - Controlled by Super Admin Setting -->
        <?php
        // Check if Recent Transactions section should be displayed (default: enabled)
        $show_recent_transactions_section = getSuperAdminSetting('show_recent_transactions_section', '1') === '1';
        if ($show_recent_transactions_section):
        ?>
        <div class="transactions-section">
            <div class="transactions-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">Recent Transactions</h3>
                <div class="transactions-actions">
                    <a href="../transactions/" class="btn btn-primary btn-sm me-2">
                        <i class="fas fa-list me-1"></i>View All Transactions
                    </a>
                    <a href="../accounts/statements.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-file-alt me-1"></i>View Statements
                    </a>
                </div>
            </div>

            <?php if (!empty($recent_transactions)): ?>
            <div class="table-responsive">
                <table class="transactions-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Type</th>
                            <th>Amount</th>
                            <th>Balance</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_transactions as $index => $transaction): ?>
                        <tr>
                            <td>
                                <span class="transaction-number"><?php echo str_pad($index + 1, 3, '0', STR_PAD_LEFT); ?></span>
                            </td>
                            <td>
                                <div><?php echo date('M d, Y', strtotime($transaction['created_at'])); ?></div>
                                <small class="text-muted"><?php echo date('H:i', strtotime($transaction['created_at'])); ?></small>
                            </td>
                            <td>
                                <div class="transaction-description">
                                    <?php echo htmlspecialchars($transaction['description']); ?>
                                </div>
                                <?php if (!empty($transaction['reference_number'])): ?>
                                <small class="text-muted">Ref: <?php echo htmlspecialchars($transaction['reference_number']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $type_class = '';
                                $type_label = '';

                                // Simplified transaction type - only Credit or Debit
                                if ($transaction['transaction_type'] == 'credit') {
                                    $type_class = $transaction['initiator_type'] == 'Admin' ? 'admin-credit' : 'user-credit';
                                    $type_label = 'Credit';
                                } else {
                                    $type_class = $transaction['initiator_type'] == 'Admin' ? 'admin-debit' : 'user-debit';
                                    $type_label = 'Debit';
                                }
                                ?>
                                <span class="transaction-type <?php echo $type_class; ?>">
                                    <?php echo $type_label; ?>
                                </span>
                            </td>
                            <td>
                                <span class="transaction-amount <?php echo $transaction['transaction_type']; ?>">
                                    <?php echo $transaction['transaction_type'] == 'credit' ? '+' : '-'; ?>
                                    <?php echo formatCurrency($transaction['amount'], $user_currency); ?>
                                </span>
                            </td>
                            <td>
                                <span class="transaction-balance">
                                    <?php echo formatCurrency($transaction['balance_after'] ?? $user['balance'], $user_currency); ?>
                                </span>
                            </td>
                            <td>
                                <span class="transaction-status <?php echo strtolower($transaction['status']); ?>">
                                    <?php echo ucfirst($transaction['status']); ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No transactions yet</h5>
                <p class="text-muted">Your transaction history will appear here</p>
                <div class="mt-3">
                    <a href="../transactions/" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Start Your First Transaction
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>



    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<!-- Transaction Details Modal -->
<div id="transactionModal" class="receipt-modal">
    <div class="modal-overlay" onclick="closeTransactionModal()"></div>
    <div class="receipt-container">
        <!-- Receipt Header -->
        <div class="receipt-header">
            <div class="bank-logo">
                <i class="fas fa-university"></i>
                <span class="bank-name">PremierBank Pro</span>
            </div>
            <div class="receipt-title">TRANSACTION RECEIPT</div>
            <button class="receipt-close" onclick="closeTransactionModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Receipt Body -->
        <div class="receipt-body">
            <div id="transactionModalContent" class="receipt-content">
                <!-- Content will be inserted here -->
            </div>
        </div>

        <!-- Receipt Footer -->
        <div class="receipt-footer">
            <div class="receipt-actions">
                <button class="btn btn-secondary" onclick="closeTransactionModal()">
                    <i class="fas fa-times me-1"></i>
                    Close
                </button>
                <button class="btn btn-primary" onclick="printReceipt()">
                    <i class="fas fa-print me-1"></i>
                    Print Receipt
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Include Dashboard JavaScript -->
<script src="dashboard.js"></script>

<!-- Card Flip and Tab Functionality -->
<script>
// Tab functionality for virtual cards
function showCard(cardIndex) {
    // Hide all card contents
    const allContents = document.querySelectorAll('.card-content');
    allContents.forEach(content => {
        content.classList.remove('active');
    });

    // Remove active class from all tabs
    const allTabs = document.querySelectorAll('.card-tab');
    allTabs.forEach(tab => {
        tab.classList.remove('active');
    });

    // Show selected card content
    const selectedContent = document.getElementById('card-content-' + cardIndex);
    if (selectedContent) {
        selectedContent.classList.add('active');
    }

    // Add active class to selected tab
    const selectedTab = document.querySelector(`[data-card="${cardIndex}"]`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Update card details in the right panel
    updateCardDetails(cardIndex);
}

// Card data for JavaScript
const cardData = <?php echo json_encode($virtual_cards); ?>;

// Update card details function
function updateCardDetails(cardIndex) {
    if (!cardData || !cardData[cardIndex]) {
        console.log('No card data available for index:', cardIndex);
        return;
    }

    const card = cardData[cardIndex];
    const detailsContainer = document.getElementById('card-details-content');

    if (!detailsContainer) {
        console.log('Card details container not found');
        return;
    }

    // Update card details content
    detailsContainer.innerHTML = `
        <div class="row g-2">
            <div class="col-12">
                <div class="detail-item">
                    <label class="detail-label">Card Number</label>
                    <div class="detail-value font-monospace">
                        ${card.card_number.substring(0, 4)} **** **** ${card.card_number.substring(card.card_number.length - 4)}
                    </div>
                </div>
            </div>

            <div class="col-6">
                <div class="detail-item">
                    <label class="detail-label">Card Type</label>
                    <div class="detail-value">
                        ${(card.card_type || 'VISA').toUpperCase()}
                    </div>
                </div>
            </div>

            <div class="col-6">
                <div class="detail-item">
                    <label class="detail-label">CVV</label>
                    <div class="detail-value">
                        ${card.cvv || '***'}
                    </div>
                </div>
            </div>

            <div class="col-6">
                <div class="detail-item">
                    <label class="detail-label">Expiry Date</label>
                    <div class="detail-value">
                        ${card.expiry_date ? new Date(card.expiry_date).toLocaleDateString('en-US', {month: '2-digit', year: 'numeric'}) : '--/--'}
                    </div>
                </div>
            </div>

            <div class="col-6">
                <div class="detail-item">
                    <label class="detail-label">Status</label>
                    <div class="detail-value">
                        <span class="badge bg-${card.status === 'active' ? 'success' : 'danger'}">
                            ${card.status ? card.status.charAt(0).toUpperCase() + card.status.slice(1) : 'Unknown'}
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-6">
                <div class="detail-item">
                    <label class="detail-label">Card Limit</label>
                    <div class="detail-value text-success">
                        $${parseFloat(card.card_limit || card.spending_limit || 5000).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
            </div>

            <div class="col-6">
                <div class="detail-item">
                    <label class="detail-label">Available Balance</label>
                    <div class="detail-value text-primary">
                        $${parseFloat(card.balance || card.card_balance || card.current_balance || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
            </div>

            <div class="col-12">
                <div class="detail-item">
                    <label class="detail-label">Card Holder</label>
                    <div class="detail-value">
                        ${card.card_holder_name || 'Unknown'}
                    </div>
                </div>
            </div>
        </div>
    `;

    console.log('Card details updated for card:', card.card_number);
}

// Document modal functionality
function showDocumentModal(imagePath, documentName) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('documentModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'documentModal';
        modal.className = 'document-modal';
        modal.innerHTML = `
            <div class="document-modal-content">
                <span class="document-modal-close" onclick="closeDocumentModal()">&times;</span>
                <img id="modalDocumentImage" src="" alt="">
            </div>
        `;
        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeDocumentModal();
            }
        });
    }

    // Set image source and show modal
    document.getElementById('modalDocumentImage').src = imagePath;
    document.getElementById('modalDocumentImage').alt = documentName;
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeDocumentModal() {
    const modal = document.getElementById('documentModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDocumentModal();
    }
});

// Card flip functionality
function flipCard(cardIndex) {
    const card = document.getElementById('card-flip-' + cardIndex);
    if (card) {
        card.classList.toggle('flipped');
    }
}

// Add hover effects for cards
document.addEventListener('DOMContentLoaded', function() {
    // New card containers
    const newCards = document.querySelectorAll('.card-flip-container-new');
    newCards.forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'transform 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Legacy card containers
    const cards = document.querySelectorAll('.card-flip-container');
    cards.forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'transform 0.3s ease';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>

<?php require_once '../shared/footer.php'; ?>
