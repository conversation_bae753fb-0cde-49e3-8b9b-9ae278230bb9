<?php
/**
 * Profile & Settings Page
 * Display user information in read-only format
 */

// Enable error display for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set page variables
$page_title = 'Profile & Settings';
$current_page = 'profile';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get complete user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Get KYC status (if table exists)
$kyc_status = null;
try {
    $kyc_query = "SELECT * FROM kyc_documents WHERE user_id = ? ORDER BY created_at DESC LIMIT 1";
    $kyc_result = $db->query($kyc_query, [$user_id]);
    $kyc_status = $kyc_result->fetch_assoc();
} catch (Exception $e) {
    // KYC table doesn't exist, continue without KYC data
    $kyc_status = null;
}

// Set page title
$page_title = 'Profile & Settings';

// Include header
require_once __DIR__ . '/../shared/header.php';
?>

<!-- Include Profile CSS -->
<link rel="stylesheet" href="profile.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once __DIR__ . '/../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once __DIR__ . '/../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Profile Hero Section -->
        <div class="profile-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Profile & Settings</div>
                    <div class="hero-subtitle">View your account information and settings</div>
                    <div class="hero-stats">
                        Account: <?php echo htmlspecialchars($user['account_number']); ?> • <?php echo ucfirst($user['account_type'] ?? 'Standard'); ?> Account
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="far fa-file-alt me-2"></i>Print Profile
                    </button>
                </div>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="profile-container">
        <!-- User Avatar and Basic Info -->
        <div class="profile-header">
            <div class="profile-avatar">
                <div class="avatar-circle">
                    <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                </div>
                <div class="avatar-info">
                    <h2><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h2>
                    <p class="user-email"><?php echo htmlspecialchars($user['email']); ?></p>
                    <div class="account-status">
                        <span class="status-badge <?php echo $user['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>">
                            <i class="fas fa-circle me-1"></i><?php echo ucfirst($user['status']); ?>
                        </span>
                        <a href="../security/" class="btn btn-outline-primary btn-sm ms-2">
                            <i class="far fa-shield-alt me-1"></i>Security Settings
                        </a>
                    </div>
                </div>
            </div>
            <div class="profile-stats">
                <div class="stat-item">
                    <div class="stat-value"><?php echo formatCurrency($user['balance'], $user['currency']); ?></div>
                    <div class="stat-label">Account Balance</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo date('M j, Y', strtotime($user['created_at'])); ?></div>
                    <div class="stat-label">Member Since</div>
                </div>
            </div>
        </div>

        <!-- Profile Information Grid (2 Columns) -->
        <div class="profile-grid-two-column">
            <!-- Personal & Contact Information -->
            <div class="profile-card">
                <div class="card-header">
                    <h3><i class="fas fa-user me-2"></i>Personal & Contact Information</h3>
                </div>
                <div class="card-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>First Name</label>
                            <div class="info-value"><?php echo htmlspecialchars($user['first_name']); ?></div>
                        </div>
                        <div class="info-item">
                            <label>Last Name</label>
                            <div class="info-value"><?php echo htmlspecialchars($user['last_name']); ?></div>
                        </div>
                        <div class="info-item">
                            <label>Email Address</label>
                            <div class="info-value"><?php echo htmlspecialchars($user['email']); ?></div>
                        </div>
                        <div class="info-item">
                            <label>Phone Number</label>
                            <div class="info-value"><?php echo htmlspecialchars($user['phone'] ?: 'Not provided'); ?></div>
                        </div>
                        <div class="info-item">
                            <label>Date of Birth</label>
                            <div class="info-value"><?php echo $user['date_of_birth'] ? date('F j, Y', strtotime($user['date_of_birth'])) : 'Not provided'; ?></div>
                        </div>
                        <div class="info-item">
                            <label>Gender</label>
                            <div class="info-value"><?php echo $user['gender'] ? ucfirst($user['gender']) : 'Not specified'; ?></div>
                        </div>
                        <div class="info-item full-width">
                            <label>Address</label>
                            <div class="info-value"><?php echo htmlspecialchars($user['address'] ?: 'Not provided'); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account & Verification Information -->
            <div class="profile-card">
                <div class="card-header">
                    <h3><i class="fas fa-university me-2"></i>Account & Verification</h3>
                </div>
                <div class="card-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Account Number</label>
                            <div class="info-value account-number">****<?php echo substr($user['account_number'], -4); ?></div>
                        </div>
                        <div class="info-item">
                            <label>Account Type</label>
                            <div class="info-value"><?php echo ucfirst($user['account_type'] ?: 'Standard'); ?></div>
                        </div>
                        <div class="info-item">
                            <label>Currency</label>
                            <div class="info-value"><?php echo strtoupper($user['currency']); ?></div>
                        </div>
                        <div class="info-item">
                            <label>Member Since</label>
                            <div class="info-value"><?php echo date('F j, Y', strtotime($user['created_at'])); ?></div>
                        </div>
                        <div class="info-item">
                            <label>KYC Status</label>
                            <div class="info-value">
                                <?php if ($kyc_status): ?>
                                    <span class="status-badge status-<?php echo $kyc_status['status']; ?>">
                                        <i class="fas fa-<?php echo $kyc_status['status'] === 'approved' ? 'check-circle' : ($kyc_status['status'] === 'pending' ? 'clock' : 'times-circle'); ?> me-1"></i>
                                        <?php echo ucfirst($kyc_status['status']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge status-pending">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Not Submitted
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="info-item">
                            <label>Account Balance</label>
                            <div class="info-value account-balance"><?php echo formatCurrency($user['balance'], $user['currency']); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Change Password Section -->
        <div class="profile-card mb-4">
            <div class="card-header">
                <h3><i class="fas fa-key me-2"></i>Change Password</h3>
            </div>
            <div class="card-body">
                <form id="changePasswordForm" class="change-password-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="currentPassword">Current Password</label>
                                <div class="password-input-group">
                                    <input type="password" id="currentPassword" name="current_password" class="form-control" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('currentPassword')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="newPassword">New Password</label>
                                <div class="password-input-group">
                                    <input type="password" id="newPassword" name="new_password" class="form-control" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-requirements">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Password must be at least 8 characters with uppercase, lowercase, number, and special character.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="confirmPassword">Confirm New Password</label>
                                <div class="password-input-group">
                                    <input type="password" id="confirmPassword" name="confirm_password" class="form-control" required>
                                    <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <div class="form-group w-100">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-shield-alt me-2"></i>Update Password
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="security-notice">
            <div class="notice-content">
                <i class="fas fa-info-circle notice-icon"></i>
                <div class="notice-text">
                    <h4>Profile Information</h4>
                    <p>Your profile information is displayed in read-only format for security purposes. To update your information, please contact our customer support team.</p>
                </div>
            </div>
        </div>
    </div>

        </div> <!-- End Content Container -->

    </div> <!-- End Main Content -->

    <!-- User Footer Component -->
    <?php require_once __DIR__ . '/../shared/user_footer.php'; ?>
</div>

<script src="profile.js"></script>
