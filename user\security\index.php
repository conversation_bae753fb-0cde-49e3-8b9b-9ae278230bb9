<?php
/**
 * Security Settings Page
 * Dedicated page for user security management
 */

// Set page variables
$page_title = 'Security Settings';
$current_page = 'security';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';
require_once __DIR__ . '/../../includes/user-2fa-functions.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Get user security settings
$security_settings = getUserSecuritySettings($user_id);
if (!$security_settings) {
    // Create default security settings if none exist
    $create_settings = "INSERT INTO user_security_settings (
        user_id, otp_enabled, google_2fa_enabled, require_2fa, allow_remember_device,
        login_attempts_limit, lockout_duration, otp_expiry_minutes, created_by, updated_by
    ) VALUES (?, 1, 0, 1, 0, 5, 30, 10, ?, ?)";
    $db->query($create_settings, [$user_id, $user_id, $user_id]);
    $security_settings = getUserSecuritySettings($user_id);
}

// Get last login information
$last_login_query = "SELECT last_login_attempt FROM user_security_settings WHERE user_id = ?";
$last_login_result = $db->query($last_login_query, [$user_id]);
$last_login_data = $last_login_result->fetch_assoc();
$last_login = $last_login_data['last_login_attempt'] ?? date('Y-m-d H:i:s');

// Set page title
$page_title = 'Security Settings';

// Include header
require_once __DIR__ . '/../shared/header.php';
?>

<!-- Include Security CSS -->
<link rel="stylesheet" href="security.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once __DIR__ . '/../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once __DIR__ . '/../shared/user_header.php'; ?>

    <div class="main-content">
        <!-- Security Hero Section -->
        <div class="security-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Security Settings</div>
                    <div class="hero-subtitle">Manage your account security and authentication preferences</div>
                    <div class="hero-stats">
                        Account Status: <?php echo ucfirst($user['status']); ?> • Last Login: <?php echo date('M j, Y g:i A', strtotime($last_login)); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="../profile/" class="btn" style="background-color: white; color: #206bc4; border: 1px solid #206bc4;">
                        <i class="fas fa-arrow-left me-2"></i>Back to Profile
                    </a>
                </div>
            </div>
        </div>

        <!-- Security Stats Section -->
        <div class="balance-overview-new mb-4">
            <!-- Account Status Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Account Status</div>
                    <div class="balance-amount" style="color: #10b981;">
                        <?php echo ucfirst($user['status']); ?>
                    </div>
                    <div class="balance-subtitle">Active Account</div>
                </div>
            </div>

            <!-- OTP Status Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, <?php echo $security_settings['otp_enabled'] ? '#3b82f6 0%, #2563eb 100%' : '#ef4444 0%, #dc2626 100%'; ?>);">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">OTP Authentication</div>
                    <div class="balance-amount" style="color: <?php echo $security_settings['otp_enabled'] ? '#3b82f6' : '#ef4444'; ?>;">
                        <?php echo $security_settings['otp_enabled'] ? 'Enabled' : 'Disabled'; ?>
                    </div>
                    <div class="balance-subtitle">SMS/Email OTP</div>
                </div>
            </div>

            <!-- 2FA Status Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, <?php echo $security_settings['google_2fa_enabled'] ? '#10b981 0%, #059669 100%' : '#f59e0b 0%, #d97706 100%'; ?>);">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Two-Factor Auth</div>
                    <div class="balance-amount" style="color: <?php echo $security_settings['google_2fa_enabled'] ? '#10b981' : '#f59e0b'; ?>;">
                        <?php echo $security_settings['google_2fa_enabled'] ? 'Enabled' : 'Available'; ?>
                    </div>
                    <div class="balance-subtitle">Google Authenticator</div>
                </div>
            </div>

            <!-- Last Login Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Last Login</div>
                    <div class="balance-amount" style="color: #8b5cf6;">
                        <?php echo date('M j', strtotime($last_login)); ?>
                    </div>
                    <div class="balance-subtitle"><?php echo date('g:i A', strtotime($last_login)); ?></div>
                </div>
            </div>
        </div>

        <!-- Security Settings Grid -->
        <div class="security-grid">
            <!-- Account Status Management -->
            <div class="security-card">
                <div class="card-header">
                    <h3><i class="far fa-user-check me-2"></i>Account Status</h3>
                </div>
                <div class="card-body">
                    <div class="security-item">
                        <div class="security-icon">
                            <i class="far fa-check-circle text-success"></i>
                        </div>
                        <div class="security-details">
                            <h6>Current Status</h6>
                            <p class="mb-2">
                                <span class="status-badge <?php echo $user['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                            </p>
                            <small class="text-muted">Account created: <?php echo date('F j, Y', strtotime($user['created_at'])); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Password Management -->
            <div class="security-card">
                <div class="card-header">
                    <h3><i class="far fa-key me-2"></i>Password Security</h3>
                </div>
                <div class="card-body">
                    <div class="security-item">
                        <div class="security-icon">
                            <i class="far fa-key text-primary"></i>
                        </div>
                        <div class="security-details">
                            <h6>Password</h6>
                            <p class="mb-2">Last changed: <?php echo date('F j, Y', strtotime($user['updated_at'] ?? $user['created_at'])); ?></p>
                            <button class="btn btn-outline-primary btn-sm" onclick="showPasswordChangeForm()">
                                <i class="far fa-edit me-1"></i>Change Password
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Last Login Information -->
            <div class="security-card">
                <div class="card-header">
                    <h3><i class="far fa-clock me-2"></i>Login Activity</h3>
                </div>
                <div class="card-body">
                    <div class="security-item">
                        <div class="security-icon">
                            <i class="far fa-clock text-info"></i>
                        </div>
                        <div class="security-details">
                            <h6>Last Login</h6>
                            <p class="mb-2"><?php echo date('F j, Y g:i A', strtotime($last_login)); ?></p>
                            <small class="text-muted">Login attempts limit: <?php echo $security_settings['login_attempts_limit']; ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Two-Factor Authentication -->
            <div class="security-card">
                <div class="card-header">
                    <h3><i class="far fa-shield-alt me-2"></i>Two-Factor Authentication</h3>
                </div>
                <div class="card-body">
                    <div class="security-item">
                        <div class="security-icon">
                            <i class="far fa-shield-alt text-warning"></i>
                        </div>
                        <div class="security-details">
                            <h6>Google Authenticator</h6>
                            <p class="mb-2">Status: <?php echo $security_settings['google_2fa_enabled'] ? 'Enabled' : 'Not enabled'; ?></p>
                            <button class="btn btn-outline-warning btn-sm" onclick="toggle2FA()">
                                <i class="far fa-<?php echo $security_settings['google_2fa_enabled'] ? 'times' : 'plus'; ?> me-1"></i>
                                <?php echo $security_settings['google_2fa_enabled'] ? 'Disable' : 'Enable'; ?> 2FA
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- OTP Settings -->
            <div class="security-card">
                <div class="card-header">
                    <h3><i class="far fa-mobile-alt me-2"></i>OTP Settings</h3>
                </div>
                <div class="card-body">
                    <div class="security-item">
                        <div class="security-icon">
                            <i class="far fa-mobile-alt text-primary"></i>
                        </div>
                        <div class="security-details">
                            <h6>SMS/Email OTP</h6>
                            <p class="mb-2">Status: <?php echo $security_settings['otp_enabled'] ? 'Enabled' : 'Disabled'; ?></p>
                            <button class="btn btn-outline-primary btn-sm" onclick="toggleOTP()">
                                <i class="far fa-<?php echo $security_settings['otp_enabled'] ? 'times' : 'plus'; ?> me-1"></i>
                                <?php echo $security_settings['otp_enabled'] ? 'Disable' : 'Enable'; ?> OTP
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="security-card full-width">
                <div class="card-header">
                    <h3><i class="far fa-info-circle me-2"></i>Security Notice</h3>
                </div>
                <div class="card-body">
                    <div class="security-notice">
                        <div class="notice-content">
                            <i class="far fa-info-circle notice-icon"></i>
                            <div class="notice-text">
                                <h4>Account Security</h4>
                                <p>Your account security is important to us. We recommend enabling two-factor authentication and keeping your password secure. Contact support if you notice any suspicious activity.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once __DIR__ . '/../shared/user_footer.php'; ?>
</div>

<script src="security.js"></script>
