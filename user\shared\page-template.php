<?php
/**
 * Universal User Page Template
 * Use this template for all new user pages to ensure consistent layout
 * 
 * IMPORTANT: This template ensures no gaps between sidebar and content
 * DO NOT use Bootstrap grid system (col-md-*, etc.) for main layout
 */

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include required files
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

// Get user data from database
$db = getDB();
$user_id = $_SESSION['user_id'];

// Your page-specific database queries here
// Example:
// $user_query = "SELECT * FROM accounts WHERE id = ?";
// $user_result = $db->query($user_query, [$user_id]);
// $user = $user_result->fetch_assoc();

// Set page title and subtitle
$page_title = 'Your Page Title';
$page_subtitle = 'Optional subtitle'; // Optional

// Include header (this loads universal-layout.css automatically)
require_once '../shared/header.php';
?>

<!-- Page-specific styles (optional) -->
<style>
    /* Dynamic CSS Variables */
    <?php echo getInlineDynamicCSS(); ?>
    
    /* Your page-specific styles here */
    .your-custom-styles {
        /* Add your styles */
    }
</style>

<!-- Sidebar (fixed positioned, 280px wide) -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper (automatically positioned with margin-left: 280px) -->
<div class="main-content-wrapper">
    <!-- User Header Component (spans full width of content area) -->
    <?php require_once '../shared/user_header.php'; ?>

    <!-- Main Content Area -->
    <div class="main-content">
        
        <!-- Your page content goes here -->
        <div class="page-content">
            <h1>Your Page Content</h1>
            <p>Add your page content here. The layout will automatically handle:</p>
            <ul>
                <li>Sidebar positioning (fixed, 280px wide)</li>
                <li>Content area positioning (margin-left: 280px)</li>
                <li>Header spanning full content width</li>
                <li>Footer spanning full content width</li>
                <li>Responsive behavior on mobile</li>
            </ul>
            
            <!-- Example content structure -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Example Card</h5>
                            <p class="card-text">You can use Bootstrap components inside the main-content area.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Another Card</h5>
                            <p class="card-text">Just don't use Bootstrap grid for the main layout structure.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- User Footer Component (spans full width of content area) -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<!-- Include footer scripts -->
<?php require_once '../shared/footer.php'; ?>

<!-- Page-specific JavaScript (optional) -->
<script>
// Your page-specific JavaScript here
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded successfully');
});
</script>
