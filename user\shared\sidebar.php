<?php
// Get current page for active navigation
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Get the base URL for proper linking
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . '://' . $host . '/online_banking';

// Get user data from session or database
$user_first_name = $_SESSION['first_name'] ?? 'User';
$user_last_name = $_SESSION['last_name'] ?? '';
$user_account_number = $_SESSION['account_number'] ?? '';

// If session data is missing, try to get from database
if (empty($user_first_name) || $user_first_name === 'User') {
    try {
        require_once '../../config/config.php';
        $db = getDB();
        $user_result = $db->query("SELECT first_name, last_name, account_number FROM accounts WHERE id = ?", [$_SESSION['user_id']]);
        if ($user_result && $user_data = $user_result->fetch_assoc()) {
            $user_first_name = $user_data['first_name'];
            $user_last_name = $user_data['last_name'];
            $user_account_number = $user_data['account_number'];
        }
    } catch (Exception $e) {
        // Keep defaults
    }
}

// Get primary color from database (super admin settings)
$primary_color = '#206bc4'; // Default fallback
try {
    if (isset($db)) {
        // Get theme color from super_admin_settings table (correct table)
        $color_result = $db->query("SELECT setting_value FROM super_admin_settings WHERE setting_key = 'theme_color' LIMIT 1");
        if ($color_result && $color_data = $color_result->fetch_assoc()) {
            $primary_color = $color_data['setting_value'];
        }
    }
} catch (Exception $e) {
    // Keep default fallback color
}

// Color adjustment function is already available from dynamic-css.php

// Include dynamic CSS for consistent theming
require_once '../../config/dynamic-css.php';

// Check if IRS feature is enabled
$irs_enabled = false;
try {
    if (isset($db)) {
        $irs_result = $db->query("SELECT value FROM admin_settings WHERE setting_key = 'irs_feature_enabled' LIMIT 1");
        if ($irs_result && $irs_data = $irs_result->fetch_assoc()) {
            $irs_enabled = ($irs_data['value'] === '1');
        }
    }
} catch (Exception $e) {
    // Keep default false
}

// Check if crypto transfers feature is enabled
$crypto_enabled = false;
try {
    if (isset($db)) {
        $crypto_result = $db->query("SELECT value FROM admin_settings WHERE setting_key = 'crypto_transfers_enabled' LIMIT 1");
        if ($crypto_result && $crypto_data = $crypto_result->fetch_assoc()) {
            $crypto_enabled = ($crypto_data['value'] === '1');
        }
    }
} catch (Exception $e) {
    // Keep default false
}
?>

<!-- Banking Sidebar CSS -->
<style>
    /* Dynamic CSS Variables */
    <?php echo getInlineDynamicCSS(); ?>
    .banking-sidebar {
        width: 280px;
        height: 100vh;
        background: linear-gradient(180deg, var(--background-white) 0%, var(--sidebar-bg) 100%);
        border-right: 1px solid var(--border-color);
        position: fixed;
        left: 0;
        top: 0;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }

    .sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--border-color);
        background: var(--background-white);
    }

    .bank-logo {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, <?php echo $primary_color; ?>, <?php echo adjustColorBrightness($primary_color, -20); ?>);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 0.75rem;
    }

    .bank-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        min-width: 0;
    }

    .user-info {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: linear-gradient(135deg, <?php echo $primary_color; ?>15, <?php echo $primary_color; ?>08);
        border-radius: 12px;
        border: 1px solid <?php echo $primary_color; ?>20;
    }

    .user-avatar {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, <?php echo $primary_color; ?>, <?php echo adjustColorBrightness($primary_color, -20); ?>);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 1.1rem;
        margin-right: 0.75rem;
    }

    .user-details {
        flex: 1;
    }

    .user-name {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.95rem;
    }

    .account-number {
        font-size: 0.85rem;
        color: var(--text-secondary);
        font-family: monospace;
    }

    .sidebar-nav {
        padding: 1rem 0;
    }

    .nav-section {
        margin-bottom: 1.5rem;
    }

    .nav-section-title {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 0 1.5rem;
        margin-bottom: 0.5rem;
    }

    .nav-list {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .nav-item {
        margin: 0;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
    }

    .nav-link:hover {
        background: var(--primary-light);
        color: var(--primary-color);
        border-left-color: var(--primary-color);
        text-decoration: none;
    }

    .nav-link.active {
        background: var(--primary-color);
        color: white;
        border-left-color: var(--primary-color);
        font-weight: 600;
    }

    .nav-link.logout-link:hover {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
        border-left-color: #dc2626;
    }

    .nav-icon {
        width: 20px;
        height: 20px;
        margin-right: 0.75rem;
        flex-shrink: 0;
    }

    .sidebar-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1rem;
        border-top: 1px solid var(--border-color);
        background: var(--background-white);
    }

    .logout-link {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        color: var(--danger-color);
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .logout-link:hover {
        background: rgba(239, 68, 68, 0.1);
        color: var(--danger-color);
    }

    .logout-icon {
        width: 20px;
        height: 20px;
        margin-right: 0.75rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .banking-sidebar {
            width: 100%;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .banking-sidebar.open {
            transform: translateX(0);
        }
    }
</style>

<!-- Banking Sidebar Navigation -->
<div class="banking-sidebar">
    <!-- Bank Logo & Brand -->
    <div class="sidebar-header">
        <div class="bank-logo">
            <div class="logo-icon">
                <svg width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                </svg>
            </div>
            <div class="bank-name">
                <?php echo htmlspecialchars($site_name ?? 'PremierBank Pro'); ?>
            </div>
        </div>
        <div class="user-info">
            <div class="user-avatar">
                <?php echo strtoupper(substr($user_first_name, 0, 1) . substr($user_last_name, 0, 1)); ?>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo htmlspecialchars($user_first_name . ' ' . $user_last_name); ?></div>
                <div class="account-number">•••• <?php echo substr($user_account_number, -4); ?></div>
            </div>
        </div>
    </div>

    <!-- Main Banking Navigation -->
    <nav class="sidebar-nav">
        <!-- Overview Section -->
        <div class="nav-section">
            <div class="nav-section-title">Overview</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/dashboard/" class="nav-link <?php echo $current_page === 'index' && $current_dir === 'dashboard' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                        </svg>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/profile/" class="nav-link <?php echo $current_dir === 'profile' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                        </svg>
                        <span>Profile</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/security/" class="nav-link <?php echo $current_dir === 'security' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Security Settings</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Banking Services Section -->
        <div class="nav-section">
            <div class="nav-section-title">Banking Services</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/transfers/" class="nav-link <?php echo $current_dir === 'transfers' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                        </svg>
                        <span>Transfer Money</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/accounts/" class="nav-link <?php echo ($current_dir === 'accounts' && $current_page === 'index') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                        </svg>
                        <span>Internal Transfers</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/accounts/history.php" class="nav-link <?php echo ($current_dir === 'accounts' && $current_page === 'history') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                        </svg>
                        <span>Transfer History</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/transactions/" class="nav-link <?php echo $current_dir === 'transactions' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <span>Transaction History</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/accounts/statements.php" class="nav-link <?php echo $current_page === 'statements' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                        </svg>
                        <span>Transaction Statements</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/beneficiaries/" class="nav-link <?php echo $current_dir === 'beneficiaries' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                        <span>Beneficiaries</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/wire-beneficiaries/" class="nav-link <?php echo $current_dir === 'wire-beneficiaries' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clip-rule="evenodd"/>
                        </svg>
                        <span>Wire Beneficiaries</span>
                    </a>
                </li>
                <?php if ($irs_enabled): ?>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/application/apply.php" class="nav-link <?php echo ($current_dir === 'application' && $current_page === 'apply') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 2v10h8V6H6zm2 2h4a1 1 0 110 2H8a1 1 0 110-2zm0 3h4a1 1 0 110 2H8a1 1 0 110-2z" clip-rule="evenodd"/>
                        </svg>
                        <span>IRS Tax Assistance</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/application/status.php" class="nav-link <?php echo ($current_dir === 'application' && $current_page === 'status') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>Application Status</span>
                    </a>
                </li>
                <?php endif; ?>
                <?php if ($crypto_enabled): ?>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/crypto/transfer.php" class="nav-link <?php echo ($current_dir === 'crypto' && $current_page === 'transfer') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm-6-8a6 6 0 1112 0 6 6 0 01-12 0zm3.5-2a.5.5 0 01.5.5v.5h.5a.5.5 0 010 1H8v.5c0 .501.25.916.567 *************.352.234.567.267v.5a.5.5 0 01-1 0v-.5h-.5a.5.5 0 010-1H8v-.5c0-.501-.25-.916-.567-1.166A2.305 2.305 0 017 9.5v-.5a.5.5 0 01.5-.5zm4.5 0a.5.5 0 01.5.5v.5h.5a.5.5 0 010 1H12v.5c0 .501.25.916.567 *************.352.234.567.267v.5a.5.5 0 01-1 0v-.5h-.5a.5.5 0 010-1H12v-.5c0-.501-.25-.916-.567-1.166A2.305 2.305 0 0111 9.5v-.5a.5.5 0 01.5-.5z" clip-rule="evenodd"/>
                        </svg>
                        <span>Crypto Transfers</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/crypto/deposit.php" class="nav-link <?php echo ($current_dir === 'crypto' && $current_page === 'deposit') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                        </svg>
                        <span>Crypto Deposits</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/crypto/request-wallet.php" class="nav-link <?php echo ($current_dir === 'crypto' && $current_page === 'request-wallet') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clip-rule="evenodd"/>
                            <path d="M9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"/>
                        </svg>
                        <span>Request Wallet</span>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </div>

        <!-- Cards & Digital Section -->
        <div class="nav-section">
            <div class="nav-section-title">Cards & Digital</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/cards/" class="nav-link <?php echo ($current_dir === 'cards' && $current_page !== 'topup') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v2H4V6zm0 4h12v4H4v-4z" clip-rule="evenodd"/>
                        </svg>
                        <span>My Cards</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/cards/topup.php" class="nav-link <?php echo ($current_dir === 'cards' && $current_page === 'topup') ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"/>
                        </svg>
                        <span>Card Top-up</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/cheques/" class="nav-link <?php echo $current_dir === 'cheques' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 2v10h8V6H6zm2 2h4a1 1 0 110 2H8a1 1 0 110-2zm0 3h4a1 1 0 110 2H8a1 1 0 110-2z" clip-rule="evenodd"/>
                        </svg>
                        <span>Cheque Preview</span>
                    </a>
                </li>
            </ul>
        </div>



        <!-- Support & Settings Section -->
        <div class="nav-section">
            <div class="nav-section-title">Support</div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/user/support/" class="nav-link <?php echo $current_dir === 'support' ? 'active' : ''; ?>">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                        </svg>
                        <span>Help & Support</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo $base_url; ?>/auth/logout.php" class="nav-link logout-link">
                        <svg class="nav-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 01-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
                        </svg>
                        <span>Sign Out</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
</div>
