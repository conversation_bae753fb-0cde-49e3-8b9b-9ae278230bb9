<?php
/**
 * User Dashboard Header Component
 * Minimal header with page title, user info, and logout (like admin)
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection if not already included
if (!isset($db)) {
    require_once __DIR__ . '/../../config/database.php';
    $db = getDB();
}

// Get user information
$user_id = $_SESSION['user_id'];
try {
    $user_result = $db->query("SELECT first_name, last_name, email, account_number FROM accounts WHERE id = ?", [$user_id]);
    $user_info = $user_result->fetch_assoc();
} catch (Exception $e) {
    $user_info = ['first_name' => 'User', 'last_name' => '', 'email' => '', 'account_number' => ''];
}

// Get primary color from super admin settings
$primary_color = '#630fff'; // Default to current theme color
try {
    $color_result = $db->query("SELECT setting_value FROM super_admin_settings WHERE setting_key = 'theme_color' LIMIT 1");
    if ($color_result && $color_data = $color_result->fetch_assoc()) {
        $primary_color = $color_data['setting_value'];
    }
} catch (Exception $e) {
    // Keep default
}

// Get base URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . '://' . $host . '/online_banking';
?>

<!-- User Dashboard Header -->
<div class="user-dashboard-header">
    <div class="header-content">
        <div class="header-left">
            <h1 class="page-title"><?php echo isset($page_title) ? htmlspecialchars($page_title) : 'Dashboard'; ?></h1>
            <?php if (isset($page_subtitle)): ?>
                <p class="page-subtitle"><?php echo htmlspecialchars($page_subtitle); ?></p>
            <?php endif; ?>
        </div>
        
        <div class="header-right">
            <div class="user-info-header">
                <div class="user-details">
                    <div class="user-name"><?php echo htmlspecialchars($user_info['first_name'] . ' ' . $user_info['last_name']); ?></div>
                    <div class="user-role">Account: <?php echo htmlspecialchars($user_info['account_number']); ?></div>
                </div>
                <div class="user-avatar">
                    <?php echo strtoupper(substr($user_info['first_name'], 0, 1) . substr($user_info['last_name'], 0, 1)); ?>
                </div>
            </div>
            

        </div>
    </div>
</div>

<style>
.user-dashboard-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 2rem;
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin: 0;
}

.header-left .page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.header-left .page-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    margin-top: 0.25rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-details {
    text-align: right;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1f2937;
}

.user-role {
    font-size: 0.75rem;
    color: #6b7280;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: <?php echo $primary_color; ?>;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}



/* Mobile responsiveness */
@media (max-width: 768px) {
    .user-dashboard-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .header-right {
        width: 100%;
        justify-content: space-between;
    }

    .user-details {
        text-align: left;
    }
}
</style>
