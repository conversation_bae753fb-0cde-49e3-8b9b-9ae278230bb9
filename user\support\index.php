<?php
/**
 * Help & Support Page
 * Allow users to report transaction issues and contact admin
 */

// Set page variables
$page_title = 'Help & Support';
$current_page = 'support';

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../config/dynamic-css.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Get user's recent transactions for the dropdown
$transactions_query = "SELECT id, description, amount, currency, created_at, transaction_type 
                      FROM transactions 
                      WHERE user_id = ? 
                      ORDER BY created_at DESC 
                      LIMIT 20";
$transactions_result = $db->query($transactions_query, [$user_id]);
$recent_transactions = [];
while ($transaction = $transactions_result->fetch_assoc()) {
    $recent_transactions[] = $transaction;
}

// Get user's previous support tickets
$tickets_query = "SELECT * FROM support_tickets 
                 WHERE user_id = ? 
                 ORDER BY created_at DESC 
                 LIMIT 5";
$tickets_result = $db->query($tickets_query, [$user_id]);
$previous_tickets = [];
while ($ticket = $tickets_result->fetch_assoc()) {
    $previous_tickets[] = $ticket;
}

// Set page title
$page_title = 'Help & Support';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Support CSS -->
<link rel="stylesheet" href="support.css">

<!-- Dynamic CSS Variables -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Support Hero Section -->
        <div class="support-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Help & Support</div>
                    <div class="hero-subtitle">Get help with your account and report issues</div>
                    <div class="hero-stats">
                        24/7 Support Available • Response within 24 hours
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="viewFAQ()">
                        <i class="far fa-question-circle me-2"></i>View FAQ
                    </button>
                </div>
            </div>
        </div>
        <!-- Quick Help Section -->
        <div class="quick-help">
            <h3><i class="far fa-bolt me-2"></i>Quick Help</h3>
            <div class="help-grid">
                <div class="help-item" onclick="showContactInfo()">
                    <div class="help-icon">
                        <i class="far fa-phone"></i>
                    </div>
                    <div class="help-content">
                        <h4>Call Support</h4>
                        <p>Speak with our support team</p>
                    </div>
                </div>

                <div class="help-item" onclick="showLiveChat()">
                    <div class="help-icon">
                        <i class="far fa-comments"></i>
                    </div>
                    <div class="help-content">
                        <h4>Live Chat</h4>
                        <p>Chat with support online</p>
                    </div>
                </div>
                
                <div class="help-item" onclick="viewFAQ()">
                    <div class="help-icon">
                        <i class="far fa-question-circle"></i>
                    </div>
                    <div class="help-content">
                        <h4>FAQ</h4>
                        <p>Find answers to common questions</p>
                    </div>
                </div>
                
                <div class="help-item" onclick="scrollToTicketForm()">
                    <div class="help-icon">
                        <i class="far fa-ticket-alt"></i>
                    </div>
                    <div class="help-content">
                        <h4>Submit Ticket</h4>
                        <p>Report an issue or ask a question</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Support Ticket Form -->
        <div class="ticket-form-section" id="ticketFormSection">
            <div class="form-card">
                <div class="card-header">
                    <h3><i class="far fa-ticket-alt me-2"></i>Submit Support Ticket</h3>
                    <p>Describe your issue and we'll get back to you as soon as possible</p>
                </div>
                
                <form id="supportTicketForm" class="support-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="issueCategory">Issue Category</label>
                            <select id="issueCategory" name="category" class="form-control" required>
                                <option value="">Select issue type</option>
                                <option value="transaction_issue">Transaction Issue</option>
                                <option value="account_access">Account Access</option>
                                <option value="card_issue">Card Issue</option>
                                <option value="wire_transfer">Wire Transfer</option>
                                <option value="billing_question">Billing Question</option>
                                <option value="technical_issue">Technical Issue</option>
                                <option value="security_concern">Security Concern</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="priority">Priority Level</label>
                            <select id="priority" name="priority" class="form-control" required>
                                <option value="">Select priority</option>
                                <option value="low">Low - General inquiry</option>
                                <option value="medium">Medium - Account issue</option>
                                <option value="high">High - Urgent problem</option>
                                <option value="critical">Critical - Security issue</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group" id="transactionGroup" style="display: none;">
                        <label for="relatedTransaction">Related Transaction (Optional)</label>
                        <select id="relatedTransaction" name="transaction_id" class="form-control">
                            <option value="">Select a transaction</option>
                            <?php foreach ($recent_transactions as $transaction): ?>
                            <option value="<?php echo $transaction['id']; ?>">
                                <?php echo date('M j, Y', strtotime($transaction['created_at'])); ?> - 
                                <?php echo htmlspecialchars($transaction['description']); ?> - 
                                <?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" class="form-control" 
                               placeholder="Brief description of your issue" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Detailed Description</label>
                        <textarea id="description" name="description" class="form-control" rows="6" 
                                  placeholder="Please provide as much detail as possible about your issue..." required></textarea>
                        <div class="character-count">
                            <span id="charCount">0</span>/1000 characters
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="contactMethod">Preferred Contact Method</label>
                        <select id="contactMethod" name="contact_method" class="form-control" required>
                            <option value="">How should we contact you?</option>
                            <option value="email">Email</option>
                            <option value="phone">Phone Call</option>
                            <option value="both">Email and Phone</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetTicketForm()">
                            <i class="fas fa-undo me-2"></i>Reset Form
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Submit Ticket
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Previous Tickets -->
        <?php if (!empty($previous_tickets)): ?>
        <div class="previous-tickets">
            <div class="section-header">
                <h3><i class="fas fa-history me-2"></i>Your Recent Tickets</h3>
                <a href="tickets.php" class="view-all-link">View All Tickets</a>
            </div>
            
            <div class="tickets-list">
                <?php foreach ($previous_tickets as $ticket): ?>
                <div class="ticket-item" onclick="viewTicket(<?php echo $ticket['id']; ?>)">
                    <div class="ticket-info">
                        <div class="ticket-subject"><?php echo htmlspecialchars($ticket['subject']); ?></div>
                        <div class="ticket-meta">
                            <span class="ticket-id">#<?php echo str_pad($ticket['id'], 6, '0', STR_PAD_LEFT); ?></span>
                            <span class="ticket-date"><?php echo date('M j, Y', strtotime($ticket['created_at'])); ?></span>
                            <span class="ticket-category"><?php echo ucwords(str_replace('_', ' ', $ticket['category'])); ?></span>
                        </div>
                    </div>
                    <div class="ticket-status">
                        <span class="status-badge status-<?php echo $ticket['status']; ?>">
                            <?php echo ucfirst($ticket['status']); ?>
                        </span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Contact Information -->
        <div class="contact-info" id="contactInfo" style="display: none;">
            <div class="info-card">
                <div class="card-header">
                    <h3><i class="fas fa-phone me-2"></i>Contact Information</h3>
                </div>
                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-content">
                            <h4>Phone Support</h4>
                            <p>+****************</p>
                            <small>Available 24/7</small>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-content">
                            <h4>Email Support</h4>
                            <p><EMAIL></p>
                            <small>Response within 24 hours</small>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="contact-content">
                            <h4>Business Hours</h4>
                            <p>Monday - Friday: 8:00 AM - 8:00 PM</p>
                            <small>Saturday - Sunday: 9:00 AM - 5:00 PM</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        </div> <!-- End Content Container -->
    </div> <!-- End Main Content -->

    <!-- Success Modal -->
    <div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ticket Submitted Successfully</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="successContent">
                    <!-- Success content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div> <!-- End Main Content Wrapper -->

<script src="support.js"></script>

<script>
// Pass PHP data to JavaScript
window.supportData = {
    userId: <?php echo $user_id; ?>,
    userName: '<?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>',
    userEmail: '<?php echo htmlspecialchars($user['email']); ?>'
};
</script>
