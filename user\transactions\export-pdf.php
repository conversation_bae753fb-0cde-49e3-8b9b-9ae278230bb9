<?php
/**
 * Clean PDF Export for Transaction History
 * Generates professional bank statement PDF
 */

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include required files
require_once '../../config/config.php';

// Get user data
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user account information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Get filter parameters
$date_from = $_POST['date_from'] ?? '';
$date_to = $_POST['date_to'] ?? '';
$transaction_type = $_POST['transaction_type'] ?? '';
$amount_min = $_POST['amount_min'] ?? '';
$amount_max = $_POST['amount_max'] ?? '';

// Build WHERE clause
$where_conditions = ["account_id = ?"];
$params = [$user_id];

if (!empty($date_from)) {
    $where_conditions[] = "DATE(created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(created_at) <= ?";
    $params[] = $date_to;
}

if (!empty($transaction_type)) {
    $where_conditions[] = "transaction_type = ?";
    $params[] = $transaction_type;
}

if (!empty($amount_min)) {
    $where_conditions[] = "amount >= ?";
    $params[] = floatval($amount_min);
}

if (!empty($amount_max)) {
    $where_conditions[] = "amount <= ?";
    $params[] = floatval($amount_max);
}

$where_clause = implode(' AND ', $where_conditions);

// Get transactions
$transactions_query = "
    SELECT id, transaction_type, amount, currency, description, sender_name,
           reference_number, category, status, created_at
    FROM account_transactions 
    WHERE $where_clause
    ORDER BY created_at DESC
";

$transactions_result = $db->query($transactions_query, $params);
$transactions = [];
while ($row = $transactions_result->fetch_assoc()) {
    $transactions[] = $row;
}

// Calculate totals
$total_credit = 0;
$total_debit = 0;
$credit_types = ['credit', 'transfer_in', 'deposit'];

foreach ($transactions as $transaction) {
    if (in_array($transaction['transaction_type'], $credit_types)) {
        $total_credit += $transaction['amount'];
    } else {
        $total_debit += $transaction['amount'];
    }
}

// Set headers for HTML display (will be printed to PDF)
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Bank Statement - <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></title>
    <style>
        @page {
            margin: 0.5in;
            size: A4 portrait;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px;
            line-height: 1.3;
            color: #000;
        }
        
        .bank-header {
            text-align: center;
            border-bottom: 3px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .bank-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .bank-address {
            font-size: 8px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .statement-title {
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: 1px solid #000;
            padding: 8px;
            background: #f0f0f0;
            margin: 15px 0;
        }
        
        /* Account Info at Top */
        .account-info-top {
            text-align: left;
            margin-bottom: 15px;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
            font-size: 10px;
        }

        .account-holder, .account-number {
            display: inline-block;
            margin-right: 30px;
        }

        .account-info {
            border: 1px solid #000;
            margin-bottom: 20px;
        }
        
        .info-row {
            display: table;
            width: 100%;
            border-bottom: 1px solid #ccc;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-cell {
            display: table-cell;
            padding: 8px 12px;
            border-right: 1px solid #ccc;
            width: 50%;
        }
        
        .info-cell:last-child {
            border-right: none;
        }
        
        .info-label {
            font-weight: bold;
            font-size: 8px;
            color: #333;
            text-transform: uppercase;
        }
        
        .info-value {
            font-size: 9px;
            color: #000;
            margin-top: 2px;
        }
        
        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 8px;
        }
        
        .transactions-table th {
            background: #e0e0e0;
            border: 1px solid #000;
            padding: 6px 3px;
            text-align: left;
            font-weight: bold;
            font-size: 7px;
            text-transform: uppercase;
        }
        
        .transactions-table td {
            border: 1px solid #999;
            padding: 4px 3px;
            vertical-align: top;
            font-size: 7px;
        }
        
        .transactions-table tbody tr:nth-child(even) {
            background: #f5f5f5;
        }
        
        .amount-credit {
            color: #006600;
            font-weight: bold;
        }
        
        .amount-debit {
            color: #cc0000;
            font-weight: bold;
        }
        
        .summary-box {
            border: 2px solid #000;
            padding: 12px;
            margin: 20px 0;
            background: #f8f8f8;
        }
        
        .summary-title {
            font-weight: bold;
            font-size: 10px;
            text-transform: uppercase;
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
        }
        
        .summary-row {
            display: table;
            width: 100%;
            margin-bottom: 3px;
        }
        
        .summary-label {
            display: table-cell;
            font-weight: bold;
            font-size: 9px;
        }
        
        .summary-value {
            display: table-cell;
            text-align: right;
            font-size: 9px;
        }
        
        .footer {
            margin-top: 30px;
            border-top: 1px solid #000;
            padding-top: 10px;
            font-size: 7px;
            color: #333;
            text-align: center;
        }
        
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <!-- Account Information at Top -->
    <div class="account-info-top">
        <div class="account-holder">
            <strong>Account Holder:</strong> <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
        </div>
        <div class="account-number">
            <strong>Account Number:</strong> <?php echo htmlspecialchars($user['account_number']); ?>
        </div>
    </div>

    <!-- Bank Header -->
    <div class="bank-header">
        <div class="bank-name">SecureBank Online</div>
        <div class="bank-address">
            123 Banking Street, Financial District, NY 10001<br>
            Phone: (************* | Email: <EMAIL>
        </div>
        <div class="statement-title">Account Transaction Statement</div>
    </div>

    <!-- Transactions Table -->
    <?php if (!empty($transactions)): ?>
    <table class="transactions-table">
        <thead>
            <tr>
                <th style="width: 8%;">#</th>
                <th style="width: 12%;">Date</th>
                <th style="width: 20%;">Sender</th>
                <th style="width: 25%;">Description</th>
                <th style="width: 10%;">Type</th>
                <th style="width: 15%;">Amount</th>
                <th style="width: 10%;">Reference</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($transactions as $index => $transaction): ?>
            <tr>
                <td style="text-align: center;"><?php echo $index + 1; ?></td>
                <td><?php echo date('m/d/Y', strtotime($transaction['created_at'])); ?></td>
                <td><?php echo htmlspecialchars($transaction['sender_name'] ?: 'N/A'); ?></td>
                <td><?php echo htmlspecialchars(substr($transaction['description'] ?: 'No description', 0, 40)); ?></td>
                <td><?php echo ucfirst($transaction['transaction_type']); ?></td>
                <td style="text-align: right;" class="amount-<?php echo in_array($transaction['transaction_type'], $credit_types) ? 'credit' : 'debit'; ?>">
                    <?php
                    $sign = in_array($transaction['transaction_type'], $credit_types) ? '+' : '-';
                    echo $sign . '$' . number_format($transaction['amount'], 2);
                    ?>
                </td>
                <td style="font-family: monospace; font-size: 6px;"><?php echo htmlspecialchars($transaction['reference_number'] ?: 'N/A'); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php else: ?>
    <div style="text-align: center; padding: 40px; border: 1px solid #ccc; background: #f9f9f9;">
        <strong>No transactions found for the selected criteria.</strong>
    </div>
    <?php endif; ?>

    <!-- Summary Section -->
    <div class="summary-box">
        <div class="summary-title">Transaction Summary</div>
        <div class="summary-row">
            <div class="summary-label">Total Credits:</div>
            <div class="summary-value amount-credit">+$<?php echo number_format($total_credit, 2); ?></div>
        </div>
        <div class="summary-row">
            <div class="summary-label">Total Debits:</div>
            <div class="summary-value amount-debit">-$<?php echo number_format($total_debit, 2); ?></div>
        </div>
        <div class="summary-row" style="border-top: 1px solid #000; padding-top: 5px; margin-top: 5px;">
            <div class="summary-label"><strong>Net Amount:</strong></div>
            <div class="summary-value"><strong>$<?php echo number_format($total_credit - $total_debit, 2); ?></strong></div>
        </div>
        <div class="summary-row">
            <div class="summary-label">Total Transactions:</div>
            <div class="summary-value"><?php echo count($transactions); ?></div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p><strong>IMPORTANT NOTICE:</strong> This statement is computer generated and does not require a signature.
        Please verify all transactions and report any discrepancies within 30 days.</p>
        <p>For customer service, call (************* or visit www.securebankonline.com</p>
        <p>Statement generated on <?php echo date('F j, Y \a\t g:i A T'); ?> | Page 1 of 1</p>
    </div>

    <!-- Auto-print script -->
    <script>
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
        </div>
        <div class="info-row">
            <div class="info-cell">
                <div class="info-label">Statement Period</div>
                <div class="info-value">
                    <?php 
                    if ($date_from && $date_to) {
                        echo date('M j, Y', strtotime($date_from)) . ' - ' . date('M j, Y', strtotime($date_to));
                    } else {
                        echo 'All Available Transactions';
                    }
                    ?>
                </div>
            </div>
            <div class="info-cell">
                <div class="info-label">Generated On</div>
                <div class="info-value"><?php echo date('F j, Y \a\t g:i A'); ?></div>
            </div>
        </div>
        <div class="info-row">
            <div class="info-cell">
                <div class="info-label">Current Balance</div>
                <div class="info-value">$<?php echo number_format($user['balance'], 2); ?></div>
            </div>
            <div class="info-cell">
                <div class="info-label">Account Status</div>
                <div class="info-value"><?php echo ucfirst($user['status']); ?></div>
            </div>
        </div>
    </div>
