<?php
/**
 * Transaction History Page
 * Comprehensive transaction statement with filtering and export
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include database connection and dynamic CSS
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

// Get user data from database
$db = getDB();
$user_id = $_SESSION['user_id'];

// Get user account information
$user_query = "SELECT * FROM accounts WHERE id = ?";
$user_result = $db->query($user_query, [$user_id]);
$user = $user_result->fetch_assoc();

if (!$user) {
    header('Location: ../../login.php');
    exit();
}

// Pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Filter settings
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$transaction_type = $_GET['transaction_type'] ?? '';
$amount_min = $_GET['amount_min'] ?? '';
$amount_max = $_GET['amount_max'] ?? '';

// Build WHERE clause for filters (using account_id instead of user_id for account_transactions table)
$where_conditions = ["account_id = ?"];
$params = [$user_id];

if (!empty($date_from)) {
    $where_conditions[] = "DATE(created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(created_at) <= ?";
    $params[] = $date_to;
}

if (!empty($transaction_type)) {
    $where_conditions[] = "transaction_type = ?";
    $params[] = $transaction_type;
}

if (!empty($amount_min)) {
    $where_conditions[] = "amount >= ?";
    $params[] = floatval($amount_min);
}

if (!empty($amount_max)) {
    $where_conditions[] = "amount <= ?";
    $params[] = floatval($amount_max);
}

$where_clause = implode(' AND ', $where_conditions);

// Get transactions with pagination (using account_transactions table with actual sender_name)
$transactions_query = "
    SELECT id, transaction_type, amount, currency, description, sender_name,
           reference_number, category, status, created_at
    FROM account_transactions
    WHERE $where_clause
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
";

$count_query = "SELECT COUNT(*) as total FROM account_transactions WHERE $where_clause";

// Add pagination parameters
$params[] = $per_page;
$params[] = $offset;

try {
    $transactions_result = $db->query($transactions_query, $params);
    $transactions = [];
    while ($row = $transactions_result->fetch_assoc()) {
        // Ensure all fields have default values to prevent display issues
        $row['currency'] = $row['currency'] ?: 'USD';
        $row['description'] = $row['description'] ?: 'Transaction';
        $row['sender_name'] = $row['sender_name'] ?: 'System';
        $row['reference_number'] = $row['reference_number'] ?: 'REF' . $row['id'];
        $row['category'] = $row['category'] ?: 'adjustment';
        $row['status'] = $row['status'] ?: 'completed';
        $transactions[] = $row;
    }
} catch (Exception $e) {
    error_log("Transaction query error: " . $e->getMessage());
    $transactions = [];
}

// Get total count for pagination
$count_params = array_slice($params, 0, -2); // Remove LIMIT and OFFSET params
$count_result = $db->query($count_query, $count_params);
$total_transactions = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_transactions / $per_page);

// Get stats data
// 1. Available Balance
$available_balance = $user['balance'];

// 2. Virtual Card Balance
try {
    $virtual_card_query = "SELECT COALESCE(SUM(spending_limit), 0) as total_balance FROM virtual_cards WHERE user_id = ? AND status = 'active'";
    $virtual_card_result = $db->query($virtual_card_query, [$user_id]);
    $virtual_card_balance = $virtual_card_result->fetch_assoc()['total_balance'];
} catch (Exception $e) {
    $virtual_card_balance = 0;
}

// 3. Last Outflow (most recent debit transaction)
$last_outflow_query = "SELECT amount FROM transactions WHERE user_id = ? AND transaction_type = 'debit' ORDER BY created_at DESC LIMIT 1";
$last_outflow_result = $db->query($last_outflow_query, [$user_id]);
$last_outflow = $last_outflow_result->fetch_assoc()['amount'] ?? 0;

// 4. Wallet Balance (crypto wallets)
$wallet_balance_query = "SELECT COALESCE(SUM(wallet_balance), 0) as total_balance FROM crypto_wallets WHERE account_id = ? AND status = 'active'";
$wallet_balance_result = $db->query($wallet_balance_query, [$user_id]);
$wallet_balance = $wallet_balance_result->fetch_assoc()['total_balance'];

// Set page title and subtitle
$page_title = 'Transaction History';
$page_subtitle = 'Complete transaction statement and export';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Transaction History CSS -->
<link rel="stylesheet" href="transactions.css">

<!-- Dynamic CSS Variables Only -->
<style>
    <?php echo getInlineDynamicCSS(); ?>




</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content">

        <!-- Mini Hero Section -->
        <div class="transaction-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Transaction History</div>
                    <div class="hero-subtitle">Complete statement of all your banking activities</div>
                    <div class="hero-stats">
                        Total Transactions: <?php echo number_format($total_transactions); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf me-2"></i>Export PDF
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Available Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Available</div>
                    <div class="balance-amount available">
                        <?php echo formatCurrency($available_balance, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">Main Account</div>
                </div>
            </div>

            <!-- Virtual Card Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon card">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Virtual Cards</div>
                    <div class="balance-amount card">
                        <?php echo formatCurrency($virtual_card_balance, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">Active Cards</div>
                </div>
            </div>

            <!-- Last Outflow Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Last Outflow</div>
                    <div class="balance-amount" style="color: #ef4444;">
                        -<?php echo formatCurrency($last_outflow, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">Recent Debit</div>
                </div>
            </div>

            <!-- Wallet Balance Card -->
            <div class="balance-card-new">
                <div class="balance-icon crypto">
                    <i class="fab fa-bitcoin"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Crypto Wallet</div>
                    <div class="balance-amount wallet">
                        <?php echo formatCurrency($wallet_balance, $user['currency'] ?? 'USD'); ?>
                    </div>
                    <div class="balance-subtitle">Digital Assets</div>
                </div>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section mb-4">
            <div class="filters-card">
                <div class="filters-header">
                    <h5><i class="fas fa-filter me-2"></i>Filter Transactions</h5>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-times me-1"></i>Clear
                    </button>
                </div>
                <form method="GET" class="filters-form">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Date From</label>
                            <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date To</label>
                            <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Type</label>
                            <select name="transaction_type" class="form-control">
                                <option value="">All Types</option>
                                <option value="credit" <?php echo $transaction_type === 'credit' ? 'selected' : ''; ?>>Credit</option>
                                <option value="debit" <?php echo $transaction_type === 'debit' ? 'selected' : ''; ?>>Debit</option>
                                <option value="transfer_in" <?php echo $transaction_type === 'transfer_in' ? 'selected' : ''; ?>>Transfer In</option>
                                <option value="transfer_out" <?php echo $transaction_type === 'transfer_out' ? 'selected' : ''; ?>>Transfer Out</option>
                                <option value="deposit" <?php echo $transaction_type === 'deposit' ? 'selected' : ''; ?>>Deposit</option>
                                <option value="withdrawal" <?php echo $transaction_type === 'withdrawal' ? 'selected' : ''; ?>>Withdrawal</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Min Amount</label>
                            <input type="number" name="amount_min" class="form-control" step="0.01" value="<?php echo htmlspecialchars($amount_min); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Max Amount</label>
                            <input type="number" name="amount_max" class="form-control" step="0.01" value="<?php echo htmlspecialchars($amount_max); ?>">
                        </div>
                    </div>
                    <div class="filters-actions mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transaction Table Section -->
        <div class="transactions-section">
            <div class="transactions-header">
                <h3><i class="fas fa-list me-2"></i>Transaction Statement</h3>
                <div class="transactions-summary">
                    Showing <?php echo number_format(count($transactions)); ?> of <?php echo number_format($total_transactions); ?> transactions
                </div>
            </div>

            <?php if (!empty($transactions)): ?>
            <div class="table-container">
                <div class="table-responsive">
                    <table class="transactions-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Sender</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Reference</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($transactions as $index => $transaction): ?>
                        <tr>
                            <td class="transaction-number">
                                <?php echo ($page - 1) * $per_page + $index + 1; ?>
                            </td>
                            <td class="sender-name">
                                <?php echo htmlspecialchars($transaction['sender_name'] ?: 'N/A'); ?>
                            </td>
                            <td class="transaction-type">
                                <span class="type-badge type-<?php echo $transaction['transaction_type']; ?>">
                                    <?php
                                    switch ($transaction['transaction_type']) {
                                        case 'credit':
                                            echo '<i class="fas fa-arrow-up me-1"></i>Credit';
                                            break;
                                        case 'debit':
                                            echo '<i class="fas fa-arrow-down me-1"></i>Debit';
                                            break;
                                        case 'transfer_in':
                                            echo '<i class="fas fa-arrow-right me-1"></i>Transfer In';
                                            break;
                                        case 'transfer_out':
                                            echo '<i class="fas fa-arrow-left me-1"></i>Transfer Out';
                                            break;
                                        case 'deposit':
                                            echo '<i class="fas fa-plus me-1"></i>Deposit';
                                            break;
                                        case 'withdrawal':
                                            echo '<i class="fas fa-minus me-1"></i>Withdrawal';
                                            break;
                                        default:
                                            echo '<i class="fas fa-exchange-alt me-1"></i>' . ucfirst($transaction['transaction_type']);
                                    }
                                    ?>
                                </span>
                            </td>
                            <td class="description" onclick="showTransactionDetails(<?php echo htmlspecialchars(json_encode(array_merge($transaction, [
                                'formatted_date' => (new DateTime($transaction['created_at']))->format('M d, Y'),
                                'formatted_time' => (new DateTime($transaction['created_at']))->format('h:i A'),
                                'formatted_amount' => (in_array($transaction['transaction_type'], ['credit', 'transfer_in', 'deposit']) ? '+' : '-') . formatCurrency($transaction['amount'], $transaction['currency'])
                            ])), ENT_QUOTES, 'UTF-8'); ?>)">
                                <?php
                                $desc = $transaction['description'] ?: 'No description';
                                $category = $transaction['category'] ? ' - ' . strtoupper($transaction['category']) : '';
                                $full_desc = $desc . $category;
                                echo htmlspecialchars(strlen($full_desc) > 35 ? substr($full_desc, 0, 35) . '...' : $full_desc);
                                ?>
                            </td>
                            <td class="reference-number">
                                <?php echo htmlspecialchars($transaction['reference_number'] ?: 'N/A'); ?>
                            </td>
                            <td class="amount">
                                <span class="amount-value amount-<?php echo $transaction['transaction_type']; ?>">
                                    <?php
                                    // Determine sign based on transaction type
                                    $credit_types = ['credit', 'transfer_in', 'deposit'];
                                    $sign = in_array($transaction['transaction_type'], $credit_types) ? '+' : '-';
                                    echo $sign . formatCurrency($transaction['amount'], $transaction['currency']);
                                    ?>
                                </span>
                            </td>
                            <td class="date-time">
                                <?php
                                $date = new DateTime($transaction['created_at']);
                                echo $date->format('m/d/y');
                                ?>
                            </td>
                            <td class="status">
                                <span class="status-badge status-<?php echo $transaction['status']; ?>">
                                    <?php echo ucfirst($transaction['status']); ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination-wrapper">
                <nav aria-label="Transaction pagination">
                    <ul class="pagination">
                        <!-- Previous Page -->
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                        <?php endif; ?>

                        <!-- Page Numbers -->
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);

                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <!-- Next Page -->
                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>

                <div class="pagination-info">
                    Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                    (<?php echo number_format($total_transactions); ?> total transactions)
                </div>
            </div>
            <?php endif; ?>

            <?php else: ?>
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No transactions found</h5>
                <p class="text-muted">
                    <?php if (!empty($date_from) || !empty($date_to) || !empty($transaction_type) || !empty($amount_min) || !empty($amount_max)): ?>
                        Try adjusting your filters to see more results.
                    <?php else: ?>
                        Your transaction history will appear here once you start banking.
                    <?php endif; ?>
                </p>
                <?php if (!empty($date_from) || !empty($date_to) || !empty($transaction_type) || !empty($amount_min) || !empty($amount_max)): ?>
                <button class="btn btn-outline-primary" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>Clear Filters
                </button>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>

    </div>

    <!-- Bank Receipt Style Modal -->
    <div id="transactionModal" class="receipt-modal">
        <div class="modal-overlay" onclick="closeModal()"></div>
        <div class="receipt-container">
            <!-- Receipt Header -->
            <div class="receipt-header">
                <div class="bank-logo">
                    <i class="fas fa-university"></i>
                    <span class="bank-name">PremierBank Pro</span>
                </div>
                <div class="receipt-title">TRANSACTION RECEIPT</div>
                <button class="receipt-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Receipt Body -->
            <div class="receipt-body">
                <div id="modalContent" class="receipt-content">
                    <!-- Content will be inserted here -->
                </div>
            </div>

            <!-- Receipt Footer -->
            <div class="receipt-footer">
                <div class="receipt-actions">
                    <button class="btn-print" onclick="printReceipt()">
                        <i class="fas fa-print"></i> Print Receipt
                    </button>
                    <button class="btn-close-receipt" onclick="closeModal()">
                        <i class="fas fa-times"></i> Close
                    </button>
                </div>
                <div class="receipt-disclaimer">
                    <small>This is an electronic receipt. Please retain for your records.</small>
                </div>
            </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<!-- Include Transaction History JavaScript -->
<script src="transactions.js"></script>


