<?php
/**
 * Check Account Number Format
 * NO SESSION REQUIRED - DEBUG TOOL
 */

require_once '../../config/config.php';

echo "<h1>🔍 Account Number Format Check</h1>";

try {
    $db = getDB();
    
    echo "<h3>Sample Account Numbers:</h3>";
    $accounts = $db->query("SELECT id, account_number, first_name, last_name, LENGTH(account_number) as length FROM accounts WHERE is_admin = 0 LIMIT 10");
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Account Number</th><th>Length</th><th>Name</th></tr>";
    
    $lengths = [];
    while ($account = $accounts->fetch_assoc()) {
        $length = $account['length'];
        $lengths[] = $length;
        
        echo "<tr>";
        echo "<td>" . $account['id'] . "</td>";
        echo "<td><strong>" . $account['account_number'] . "</strong></td>";
        echo "<td>" . $length . " digits</td>";
        echo "<td>" . htmlspecialchars($account['first_name'] . ' ' . $account['last_name']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show length statistics
    $unique_lengths = array_unique($lengths);
    echo "<h3>📊 Account Number Lengths Found:</h3>";
    foreach ($unique_lengths as $length) {
        $count = array_count_values($lengths)[$length];
        echo "<p><strong>$length digits:</strong> $count accounts</p>";
    }
    
    // Check the specific account
    echo "<h3>🎯 Specific Account Check:</h3>";
    $test_account = '************';
    echo "<p><strong>Test Account:</strong> $test_account</p>";
    echo "<p><strong>Length:</strong> " . strlen($test_account) . " digits</p>";
    
    $check_query = "SELECT id, first_name, last_name FROM accounts WHERE account_number = ? AND is_admin = 0";
    $check_result = $db->query($check_query, [$test_account]);
    
    if ($check_result->num_rows > 0) {
        $user = $check_result->fetch_assoc();
        echo "<p style='color: green;'>✅ <strong>Account Found:</strong> " . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>Account Not Found</strong></p>";
    }
    
    // Suggest regex pattern
    if (!empty($unique_lengths)) {
        $min_length = min($unique_lengths);
        $max_length = max($unique_lengths);
        
        echo "<h3>💡 Suggested Regex Pattern:</h3>";
        if ($min_length === $max_length) {
            echo "<code>/^\\d{" . $min_length . "}$/</code> (exactly $min_length digits)";
        } else {
            echo "<code>/^\\d{" . $min_length . "," . $max_length . "}$/</code> ($min_length to $max_length digits)";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f0f0f0; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
