<?php
/**
 * Final Transfer Test - Simple Direct Connection
 * NO SESSION REQUIRED - DEBUG TOOL
 */

session_start();
require_once '../../config/config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Final Transfer Test - Simple & Direct</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .test-section { border: 1px solid #ddd; margin: 15px 0; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body class="container py-4">
    <h1>🎯 Final Transfer Test - Simple & Direct</h1>

    <div class="alert alert-success">
        <h4>✅ SOLUTION IMPLEMENTED</h4>
        <p><strong>Problem:</strong> Complex JSON/PDO approach was causing errors</p>
        <p><strong>Solution:</strong> Simple MySQLi + Form submission like other pages</p>
    </div>

    <div class="test-section">
        <h2>🔧 What Was Fixed</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>❌ Before (Complex):</h4>
                <ul>
                    <li>JSON requests/responses</li>
                    <li>Mixed PDO/MySQLi code</li>
                    <li>Complex error handling</li>
                    <li>Empty response errors</li>
                    <li>400 Bad Request errors</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4>✅ After (Simple):</h4>
                <ul>
                    <li>Form data submission</li>
                    <li>Pure MySQLi like other pages</li>
                    <li>Simple text responses</li>
                    <li>Direct database connection</li>
                    <li>No JSON parsing errors</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📁 New File Structure</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>Inter-Bank Transfer:</h4>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-code text-success"></i> <code>process-interbank-transfer-v2.php</code> <span class="badge bg-success">Simple</span></li>
                    <li><i class="fas fa-file-code text-info"></i> <code>interbank-transfers.js</code> <span class="badge bg-info">Form Data</span></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4>Local Bank Transfer:</h4>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-code text-success"></i> <code>process-local-transfer-simple.php</code> <span class="badge bg-success">Simple</span></li>
                    <li><i class="fas fa-file-code text-info"></i> <code>transfers.js</code> <span class="badge bg-info">Updated</span></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Database Connection Test</h2>
        <?php
        try {
            $db = getDB();
            echo "<p class='success'>✅ MySQLi connection successful</p>";
            
            // Test both transfer tables
            $local_count = $db->query("SELECT COUNT(*) as count FROM local_transfers")->fetch_assoc()['count'];
            $interbank_count = $db->query("SELECT COUNT(*) as count FROM interbank_transfers")->fetch_assoc()['count'];
            
            echo "<p class='info'>📊 Local transfers: $local_count</p>";
            echo "<p class='info'>📊 Inter-bank transfers: $interbank_count</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🎯 Direct Transfer Tests</h2>
        
        <?php if (isset($_SESSION['user_id'])): ?>
            <div class="alert alert-info">
                <strong>User Session Active:</strong> <?php echo $_SESSION['username'] ?? 'Unknown'; ?>
                <br><strong>Ready for testing!</strong>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>🏦 Test Inter-Bank Transfer</h4>
                    <button class="btn btn-success" onclick="testInterbank()">Test Inter-Bank API</button>
                    <div id="interbank-result" class="mt-2"></div>
                </div>
                
                <div class="col-md-6">
                    <h4>🏛️ Test Local Bank Transfer</h4>
                    <button class="btn btn-primary" onclick="testLocal()">Test Local Bank API</button>
                    <div id="local-result" class="mt-2"></div>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-warning">
                <strong>No User Session:</strong> Please login to test transfers.
                <br><a href="../login.php" class="btn btn-primary mt-2">Login as User</a>
            </div>
        <?php endif; ?>
    </div>

    <div class="test-section">
        <h2>📋 Manual Testing Steps</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>Inter-Bank Transfer:</h4>
                <ol>
                    <li>Go to <a href="index.php">transfers page</a></li>
                    <li>Select "Inter-Bank Transfer"</li>
                    <li>Enter internal account number</li>
                    <li>Submit transfer</li>
                    <li><strong>Expected:</strong> Success message, no errors</li>
                </ol>
            </div>
            
            <div class="col-md-6">
                <h4>Local Bank Transfer:</h4>
                <ol>
                    <li>Select "Local Bank Transfer"</li>
                    <li>Enter external bank details</li>
                    <li>Submit transfer</li>
                    <li><strong>Expected:</strong> Success message, no errors</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 Quick Links</h2>
        <a href="index.php" class="btn btn-primary me-2">🚀 Main Transfers Page</a>
        <a href="test-simple-interbank.php" class="btn btn-success me-2">🏦 Inter-Bank Test</a>
        <a href="../../admin/transfers.php" class="btn btn-secondary">👨‍💼 Admin Transfers</a>
    </div>

    <div class="test-section">
        <h2>📊 Expected Results</h2>
        <div class="alert alert-success">
            <h5>✅ Success Criteria:</h5>
            <ul class="mb-0">
                <li><strong>No JSON Errors:</strong> No "Unexpected end of JSON input"</li>
                <li><strong>No 400 Errors:</strong> No "Bad Request" responses</li>
                <li><strong>No Empty Responses:</strong> Clear success/error messages</li>
                <li><strong>Direct Database:</strong> MySQLi connection like other pages</li>
                <li><strong>Simple Processing:</strong> Form data, not JSON</li>
            </ul>
        </div>
    </div>

    <script>
        function testInterbank() {
            const resultDiv = document.getElementById('interbank-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing...';
            
            const formData = new FormData();
            formData.append('source_account', 'main');
            formData.append('beneficiary_account', '**********');
            formData.append('beneficiary_name', 'Test User');
            formData.append('amount', '5.00');
            formData.append('narration', 'Test Inter-Bank Transfer');
            
            fetch('process-interbank-transfer-v2.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                console.log('Inter-bank response:', data);
                if (data.startsWith('SUCCESS:')) {
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ ' + data + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">❌ ' + data + '</div>';
                }
            })
            .catch(error => {
                console.error('Inter-bank error:', error);
                resultDiv.innerHTML = '<div class="alert alert-danger">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testLocal() {
            const resultDiv = document.getElementById('local-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing...';
            
            const formData = new FormData();
            formData.append('source_account', 'main');
            formData.append('beneficiary_account', '**********');
            formData.append('beneficiary_name', 'Test External Bank');
            formData.append('beneficiary_bank', 'External Bank Ltd');
            formData.append('routing_code', '*********');
            formData.append('account_type', 'checking');
            formData.append('amount', '10.00');
            formData.append('narration', 'Test Local Transfer');
            
            fetch('process-local-transfer-simple.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                console.log('Local transfer response:', data);
                if (data.startsWith('SUCCESS:')) {
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ ' + data + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">❌ ' + data + '</div>';
                }
            })
            .catch(error => {
                console.error('Local transfer error:', error);
                resultDiv.innerHTML = '<div class="alert alert-danger">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
