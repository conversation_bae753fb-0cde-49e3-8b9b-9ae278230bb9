/**
 * Inter-Bank Transfer JavaScript V2
 * Dedicated JavaScript for inter-bank transfers
 * Separated from local bank transfers to prevent conflicts
 */

// Global variables for inter-bank transfers
let interbankTransferData = {};
let interbankSuccessModal = null;

/**
 * Initialize inter-bank transfer functionality
 */
function initializeInterbankTransfers() {
    console.log('🏦 Inter-bank transfer module loaded');
    
    // Initialize Bootstrap modals
    const successModalElement = document.getElementById('successModal');
    if (successModalElement) {
        interbankSuccessModal = new bootstrap.Modal(successModalElement);
    }
    
    // Setup form validation for inter-bank transfers
    setupInterbankFormValidation();
    
    console.log('✅ Inter-bank transfer module initialized');
}

/**
 * Setup form validation for inter-bank transfers
 */
function setupInterbankFormValidation() {
    const form = document.getElementById('transferForm');
    if (!form) return;
    
    // Real-time validation for inter-bank specific fields
    const accountField = document.getElementById('beneficiaryAccount');
    const nameField = document.getElementById('beneficiaryName');
    const amountField = document.getElementById('transferAmount');
    
    if (accountField) {
        accountField.addEventListener('input', validateInterbankAccount);
        accountField.addEventListener('blur', checkInterbankAccountExists);
    }
    
    if (nameField) {
        nameField.addEventListener('input', validateInterbankName);
    }
    
    if (amountField) {
        amountField.addEventListener('input', validateInterbankAmount);
    }
}

/**
 * Validate inter-bank account number
 */
function validateInterbankAccount() {
    const accountField = document.getElementById('beneficiaryAccount');
    const accountNumber = accountField.value.trim();
    
    // Clear previous validation
    accountField.classList.remove('is-valid', 'is-invalid');
    clearFieldError(accountField);
    
    if (accountNumber.length === 0) {
        return;
    }
    
    // Validate format (10-12 digits to match actual account numbers)
    if (!/^\d{10,12}$/.test(accountNumber)) {
        showFieldError(accountField, 'Account number must be 10-12 digits');
        return false;
    }
    
    // Check if not own account
    if (window.userAccountNumber && accountNumber === window.userAccountNumber) {
        showFieldError(accountField, 'Cannot transfer to your own account');
        return false;
    }
    
    accountField.classList.add('is-valid');
    return true;
}

/**
 * Check if inter-bank account exists in our system
 */
function checkInterbankAccountExists() {
    const accountField = document.getElementById('beneficiaryAccount');
    const accountNumber = accountField.value.trim();
    
    if (!validateInterbankAccount()) {
        return;
    }
    
    // Show loading state
    showAccountValidationLoading();
    
    fetch('validate-account.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            account_number: accountNumber
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.exists && data.is_internal) {
            showAccountValidationSuccess(`✅ Internal user: ${data.name}`, 'Inter-bank transfer available');
            
            // Auto-fill name if empty
            const nameField = document.getElementById('beneficiaryName');
            if (!nameField.value) {
                nameField.value = data.name;
                validateInterbankName();
            }
        } else {
            showAccountValidationError('Account not found in our system', 'Inter-bank transfers require internal accounts');
        }
    })
    .catch(error => {
        console.error('Account validation error:', error);
        showAccountValidationError('Unable to verify account', 'Please check the account number');
    });
}

/**
 * Validate beneficiary name for inter-bank transfers
 */
function validateInterbankName() {
    const nameField = document.getElementById('beneficiaryName');
    const name = nameField.value.trim();
    
    nameField.classList.remove('is-valid', 'is-invalid');
    clearFieldError(nameField);
    
    if (name.length === 0) {
        return;
    }
    
    if (name.length < 2) {
        showFieldError(nameField, 'Name must be at least 2 characters');
        return false;
    }
    
    if (!/^[a-zA-Z\s]+$/.test(name)) {
        showFieldError(nameField, 'Name can only contain letters and spaces');
        return false;
    }
    
    nameField.classList.add('is-valid');
    return true;
}

/**
 * Validate transfer amount for inter-bank transfers
 */
function validateInterbankAmount() {
    const amountField = document.getElementById('transferAmount');
    const amount = parseFloat(amountField.value) || 0;
    
    amountField.classList.remove('is-valid', 'is-invalid');
    clearFieldError(amountField);
    
    if (amount === 0) {
        return;
    }
    
    if (amount < 1) {
        showFieldError(amountField, 'Minimum transfer amount is $1.00');
        return false;
    }
    
    if (amount > 50000) {
        showFieldError(amountField, 'Maximum transfer amount is $50,000.00');
        return false;
    }
    
    // Check balance (if available)
    if (window.userBalance && amount > window.userBalance) {
        showFieldError(amountField, 'Insufficient balance');
        return false;
    }
    
    amountField.classList.add('is-valid');
    updateInterbankTransferSummary();
    return true;
}

/**
 * Update transfer summary for inter-bank transfers
 */
function updateInterbankTransferSummary() {
    const amount = parseFloat(document.getElementById('transferAmount').value) || 0;
    const currency = document.querySelector('.input-group-text').textContent;
    
    // No fees for inter-bank transfers
    const fee = 0;
    const total = amount;
    
    // Update summary display
    document.getElementById('summaryAmount').textContent = amount > 0 ? 
        `${currency} ${amount.toFixed(2)}` : '-';
    
    document.getElementById('summaryFee').textContent = 'Free';
    document.getElementById('summaryTotal').textContent = amount > 0 ? 
        `${currency} ${total.toFixed(2)}` : '-';
}

/**
 * Collect inter-bank transfer data
 */
function collectInterbankTransferData() {
    interbankTransferData = {
        source_account: document.getElementById('sourceAccount')?.value || 'main',
        beneficiary_account: document.getElementById('beneficiaryAccount')?.value || '',
        beneficiary_name: document.getElementById('beneficiaryName')?.value || '',
        amount: parseFloat(document.getElementById('transferAmount')?.value || 0),
        narration: document.getElementById('transferNarration')?.value || 'Inter-Bank Transfer',
        currency: document.querySelector('.input-group-text')?.textContent || 'USD'
    };

    console.log('📊 Inter-bank transfer data collected:', interbankTransferData);
}

/**
 * Process inter-bank transfer - Simple form submission
 */
function processInterbankTransfer() {
    const submitBtn = document.getElementById('submitTransfer');
    const originalText = submitBtn.innerHTML;

    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing Inter-Bank Transfer...';
    submitBtn.disabled = true;

    console.log('🏦 Processing inter-bank transfer...');

    // Create form data
    const formData = new FormData();
    formData.append('source_account', document.getElementById('sourceAccount')?.value || 'main');
    formData.append('beneficiary_account', document.getElementById('beneficiaryAccount')?.value || '');
    formData.append('beneficiary_name', document.getElementById('beneficiaryName')?.value || '');
    formData.append('amount', document.getElementById('transferAmount')?.value || '0');
    formData.append('narration', document.getElementById('transferNarration')?.value || 'Inter-Bank Transfer');

    fetch('process-interbank-transfer-v2.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('✅ Inter-bank transfer successful:', data);
        if (data.success) {
            showInterbankTransferSuccess(data);
        } else {
            throw new Error(data.error || 'Transfer failed');
        }
    })
    .catch(error => {
        console.error('❌ Inter-bank transfer failed:', error);
        if (window.showAlert) {
            window.showAlert(`Transfer failed: ${error.message}`, 'danger');
        } else {
            alert(`Transfer failed: ${error.message}`);
        }
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

/**
 * Show inter-bank transfer success modal with receipt
 */
function showInterbankTransferSuccess(data) {
    console.log('🎉 Showing inter-bank transfer success modal:', data);

    // Update success modal content if elements exist
    const successAmountEl = document.getElementById('successAmount');
    const successRecipientEl = document.getElementById('successRecipient');
    const successReferenceEl = document.getElementById('successReference');
    const successFeeEl = document.getElementById('successFee');
    const successMessageEl = document.getElementById('successMessage');

    if (successAmountEl) successAmountEl.textContent = `${data.currency || '$'} ${data.amount.toFixed(2)}`;
    if (successRecipientEl) successRecipientEl.textContent = data.recipient;
    if (successReferenceEl) successReferenceEl.textContent = data.transaction_id;
    if (successFeeEl) successFeeEl.textContent = 'Free';

    // Update main success message
    if (successMessageEl) {
        successMessageEl.textContent = `Your inter-bank transfer of ${data.currency || '$'} ${data.amount.toFixed(2)} to ${data.recipient} has been processed successfully.`;
    }

    // Store receipt data for download
    window.transferReceiptData = data;

    // Show success modal (try both possible modal instances)
    if (interbankSuccessModal) {
        interbankSuccessModal.show();
    } else if (window.successModal) {
        window.successModal.show();
    } else {
        // Fallback: try to find and show modal manually
        const modalElement = document.getElementById('successModal');
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            // Add event listener for when modal is hidden to redirect
            modalElement.addEventListener('hidden.bs.modal', function() {
                window.location.href = '/user/transfers/';
            }, { once: true });
        }
    }

    // Clear form
    clearInterbankForm();

    // Update balance if available
    if (data.new_balance !== undefined && window.updateUserBalance) {
        window.updateUserBalance(data.new_balance);
    }
}

/**
 * Clear inter-bank transfer form
 */
function clearInterbankForm() {
    const form = document.getElementById('transferForm');
    if (form) {
        form.reset();
        
        // Clear validation states
        const formControls = form.querySelectorAll('.form-control');
        formControls.forEach(control => {
            control.classList.remove('is-valid', 'is-invalid');
            clearFieldError(control);
        });
        
        // Clear account validation
        clearAccountValidation();
        
        // Reset summary
        updateInterbankTransferSummary();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on the inter-bank transfer page
    if (document.getElementById('transferForm') && window.currentTransferType === 'inter-bank') {
        initializeInterbankTransfers();
    }
});

// Export functions for global use
window.InterbankTransfers = {
    processInterbankTransfer,
    validateInterbankAccount,
    validateInterbankName,
    validateInterbankAmount,
    updateInterbankTransferSummary,
    clearInterbankForm
};
