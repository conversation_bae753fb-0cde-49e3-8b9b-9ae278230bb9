<?php
/**
 * Simple Inter-Bank Transfer Processing
 * Direct MySQLi connection like other pages in the application
 */

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    echo "Error: Please login first";
    exit();
}

// Include database connection
require_once '../../config/config.php';

// Get form data
$source_account = $_POST['source_account'] ?? 'main';
$beneficiary_account = $_POST['beneficiary_account'] ?? '';
$beneficiary_name = $_POST['beneficiary_name'] ?? '';
$amount = floatval($_POST['amount'] ?? 0);
$narration = $_POST['narration'] ?? 'Inter-Bank Transfer';

$user_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

try {
    // Get database connection
    $db = getDB();

    // Get user information
    $user_query = "SELECT id, account_number, first_name, last_name, balance, currency FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();

    if (!$user) {
        throw new Exception('User account not found');
    }
    
    // Validate required fields
    if (empty($beneficiary_account) || empty($beneficiary_name) || $amount <= 0) {
        throw new Exception('Missing required transfer information');
    }

    // Validate amount
    if ($amount < 1) {
        throw new Exception('Minimum transfer amount is $1.00');
    }

    if ($amount > 50000) {
        throw new Exception('Maximum transfer amount is $50,000.00');
    }

    // Validate beneficiary account format (10-12 digits to match actual account numbers)
    if (!preg_match('/^\d{10,12}$/', $beneficiary_account)) {
        throw new Exception('Invalid account number format (must be 10-12 digits)');
    }

    // Check if trying to transfer to own account
    if ($beneficiary_account === $user['account_number']) {
        throw new Exception('Cannot transfer to your own account');
    }
    
    // Check balance
    $available_balance = floatval($user['balance']);

    // No fees for inter-bank transfers
    $transfer_fee = 0;
    $total_debit = $amount;

    // Check sufficient balance
    if ($total_debit > $available_balance) {
        throw new Exception('Insufficient balance for this transfer');
    }

    // Find recipient in our system
    $recipient_query = "SELECT id, first_name, last_name FROM accounts
                       WHERE account_number = ? AND is_admin = 0 AND status = 'active' AND id != ?";
    $recipient_result = $db->query($recipient_query, [$beneficiary_account, $user_id]);

    if ($recipient_result->num_rows === 0) {
        throw new Exception('Recipient account not found in our system');
    }

    $recipient = $recipient_result->fetch_assoc();
    $recipient_id = $recipient['id'];
    $recipient_full_name = $recipient['first_name'] . ' ' . $recipient['last_name'];
    
    // Start database transaction
    $db->beginTransaction();

    try {
        // Generate unique transfer reference
        $transfer_reference = 'IBT' . date('Ymd') . sprintf('%06d', mt_rand(100000, 999999));

        // Insert transfer record into INTERBANK TRANSFERS table
        $insert_transfer_sql = "INSERT INTO interbank_transfers (
            transaction_id, sender_id, recipient_id, sender_account_type,
            amount, currency, transfer_fee, narration, status, created_at, completed_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'completed', NOW(), NOW())";

        $transfer_id = $db->insert($insert_transfer_sql, [
            $transfer_reference, $user_id, $recipient_id, $source_account,
            $amount, $user['currency'], $transfer_fee, $narration
        ]);
        
        // Debit sender's account
        $update_sender_sql = "UPDATE accounts SET balance = balance - ? WHERE id = ?";
        $db->query($update_sender_sql, [$total_debit, $user_id]);

        // Credit recipient's account
        $update_recipient_sql = "UPDATE accounts SET balance = balance + ? WHERE id = ?";
        $db->query($update_recipient_sql, [$amount, $recipient_id]);
        
        // Create transaction records
        $transaction_description = "Inter-bank transfer to {$recipient_full_name}";

        // Sender debit transaction
        $debit_sql = "INSERT INTO transactions (user_id, transaction_type, amount, currency, description, reference_number, category, status, created_at)
                      VALUES (?, 'debit', ?, ?, ?, ?, 'transfer', 'completed', NOW())";
        $db->query($debit_sql, [$user_id, $amount, $user['currency'], $transaction_description, $transfer_reference]);

        // Recipient credit transaction
        $credit_description = "Inter-bank transfer from {$user['first_name']} {$user['last_name']}";
        $credit_sql = "INSERT INTO transactions (user_id, transaction_type, amount, currency, description, reference_number, category, status, created_at)
                       VALUES (?, 'credit', ?, ?, ?, ?, 'transfer', 'completed', NOW())";
        $db->query($credit_sql, [$recipient_id, $amount, $user['currency'], $credit_description, $transfer_reference]);

        // Commit transaction
        $db->commit();

        // Set success data for modal
        $success_data = [
            'success' => true,
            'message' => 'Inter-bank transfer completed successfully',
            'transfer_id' => $transfer_id,
            'transaction_id' => $transfer_reference,
            'amount' => $amount,
            'currency' => $user['currency'],
            'fee' => $transfer_fee,
            'total_debit' => $total_debit,
            'recipient' => $recipient_full_name,
            'recipient_account' => $beneficiary_account,
            'new_balance' => $available_balance - $total_debit,
            'receipt_email_sent' => false // Can be updated later if email is implemented
        ];

    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }

} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// Return JSON response for modal
header('Content-Type: application/json');
if (isset($success_data)) {
    echo json_encode($success_data);
} else {
    http_response_code(400);
    echo json_encode(['error' => $error_message]);
}
?>
