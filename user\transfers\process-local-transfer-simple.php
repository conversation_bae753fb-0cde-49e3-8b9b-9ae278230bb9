<?php
/**
 * Simple Local Bank Transfer Processing
 * Direct MySQLi connection like other pages in the application
 */

// Start session and check authentication
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    echo "Error: Please login first";
    exit();
}

// Include database connection
require_once '../../config/config.php';

// Get form data
$source_account = $_POST['source_account'] ?? 'main';
$beneficiary_account = $_POST['beneficiary_account'] ?? '';
$beneficiary_name = $_POST['beneficiary_name'] ?? '';
$beneficiary_bank = $_POST['beneficiary_bank'] ?? '';
$routing_code = $_POST['routing_code'] ?? '';
$account_type = $_POST['account_type'] ?? '';
$amount = floatval($_POST['amount'] ?? 0);
$narration = $_POST['narration'] ?? 'Local Bank Transfer';
$otp_code = $_POST['otp_code'] ?? '';

$user_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

try {
    // Get database connection
    $db = getDB();
    
    // Get user information
    $user_query = "SELECT id, account_number, first_name, last_name, balance, currency FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
    
    if (!$user) {
        throw new Exception('User account not found');
    }

    // Validate required fields
    if (empty($beneficiary_account) || empty($beneficiary_name) || empty($beneficiary_bank) || $amount <= 0) {
        throw new Exception('Missing required transfer information');
    }
    
    // Validate amount
    if ($amount < 1) {
        throw new Exception('Minimum transfer amount is $1.00');
    }
    
    if ($amount > 50000) {
        throw new Exception('Maximum transfer amount is $50,000.00');
    }
    
    // Validate account number format
    if (!preg_match('/^[0-9]{8,20}$/', $beneficiary_account)) {
        throw new Exception('Invalid beneficiary account number format');
    }
    
    // Check if OTP is required for local transfers
    $otp_required = false;
    $otp_settings_query = "SELECT local_transfer_otp FROM admin_otp_settings WHERE id = 1";
    $otp_settings_result = $db->query($otp_settings_query);
    if ($otp_settings_result && $otp_settings_result->num_rows > 0) {
        $otp_settings = $otp_settings_result->fetch_assoc();
        $otp_required = (bool)$otp_settings['local_transfer_otp'];
    }

    if ($otp_required) {
        // Verify OTP for local bank transfers
        if (empty($otp_code)) {
            throw new Exception('OTP verification is required for local bank transfers');
        }
        
        $verify_otp_sql = "SELECT * FROM user_otps
                          WHERE user_id = ? AND otp_code = ?
                          AND (purpose = 'transfer' OR source = 'transfer')
                          AND expires_at > NOW()
                          AND (is_used = 0 OR used = 0)";
        $otp_result = $db->query($verify_otp_sql, [$user_id, $otp_code]);

        if ($otp_result->num_rows === 0) {
            throw new Exception('Invalid or expired verification code');
        }

        // Mark OTP as used
        $mark_otp_used_sql = "UPDATE user_otps SET is_used = 1, used = 1, used_at = NOW() WHERE user_id = ? AND otp_code = ?";
        $db->query($mark_otp_used_sql, [$user_id, $otp_code]);
    }
    
    // Check balance
    $available_balance = floatval($user['balance']);
    
    // Calculate fees for local bank transfers
    $transfer_fee = max(2.50, $amount * 0.001); // $2.50 or 0.1%, whichever is higher
    $total_debit = $amount + $transfer_fee;
    
    // Check sufficient balance
    if ($total_debit > $available_balance) {
        throw new Exception('Insufficient balance for this transfer');
    }
    
    // Start database transaction
    $db->beginTransaction();

    try {
        // Generate unique transfer reference
        $transfer_reference = 'LBT' . date('Ymd') . sprintf('%06d', mt_rand(100000, 999999));

        // Insert transfer record into LOCAL TRANSFERS table
        $insert_transfer_sql = "INSERT INTO local_transfers (
            transaction_id, sender_id, sender_account_type,
            beneficiary_account_number, beneficiary_account_name, beneficiary_bank_name,
            routing_code, account_type, amount, currency, transfer_fee,
            narration, status, otp_verified
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        // Determine if OTP was actually verified
        $otp_was_verified = $otp_required && !empty($otp_code) ? 1 : 0;

        $transfer_id = $db->insert($insert_transfer_sql, [
            $transfer_reference,    // transaction_id
            $user_id,              // sender_id
            $source_account,       // sender_account_type
            $beneficiary_account,  // beneficiary_account_number
            $beneficiary_name,     // beneficiary_account_name
            $beneficiary_bank,     // beneficiary_bank_name
            $routing_code ?: null, // routing_code (null if empty)
            $account_type ?: null, // account_type (null if empty)
            $amount,               // amount
            $user['currency'],     // currency
            $transfer_fee,         // transfer_fee
            $narration ?: null,    // narration (null if empty)
            'completed',           // status
            $otp_was_verified      // otp_verified (1 or 0)
        ]);
        
        // Debit sender's account
        $update_balance_sql = "UPDATE accounts SET balance = balance - ? WHERE id = ?";
        $db->query($update_balance_sql, [$total_debit, $user_id]);
        
        // Create transaction records
        $transaction_description = "Local bank transfer to {$beneficiary_name} at {$beneficiary_bank}";
        
        // Debit transaction
        $debit_sql = "INSERT INTO transactions (user_id, transaction_type, amount, currency, description, reference_number, category, status, created_at)
                      VALUES (?, 'debit', ?, ?, ?, ?, 'transfer', 'completed', NOW())";
        $db->query($debit_sql, [$user_id, $amount, $user['currency'], $transaction_description, $transfer_reference]);
        
        // Fee transaction
        if ($transfer_fee > 0) {
            $fee_sql = "INSERT INTO transactions (user_id, transaction_type, amount, currency, description, reference_number, category, status, created_at)
                        VALUES (?, 'debit', ?, ?, ?, ?, 'fee', 'completed', NOW())";
            $db->query($fee_sql, [$user_id, $transfer_fee, $user['currency'], "Transfer fee for {$transfer_reference}", $transfer_reference]);
        }
        
        // Commit transaction
        $db->commit();
        
        // Set success message
        $success_message = "Local bank transfer completed successfully! Reference: {$transfer_reference}";
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// Simple response - no JSON
if ($success_message) {
    echo "SUCCESS: " . $success_message;
} else {
    echo "ERROR: " . $error_message;
}
?>
