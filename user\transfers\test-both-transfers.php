<?php
/**
 * Test Both Transfer Types
 * NO SESSION REQUIRED - DEBUG TOOL
 */

session_start();
require_once '../../config/config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Transfer Types Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body class="container py-4">
    <h1>🧪 Transfer Types Test & Fix Verification</h1>

    <div class="test-section">
        <h2>🔧 Fixes Applied</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>✅ Local Transfer Fixes:</h4>
                <ul>
                    <li>Fixed PDO/MySQLi mixing</li>
                    <li>Proper prepared statements</li>
                    <li>Fixed database method calls</li>
                    <li>Better error handling</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4>✅ Inter-Bank Transfer Fixes:</h4>
                <ul>
                    <li>Dedicated JavaScript module</li>
                    <li>Separate processing endpoint</li>
                    <li>Proper delegation from main JS</li>
                    <li>Fixed empty response issues</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Database Structure Test</h2>
        <?php
        try {
            $db = getDB();
            
            // Test local_transfers table
            echo "<h4>Local Transfers Table:</h4>";
            $local_count = $db->query("SELECT COUNT(*) as count FROM local_transfers")->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p class='success'>✅ local_transfers table exists with $local_count records</p>";
            
            // Test interbank_transfers table
            echo "<h4>Inter-Bank Transfers Table:</h4>";
            $interbank_count = $db->query("SELECT COUNT(*) as count FROM interbank_transfers")->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p class='success'>✅ interbank_transfers table exists with $interbank_count records</p>";
            
            // Test accounts table
            echo "<h4>Accounts Table:</h4>";
            $accounts_count = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0")->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p class='success'>✅ accounts table has $accounts_count user accounts</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Database test failed: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🔗 File Integration Test</h2>
        <?php
        $files_to_check = [
            'transfers.js' => 'Main transfer logic',
            'interbank-transfers.js' => 'Inter-bank specific logic',
            'process-local-transfer.php' => 'Local transfer processor',
            'process-interbank-transfer-v2.php' => 'Inter-bank transfer processor'
        ];
        
        foreach ($files_to_check as $file => $description) {
            if (file_exists($file)) {
                $size = filesize($file);
                echo "<p class='success'>✅ $file ($description) - $size bytes</p>";
            } else {
                echo "<p class='error'>❌ $file missing</p>";
            }
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🧪 Simulated Transfer Tests</h2>
        
        <?php if (isset($_SESSION['user_id'])): ?>
            <div class="alert alert-info">
                <strong>User Session Active:</strong> <?php echo $_SESSION['username'] ?? 'Unknown'; ?>
                <br><strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>🏦 Test Local Transfer</h4>
                    <button class="btn btn-primary" onclick="testLocalTransfer()">Test Local Transfer API</button>
                    <div id="local-test-result" class="mt-2"></div>
                </div>
                
                <div class="col-md-6">
                    <h4>🔄 Test Inter-Bank Transfer</h4>
                    <button class="btn btn-success" onclick="testInterbankTransfer()">Test Inter-Bank Transfer API</button>
                    <div id="interbank-test-result" class="mt-2"></div>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-warning">
                <strong>No User Session:</strong> Please login as a user to test transfer APIs.
                <br><a href="../login.php" class="btn btn-primary mt-2">Login as User</a>
            </div>
        <?php endif; ?>
    </div>

    <div class="test-section">
        <h2>📋 Manual Testing Instructions</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>Local Bank Transfer Test:</h4>
                <ol>
                    <li>Go to <a href="index.php">transfers page</a></li>
                    <li>Select "Local Bank Transfer"</li>
                    <li>Fill in external bank details</li>
                    <li>Submit transfer</li>
                    <li><strong>Expected:</strong> No 400 error, successful processing</li>
                </ol>
            </div>
            
            <div class="col-md-6">
                <h4>Inter-Bank Transfer Test:</h4>
                <ol>
                    <li>Select "Inter-Bank Transfer"</li>
                    <li>Enter internal account number</li>
                    <li>Fill in recipient details</li>
                    <li>Submit transfer</li>
                    <li><strong>Expected:</strong> No empty response, successful processing</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 Quick Links</h2>
        <a href="index.php" class="btn btn-primary me-2">🚀 Transfers Page</a>
        <a href="../../admin/transfers.php" class="btn btn-secondary me-2">👨‍💼 Admin Transfers</a>
        <a href="test-transfer-processors.php" class="btn btn-info">🔧 Processor Diagnostics</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testLocalTransfer() {
            const resultDiv = document.getElementById('local-test-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing...';
            
            const testData = {
                source_account: 'main',
                beneficiary_account: '**********',
                beneficiary_name: 'Test External Bank',
                beneficiary_bank: 'External Bank Ltd',
                routing_code: '*********',
                account_type: 'checking',
                amount: 10.00,
                narration: 'Test Local Transfer',
                currency: 'USD'
            };
            
            fetch('process-local-transfer.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ Local transfer test successful!</div>';
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-warning">⚠️ ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">❌ ${error.message}</div>`;
            });
        }
        
        function testInterbankTransfer() {
            const resultDiv = document.getElementById('interbank-test-result');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing...';
            
            const testData = {
                source_account: 'main',
                beneficiary_account: '**********',
                beneficiary_name: 'Test Internal User',
                amount: 5.00,
                narration: 'Test Inter-Bank Transfer',
                currency: 'USD'
            };
            
            fetch('process-interbank-transfer-v2.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ Inter-bank transfer test successful!</div>';
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-warning">⚠️ ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">❌ ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
