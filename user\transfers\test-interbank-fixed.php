<?php
/**
 * Test Inter-Bank Transfer - Fixed Account Validation
 * NO SESSION REQUIRED - DEBUG TOOL
 */

session_start();
require_once '../../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>Please login first</p>";
    echo "<a href='../login.php'>Login</a>";
    exit();
}

$user_id = $_SESSION['user_id'];

// Get user info
try {
    $db = getDB();
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    exit();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Inter-Bank Transfer - Fixed Validation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="container py-4">
    <h1>🏦 Inter-Bank Transfer - Fixed Account Validation</h1>
    
    <div class="alert alert-success">
        <h4>✅ VALIDATION FIXED</h4>
        <p><strong>Problem:</strong> Account validation was checking for exactly 10 digits</p>
        <p><strong>Solution:</strong> Updated to accept 10-12 digits (actual account format)</p>
        <p><strong>Test Account:</strong> ************ (12 digits) should now work</p>
    </div>

    <div class="alert alert-info">
        <strong>Logged in as:</strong> <?php echo $user['first_name'] . ' ' . $user['last_name']; ?><br>
        <strong>Account:</strong> <?php echo $user['account_number']; ?><br>
        <strong>Balance:</strong> $<?php echo number_format($user['balance'], 2); ?>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3><i class="fas fa-exchange-alt me-2"></i>Test Inter-Bank Transfer</h3>
        </div>
        <div class="card-body">
            <form id="interbankForm">
                <div class="mb-3">
                    <label class="form-label">Source Account</label>
                    <select name="source_account" id="sourceAccount" class="form-control">
                        <option value="main">Main Account</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Beneficiary Account Number *</label>
                    <input type="text" name="beneficiary_account" id="beneficiaryAccount" class="form-control" 
                           placeholder="************" value="************" required>
                    <small class="text-muted">Enter 10-12 digit account number of another user</small>
                    <div id="accountValidation" class="mt-2"></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Beneficiary Name *</label>
                    <input type="text" name="beneficiary_name" id="beneficiaryName" class="form-control" 
                           placeholder="Demo User" value="Demo User" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Amount *</label>
                    <input type="number" name="amount" id="transferAmount" class="form-control" 
                           min="1" max="1000" step="0.01" value="25.00" required>
                    <small class="text-muted">Minimum: $1.00, Maximum: $1,000.00</small>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Narration</label>
                    <input type="text" name="narration" id="transferNarration" class="form-control" 
                           value="Test Inter-Bank Transfer - Fixed Validation" placeholder="Transfer description">
                </div>
                
                <button type="button" class="btn btn-primary btn-lg" onclick="testInterbankTransfer()">
                    <i class="fas fa-paper-plane me-2"></i>Submit Inter-Bank Transfer
                </button>
            </form>
            
            <div id="transferResult" class="mt-4"></div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h3><i class="fas fa-cogs me-2"></i>Validation Tests</h3>
        </div>
        <div class="card-body">
            <h5>Test Different Account Number Formats:</h5>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-outline-primary btn-sm" onclick="testAccountFormat('**********')">
                        Test 10 digits: **********
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-success btn-sm" onclick="testAccountFormat('**********1')">
                        Test 11 digits: **********1
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-success btn-sm" onclick="testAccountFormat('************')">
                        Test 12 digits: ************
                    </button>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-4">
                    <button class="btn btn-outline-danger btn-sm" onclick="testAccountFormat('*********')">
                        Test 9 digits: ********* (should fail)
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-danger btn-sm" onclick="testAccountFormat('*************')">
                        Test 13 digits: ************* (should fail)
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-danger btn-sm" onclick="testAccountFormat('abc**********')">
                        Test letters: abc********** (should fail)
                    </button>
                </div>
            </div>
            <div id="validationResult" class="mt-3"></div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header bg-secondary text-white">
            <h3><i class="fas fa-history me-2"></i>Recent Inter-Bank Transfers</h3>
        </div>
        <div class="card-body">
            <?php
            try {
                $transfers_query = "SELECT * FROM interbank_transfers WHERE sender_id = ? ORDER BY created_at DESC LIMIT 5";
                $transfers_result = $db->query($transfers_query, [$user_id]);
                
                if ($transfers_result->num_rows > 0) {
                    echo "<table class='table table-sm'>";
                    echo "<tr><th>Reference</th><th>Amount</th><th>Status</th><th>Date</th></tr>";
                    while ($transfer = $transfers_result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($transfer['transaction_id']) . "</td>";
                        echo "<td>$" . number_format($transfer['amount'], 2) . "</td>";
                        echo "<td><span class='badge bg-success'>" . $transfer['status'] . "</span></td>";
                        echo "<td>" . date('M j, Y H:i', strtotime($transfer['created_at'])) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p class='text-muted'>No inter-bank transfers found</p>";
                }
            } catch (Exception $e) {
                echo "<p class='text-danger'>Error loading transfers: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
    </div>

    <div class="mt-4">
        <a href="index.php" class="btn btn-secondary">← Back to Transfers</a>
        <a href="final-transfer-test.php" class="btn btn-info">Final Test Page</a>
        <a href="../../admin/transfers.php" class="btn btn-success">Admin Transfers</a>
    </div>

    <script>
        function testInterbankTransfer() {
            const resultDiv = document.getElementById('transferResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Processing inter-bank transfer...';
            
            // Create form data
            const formData = new FormData();
            formData.append('source_account', document.getElementById('sourceAccount').value);
            formData.append('beneficiary_account', document.getElementById('beneficiaryAccount').value);
            formData.append('beneficiary_name', document.getElementById('beneficiaryName').value);
            formData.append('amount', document.getElementById('transferAmount').value);
            formData.append('narration', document.getElementById('transferNarration').value);

            fetch('process-interbank-transfer-v2.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                console.log('Response:', data);
                if (data.startsWith('SUCCESS:')) {
                    resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>' + data + '</div>';
                    // Refresh the page after 2 seconds to show updated transfer history
                    setTimeout(() => location.reload(), 2000);
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>' + data + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>Error: ' + error.message + '</div>';
            });
        }
        
        function testAccountFormat(accountNumber) {
            const resultDiv = document.getElementById('validationResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Testing account format: ' + accountNumber;
            
            // Test the regex pattern
            const isValid = /^\d{10,12}$/.test(accountNumber);
            const length = accountNumber.length;
            
            setTimeout(() => {
                if (isValid) {
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ <strong>' + accountNumber + '</strong> (' + length + ' digits) - VALID format</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">❌ <strong>' + accountNumber + '</strong> (' + length + ' digits) - INVALID format</div>';
                }
            }, 500);
        }
        
        // Auto-validate account number on input
        document.getElementById('beneficiaryAccount').addEventListener('input', function() {
            const accountNumber = this.value.trim();
            const validationDiv = document.getElementById('accountValidation');
            
            if (accountNumber.length === 0) {
                validationDiv.innerHTML = '';
                return;
            }
            
            if (/^\d{10,12}$/.test(accountNumber)) {
                validationDiv.innerHTML = '<small class="text-success"><i class="fas fa-check me-1"></i>Valid account format (' + accountNumber.length + ' digits)</small>';
            } else {
                validationDiv.innerHTML = '<small class="text-danger"><i class="fas fa-times me-1"></i>Invalid format (must be 10-12 digits)</small>';
            }
        });
    </script>
</body>
</html>
