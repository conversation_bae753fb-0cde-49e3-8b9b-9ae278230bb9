<?php
/**
 * Test Inter-Bank Transfer with Success Modal
 * NO SESSION REQUIRED - DEBUG TOOL
 */

session_start();
require_once '../../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>Please login first</p>";
    echo "<a href='../login.php'>Login</a>";
    exit();
}

$user_id = $_SESSION['user_id'];

// Get user info
try {
    $db = getDB();
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    exit();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Inter-Bank Transfer with Success Modal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="container py-4">
    <h1>🏦 Test Inter-Bank Transfer with Success Modal</h1>
    
    <div class="alert alert-success">
        <h4>✅ MODAL FUNCTIONALITY RESTORED</h4>
        <p><strong>Fixed:</strong> Success modal with receipt and proper navigation</p>
        <p><strong>Features:</strong> JSON response, success card, redirect to /user/transfers/</p>
    </div>

    <div class="alert alert-info">
        <strong>Logged in as:</strong> <?php echo $user['first_name'] . ' ' . $user['last_name']; ?><br>
        <strong>Account:</strong> <?php echo $user['account_number']; ?><br>
        <strong>Balance:</strong> $<?php echo number_format($user['balance'], 2); ?>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3><i class="fas fa-exchange-alt me-2"></i>Test Inter-Bank Transfer</h3>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Beneficiary Account Number</label>
                <input type="text" id="beneficiaryAccount" class="form-control" 
                       value="************" placeholder="Enter account number">
            </div>
            
            <div class="mb-3">
                <label class="form-label">Beneficiary Name</label>
                <input type="text" id="beneficiaryName" class="form-control" 
                       value="Demo User" placeholder="Enter recipient name">
            </div>
            
            <div class="mb-3">
                <label class="form-label">Amount</label>
                <input type="number" id="transferAmount" class="form-control" 
                       value="15.00" min="1" step="0.01" placeholder="Enter amount">
            </div>
            
            <div class="mb-3">
                <label class="form-label">Narration</label>
                <input type="text" id="transferNarration" class="form-control" 
                       value="Test Inter-Bank Transfer with Modal" placeholder="Transfer description">
            </div>
            
            <input type="hidden" id="sourceAccount" value="main">
            
            <button type="button" class="btn btn-primary btn-lg" onclick="testInterbankWithModal()">
                <i class="fas fa-paper-plane me-2"></i>Submit Transfer (Test Modal)
            </button>
            
            <div id="transferResult" class="mt-4"></div>
        </div>
    </div>

    <!-- Success Modal (copied from main transfers page) -->
    <div class="modal fade" id="successModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center p-4">
                    <div class="success-animation mb-3">
                        <i class="fas fa-check-circle fa-4x text-success"></i>
                    </div>
                    <h4 class="text-success mb-3">Transfer Successful!</h4>
                    <p class="text-muted mb-3" id="successMessage">
                        Your transfer has been processed successfully.
                    </p>

                    <!-- Transfer Details -->
                    <div class="transfer-details mb-4">
                        <div class="row text-start">
                            <div class="col-6"><strong>Amount:</strong></div>
                            <div class="col-6" id="successAmount">-</div>
                            
                            <div class="col-6"><strong>Recipient:</strong></div>
                            <div class="col-6" id="successRecipient">-</div>
                            
                            <div class="col-6"><strong>Reference:</strong></div>
                            <div class="col-6" id="successReference">-</div>
                            
                            <div class="col-6"><strong>Fee:</strong></div>
                            <div class="col-6" id="successFee">-</div>
                        </div>
                    </div>

                    <!-- Receipt Options -->
                    <div class="receipt-options mb-4">
                        <h6 class="text-muted mb-3">📄 Receipt Options</h6>
                        <div class="d-flex justify-content-center gap-2 flex-wrap">
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="downloadReceipt()">
                                <i class="fas fa-file-pdf me-1"></i>Download PDF
                            </button>
                            <button type="button" class="btn btn-outline-info btn-sm" onclick="viewReceipt()">
                                <i class="fas fa-eye me-1"></i>View Receipt
                            </button>
                        </div>
                    </div>

                    <div class="success-actions">
                        <button type="button" class="btn btn-primary" onclick="closeSuccessModal()">
                            <i class="fas fa-check me-1"></i>Done
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="index.php" class="btn btn-secondary">← Back to Main Transfers</a>
        <a href="../../admin/transfers.php" class="btn btn-info">Admin Transfers</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize modal
        let successModal = new bootstrap.Modal(document.getElementById('successModal'));
        
        function testInterbankWithModal() {
            const resultDiv = document.getElementById('transferResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Processing inter-bank transfer...';
            
            // Create form data
            const formData = new FormData();
            formData.append('source_account', document.getElementById('sourceAccount').value);
            formData.append('beneficiary_account', document.getElementById('beneficiaryAccount').value);
            formData.append('beneficiary_name', document.getElementById('beneficiaryName').value);
            formData.append('amount', document.getElementById('transferAmount').value);
            formData.append('narration', document.getElementById('transferNarration').value);

            fetch('process-interbank-transfer-v2.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('✅ Inter-bank transfer successful:', data);
                resultDiv.innerHTML = '<div class="alert alert-success">Transfer processed! Opening success modal...</div>';
                
                if (data.success) {
                    showTransferSuccess(data);
                } else {
                    throw new Error(data.error || 'Transfer failed');
                }
            })
            .catch(error => {
                console.error('❌ Inter-bank transfer failed:', error);
                resultDiv.innerHTML = '<div class="alert alert-danger">Transfer failed: ' + error.message + '</div>';
            });
        }
        
        function showTransferSuccess(data) {
            console.log('🎉 Showing success modal with data:', data);
            
            // Update modal content
            document.getElementById('successAmount').textContent = `${data.currency || '$'} ${data.amount.toFixed(2)}`;
            document.getElementById('successRecipient').textContent = data.recipient;
            document.getElementById('successReference').textContent = data.transaction_id;
            document.getElementById('successFee').textContent = 'Free';
            document.getElementById('successMessage').textContent = 
                `Your inter-bank transfer of ${data.currency || '$'} ${data.amount.toFixed(2)} to ${data.recipient} has been processed successfully.`;
            
            // Store receipt data
            window.transferReceiptData = data;
            
            // Show modal
            successModal.show();
        }
        
        function closeSuccessModal() {
            successModal.hide();
            // Redirect back to transfers page
            window.location.href = '/user/transfers/';
        }
        
        function downloadReceipt() {
            alert('Download receipt functionality - would generate PDF with transfer details');
        }
        
        function viewReceipt() {
            alert('View receipt functionality - would show detailed receipt');
        }
    </script>
</body>
</html>
