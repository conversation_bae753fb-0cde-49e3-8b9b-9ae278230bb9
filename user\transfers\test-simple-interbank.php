<?php
/**
 * Simple Inter-Bank Transfer Test
 * Direct form submission test
 */

session_start();
require_once '../../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>Please login first</p>";
    echo "<a href='../login.php'>Login</a>";
    exit();
}

$user_id = $_SESSION['user_id'];

// Get user info
try {
    $db = getDB();
    $user_query = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$user_id]);
    $user = $user_result->fetch_assoc();
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
    exit();
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Inter-Bank Transfer Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="container py-4">
    <h1>🏦 Simple Inter-Bank Transfer Test</h1>
    
    <div class="alert alert-info">
        <strong>Logged in as:</strong> <?php echo $user['first_name'] . ' ' . $user['last_name']; ?><br>
        <strong>Account:</strong> <?php echo $user['account_number']; ?><br>
        <strong>Balance:</strong> $<?php echo number_format($user['balance'], 2); ?>
    </div>

    <div class="card">
        <div class="card-header">
            <h3>Test Inter-Bank Transfer</h3>
        </div>
        <div class="card-body">
            <form action="process-interbank-transfer-v2.php" method="POST">
                <div class="mb-3">
                    <label class="form-label">Source Account</label>
                    <select name="source_account" class="form-control">
                        <option value="main">Main Account</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Beneficiary Account Number</label>
                    <input type="text" name="beneficiary_account" class="form-control" 
                           placeholder="**********" value="**********" required>
                    <small class="text-muted">Enter a 10-digit account number of another user</small>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Beneficiary Name</label>
                    <input type="text" name="beneficiary_name" class="form-control" 
                           placeholder="Recipient Name" value="Test User" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Amount</label>
                    <input type="number" name="amount" class="form-control" 
                           min="1" max="1000" step="0.01" value="10.00" required>
                    <small class="text-muted">Minimum: $1.00, Maximum: $1,000.00</small>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Narration</label>
                    <input type="text" name="narration" class="form-control" 
                           value="Test Inter-Bank Transfer" placeholder="Transfer description">
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>Submit Transfer
                </button>
            </form>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h3>JavaScript Test</h3>
        </div>
        <div class="card-body">
            <p>Test the JavaScript version (like the main transfer page):</p>
            
            <div class="mb-3">
                <input type="text" id="beneficiaryAccount" class="form-control" 
                       placeholder="Beneficiary Account" value="**********">
            </div>
            <div class="mb-3">
                <input type="text" id="beneficiaryName" class="form-control" 
                       placeholder="Beneficiary Name" value="Test User">
            </div>
            <div class="mb-3">
                <input type="number" id="transferAmount" class="form-control" 
                       placeholder="Amount" value="5.00" min="1" step="0.01">
            </div>
            <div class="mb-3">
                <input type="text" id="transferNarration" class="form-control" 
                       placeholder="Narration" value="JS Test Transfer">
            </div>
            <input type="hidden" id="sourceAccount" value="main">
            
            <button type="button" class="btn btn-success" onclick="testJSTransfer()">
                <i class="fas fa-code me-2"></i>Test JavaScript Transfer
            </button>
            
            <div id="jsResult" class="mt-3"></div>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h3>Recent Transfers</h3>
        </div>
        <div class="card-body">
            <?php
            try {
                $transfers_query = "SELECT * FROM interbank_transfers WHERE sender_id = ? ORDER BY created_at DESC LIMIT 5";
                $transfers_result = $db->query($transfers_query, [$user_id]);
                
                if ($transfers_result->num_rows > 0) {
                    echo "<table class='table table-sm'>";
                    echo "<tr><th>Reference</th><th>Amount</th><th>Status</th><th>Date</th></tr>";
                    while ($transfer = $transfers_result->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($transfer['transaction_id']) . "</td>";
                        echo "<td>$" . number_format($transfer['amount'], 2) . "</td>";
                        echo "<td><span class='badge bg-success'>" . $transfer['status'] . "</span></td>";
                        echo "<td>" . date('M j, Y H:i', strtotime($transfer['created_at'])) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p class='text-muted'>No inter-bank transfers found</p>";
                }
            } catch (Exception $e) {
                echo "<p class='text-danger'>Error loading transfers: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
    </div>

    <div class="mt-4">
        <a href="index.php" class="btn btn-secondary">← Back to Transfers</a>
        <a href="../../admin/transfers.php" class="btn btn-info">Admin Transfers</a>
    </div>

    <script>
        function testJSTransfer() {
            const resultDiv = document.getElementById('jsResult');
            resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Processing...';
            
            // Create form data
            const formData = new FormData();
            formData.append('source_account', document.getElementById('sourceAccount').value);
            formData.append('beneficiary_account', document.getElementById('beneficiaryAccount').value);
            formData.append('beneficiary_name', document.getElementById('beneficiaryName').value);
            formData.append('amount', document.getElementById('transferAmount').value);
            formData.append('narration', document.getElementById('transferNarration').value);

            fetch('process-interbank-transfer-v2.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                console.log('Response:', data);
                if (data.startsWith('SUCCESS:')) {
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ ' + data + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">❌ ' + data + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = '<div class="alert alert-danger">❌ Error: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
