<?php
/**
 * Transfer Processors Test & Diagnostic Tool
 * NO SESSION REQUIRED - DEBUG TOOL
 */

session_start();
require_once '../../config/config.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Transfer Processors Test</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .section { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 Transfer Processors Diagnostic</h1>

    <?php
    echo "<div class='section'>";
    echo "<h2>📊 Database Connection Test</h2>";
    try {
        $db = getDB();
        echo "<p class='success'>✅ Database connection successful</p>";
        
        // Test tables exist
        $tables = ['accounts', 'local_transfers', 'interbank_transfers', 'transactions'];
        foreach ($tables as $table) {
            try {
                $result = $db->query("SELECT COUNT(*) as count FROM $table");
                $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
                echo "<p class='success'>✅ Table '$table' exists with $count records</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ Table '$table' error: " . $e->getMessage() . "</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>📁 File Structure Test</h2>";
    $files = [
        'process-local-transfer.php',
        'process-interbank-transfer.php',
        'process-interbank-transfer-v2.php',
        'transfers.js',
        'interbank-transfers.js'
    ];
    
    foreach ($files as $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            echo "<p class='success'>✅ $file exists ($size bytes)</p>";
        } else {
            echo "<p class='error'>❌ $file missing</p>";
        }
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>👤 Session Test</h2>";
    if (isset($_SESSION['user_id'])) {
        echo "<p class='success'>✅ User logged in: " . $_SESSION['user_id'] . "</p>";
        echo "<p class='info'>Username: " . ($_SESSION['username'] ?? 'N/A') . "</p>";
    } else {
        echo "<p class='error'>❌ No user session found</p>";
        echo "<p class='info'>💡 Login as a user to test transfer processing</p>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>🧪 Simulated Transfer Test</h2>";
    
    if (isset($_SESSION['user_id'])) {
        try {
            $user_id = $_SESSION['user_id'];
            
            // Get user info
            $user_query = "SELECT * FROM accounts WHERE id = ?";
            $user_stmt = $db->prepare($user_query);
            $user_stmt->execute([$user_id]);
            $user = $user_stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "<p class='success'>✅ User found: " . $user['first_name'] . " " . $user['last_name'] . "</p>";
                echo "<p class='info'>Account: " . $user['account_number'] . "</p>";
                echo "<p class='info'>Balance: $" . number_format($user['balance'], 2) . "</p>";
                
                // Test data for simulation
                $test_data = [
                    'local' => [
                        'source_account' => 'main',
                        'beneficiary_account' => '**********',
                        'beneficiary_name' => 'Test External Bank',
                        'amount' => 100.00,
                        'narration' => 'Test Local Transfer',
                        'currency' => 'USD'
                    ],
                    'interbank' => [
                        'source_account' => 'main',
                        'beneficiary_account' => '**********', // Assuming another internal account
                        'beneficiary_name' => 'Test Internal User',
                        'amount' => 50.00,
                        'narration' => 'Test Inter-Bank Transfer',
                        'currency' => 'USD'
                    ]
                ];
                
                echo "<h3>📋 Test Data Prepared:</h3>";
                echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>";
                
                // Test validation logic
                echo "<h3>🔍 Validation Tests:</h3>";
                
                // Test 1: Amount validation
                if ($test_data['local']['amount'] >= 1 && $test_data['local']['amount'] <= 50000) {
                    echo "<p class='success'>✅ Amount validation passed</p>";
                } else {
                    echo "<p class='error'>❌ Amount validation failed</p>";
                }
                
                // Test 2: Balance check
                if ($user['balance'] >= $test_data['local']['amount']) {
                    echo "<p class='success'>✅ Sufficient balance for test</p>";
                } else {
                    echo "<p class='error'>❌ Insufficient balance for test</p>";
                }
                
                // Test 3: Account format
                if (preg_match('/^\d{10}$/', $test_data['local']['beneficiary_account'])) {
                    echo "<p class='success'>✅ Account format validation passed</p>";
                } else {
                    echo "<p class='error'>❌ Account format validation failed</p>";
                }
                
            } else {
                echo "<p class='error'>❌ User not found in database</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Simulation error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='info'>⚠️ Login required for simulation</p>";
    }
    echo "</div>";

    echo "<div class='section'>";
    echo "<h2>🔧 JavaScript Integration Test</h2>";
    ?>
    <p class='info'>Testing JavaScript file loading...</p>
    <div id="js-test-results"></div>
    
    <script>
        // Test if transfer functions are available
        const results = document.getElementById('js-test-results');
        let testResults = [];
        
        // Test main transfers.js
        if (typeof processTransfer === 'function') {
            testResults.push('<p class="success">✅ processTransfer function loaded</p>');
        } else {
            testResults.push('<p class="error">❌ processTransfer function missing</p>');
        }
        
        // Test inter-bank transfers.js
        if (typeof window.InterbankTransfers === 'object') {
            testResults.push('<p class="success">✅ InterbankTransfers module loaded</p>');
        } else {
            testResults.push('<p class="error">❌ InterbankTransfers module missing</p>');
        }
        
        // Test global variables
        if (typeof currentTransferType !== 'undefined') {
            testResults.push('<p class="success">✅ currentTransferType variable available</p>');
        } else {
            testResults.push('<p class="error">❌ currentTransferType variable missing</p>');
        }
        
        results.innerHTML = testResults.join('');
    </script>
    
    <h3>📝 Manual Test Instructions:</h3>
    <ol>
        <li><strong>Local Transfer Test:</strong>
            <ul>
                <li>Go to transfers page</li>
                <li>Select "Local Bank Transfer"</li>
                <li>Enter external account details</li>
                <li>Submit and check for 400 errors</li>
            </ul>
        </li>
        <li><strong>Inter-Bank Transfer Test:</strong>
            <ul>
                <li>Select "Inter-Bank Transfer"</li>
                <li>Enter internal account details</li>
                <li>Submit and check for empty response errors</li>
            </ul>
        </li>
    </ol>
    
    <h3>🔗 Quick Links:</h3>
    <a href="index.php" style="background: #007bff; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;">🚀 Go to Transfers Page</a>
    <a href="../../admin/transfers.php" style="background: #28a745; color: white; padding: 10px; text-decoration: none; margin: 5px; display: inline-block;">👨‍💼 Admin Transfers</a>
    
    <?php echo "</div>"; ?>
</body>
</html>
