<?php
/**
 * Wire Transfer Beneficiaries Management
 * Allows users to manage their saved wire transfer beneficiaries
 */

// Start session and check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Include required files
require_once '../../config/config.php';
require_once '../../config/dynamic-css.php';

$db = getDB();
$user_id = $_SESSION['user_id'];

// Handle form submissions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'add':
                // Add new wire beneficiary
                $name = trim($_POST['name']);
                $beneficiary_country = trim($_POST['beneficiary_country']);
                $beneficiary_account_number = trim($_POST['beneficiary_account_number']);
                $beneficiary_account_name = trim($_POST['beneficiary_account_name']);
                $beneficiary_address = trim($_POST['beneficiary_address']);
                $bank_name = trim($_POST['bank_name']);
                $bank_address = trim($_POST['bank_address']);
                $bank_city = trim($_POST['bank_city']);
                $bank_country = trim($_POST['bank_country']);
                $swift_code = trim($_POST['swift_code']);
                $routing_code = trim($_POST['routing_code']) ?: null;
                $iban = trim($_POST['iban']) ?: null;
                $is_favorite = isset($_POST['is_favorite']) ? 1 : 0;

                // Validation
                if (empty($name) || empty($beneficiary_country) || empty($beneficiary_account_number) ||
                    empty($beneficiary_account_name) || empty($beneficiary_address) || empty($bank_name) ||
                    empty($bank_address) || empty($bank_city) || empty($bank_country) || empty($swift_code)) {
                    throw new Exception('Please fill in all required fields.');
                }

                // Check if beneficiary already exists
                $check_sql = "SELECT id FROM wire_beneficiaries WHERE user_id = ? AND beneficiary_account_number = ?";
                $check_result = $db->query($check_sql, [$user_id, $beneficiary_account_number]);

                if ($check_result->num_rows > 0) {
                    throw new Exception('A beneficiary with this account number already exists.');
                }

                // Insert new beneficiary
                $insert_sql = "INSERT INTO wire_beneficiaries (
                    user_id, name, beneficiary_country, beneficiary_account_number,
                    beneficiary_account_name, beneficiary_address, bank_name, bank_address,
                    bank_city, bank_country, swift_code, routing_code, iban, is_favorite
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $db->query($insert_sql, [
                    $user_id, $name, $beneficiary_country, $beneficiary_account_number,
                    $beneficiary_account_name, $beneficiary_address, $bank_name, $bank_address,
                    $bank_city, $bank_country, $swift_code, $routing_code, $iban, $is_favorite
                ]);

                $success_message = 'Wire beneficiary added successfully!';
                break;

            case 'edit':
                // Edit existing wire beneficiary
                $beneficiary_id = (int)$_POST['beneficiary_id'];
                $name = trim($_POST['name']);
                $beneficiary_country = trim($_POST['beneficiary_country']);
                $beneficiary_account_number = trim($_POST['beneficiary_account_number']);
                $beneficiary_account_name = trim($_POST['beneficiary_account_name']);
                $beneficiary_address = trim($_POST['beneficiary_address']);
                $bank_name = trim($_POST['bank_name']);
                $bank_address = trim($_POST['bank_address']);
                $bank_city = trim($_POST['bank_city']);
                $bank_country = trim($_POST['bank_country']);
                $swift_code = trim($_POST['swift_code']);
                $routing_code = trim($_POST['routing_code']) ?: null;
                $iban = trim($_POST['iban']) ?: null;
                $is_favorite = isset($_POST['is_favorite']) ? 1 : 0;

                // Validation
                if (empty($name) || empty($beneficiary_country) || empty($beneficiary_account_number) ||
                    empty($beneficiary_account_name) || empty($beneficiary_address) || empty($bank_name) ||
                    empty($bank_address) || empty($bank_city) || empty($bank_country) || empty($swift_code)) {
                    throw new Exception('Please fill in all required fields.');
                }

                // Verify ownership
                $verify_sql = "SELECT id FROM wire_beneficiaries WHERE id = ? AND user_id = ?";
                $verify_result = $db->query($verify_sql, [$beneficiary_id, $user_id]);

                if ($verify_result->num_rows === 0) {
                    throw new Exception('Beneficiary not found or access denied.');
                }

                // Check if account number conflicts with other beneficiaries (excluding current one)
                $check_sql = "SELECT id FROM wire_beneficiaries WHERE user_id = ? AND beneficiary_account_number = ? AND id != ?";
                $check_result = $db->query($check_sql, [$user_id, $beneficiary_account_number, $beneficiary_id]);

                if ($check_result->num_rows > 0) {
                    throw new Exception('A beneficiary with this account number already exists.');
                }

                // Update beneficiary
                $update_sql = "UPDATE wire_beneficiaries SET
                    name = ?, beneficiary_country = ?, beneficiary_account_number = ?,
                    beneficiary_account_name = ?, beneficiary_address = ?, bank_name = ?,
                    bank_address = ?, bank_city = ?, bank_country = ?, swift_code = ?,
                    routing_code = ?, iban = ?, is_favorite = ?
                    WHERE id = ? AND user_id = ?";

                $db->query($update_sql, [
                    $name, $beneficiary_country, $beneficiary_account_number,
                    $beneficiary_account_name, $beneficiary_address, $bank_name, $bank_address,
                    $bank_city, $bank_country, $swift_code, $routing_code, $iban, $is_favorite,
                    $beneficiary_id, $user_id
                ]);

                $success_message = 'Wire beneficiary updated successfully!';
                break;

            case 'toggle_favorite':
                $beneficiary_id = (int)$_POST['beneficiary_id'];
                $is_favorite = (int)$_POST['is_favorite'];

                // Verify ownership
                $verify_sql = "SELECT id FROM wire_beneficiaries WHERE id = ? AND user_id = ?";
                $verify_result = $db->query($verify_sql, [$beneficiary_id, $user_id]);

                if ($verify_result->num_rows === 0) {
                    throw new Exception('Beneficiary not found or access denied.');
                }

                // Update favorite status
                $update_sql = "UPDATE wire_beneficiaries SET is_favorite = ? WHERE id = ? AND user_id = ?";
                $db->query($update_sql, [$is_favorite, $beneficiary_id, $user_id]);

                $success_message = $is_favorite ? 'Added to favorites!' : 'Removed from favorites!';
                break;

            case 'delete_beneficiary':
                $beneficiary_id = (int)$_POST['beneficiary_id'];

                // Verify ownership
                $verify_sql = "SELECT id FROM wire_beneficiaries WHERE id = ? AND user_id = ?";
                $verify_result = $db->query($verify_sql, [$beneficiary_id, $user_id]);

                if ($verify_result->num_rows === 0) {
                    throw new Exception('Beneficiary not found or access denied.');
                }

                // Delete beneficiary
                $delete_sql = "DELETE FROM wire_beneficiaries WHERE id = ? AND user_id = ?";
                $db->query($delete_sql, [$beneficiary_id, $user_id]);

                $success_message = 'Wire beneficiary deleted successfully!';
                break;
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's wire beneficiaries and statistics
try {
    // Get all wire beneficiaries
    $beneficiaries_sql = "SELECT * FROM wire_beneficiaries WHERE user_id = ? ORDER BY is_favorite DESC, name ASC";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    $beneficiaries = [];
    while ($row = $beneficiaries_result->fetch_assoc()) {
        $beneficiaries[] = $row;
    }

    // Get statistics
    $stats_sql = "SELECT
                    COUNT(*) as total_beneficiaries,
                    COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_count,
                    COUNT(DISTINCT bank_country) as countries_count
                  FROM wire_beneficiaries WHERE user_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $stats = $stats_result->fetch_assoc();

} catch (Exception $e) {
    $error_message = "Error loading beneficiaries: " . $e->getMessage();
    $beneficiaries = [];
    $stats = ['total_beneficiaries' => 0, 'favorite_count' => 0, 'countries_count' => 0];
}

// Set page title and subtitle
$page_title = 'Wire Transfer Beneficiaries';
$page_subtitle = 'Manage your saved recipients for international wire transfers';

// Include header
require_once '../shared/header.php';
?>

<!-- Include Beneficiaries CSS -->
<link rel="stylesheet" href="../beneficiaries/beneficiaries.css">
<!-- Include Wire Beneficiaries CSS -->
<link rel="stylesheet" href="wire-beneficiaries.css">

<!-- Dynamic CSS Variables Only -->
<style>
    <?php echo getInlineDynamicCSS(); ?>
</style>

<!-- Sidebar -->
<?php require_once '../shared/sidebar.php'; ?>

<!-- Main Content Wrapper -->
<div class="main-content-wrapper">
    <!-- User Header Component -->
    <?php require_once '../shared/user_header.php'; ?>

    <div class="main-content" style="min-height: calc(100vh - 200px); display: flex; flex-direction: column;">

        <!-- Content Container -->
        <div class="content-container" style="flex: 1;">

        <!-- Mini Hero Section -->
        <div class="beneficiaries-hero mb-4">
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-title">Wire Transfer Beneficiaries</div>
                    <div class="hero-subtitle">Manage your saved recipients for international wire transfers</div>
                    <div class="hero-stats">
                        Total Beneficiaries: <?php echo number_format($stats['total_beneficiaries']); ?> |
                        Favorites: <?php echo number_format($stats['favorite_count']); ?>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="btn btn-primary" onclick="showAddBeneficiaryModal()">
                        <i class="fas fa-plus me-2"></i>Add Beneficiary
                    </button>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Stats Cards Section -->
        <div class="balance-overview-new mb-4">
            <!-- Total Beneficiaries Card -->
            <div class="balance-card-new">
                <div class="balance-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Total</div>
                    <div class="balance-amount available">
                        <?php echo number_format($stats['total_beneficiaries']); ?>
                    </div>
                    <div class="balance-subtitle">Wire Beneficiaries</div>
                </div>
            </div>

            <!-- Favorites Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                    <i class="fas fa-star"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Favorites</div>
                    <div class="balance-amount" style="color: #f59e0b;">
                        <?php echo number_format($stats['favorite_count']); ?>
                    </div>
                    <div class="balance-subtitle">Quick Access</div>
                </div>
            </div>

            <!-- Countries Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                    <i class="fas fa-globe"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Countries</div>
                    <div class="balance-amount" style="color: #10b981;">
                        <?php echo number_format($stats['countries_count']); ?>
                    </div>
                    <div class="balance-subtitle">Global Reach</div>
                </div>
            </div>

            <!-- Quick Wire Transfer Card -->
            <div class="balance-card-new">
                <div class="balance-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="balance-info">
                    <div class="balance-label">Wire Transfer</div>
                    <div class="balance-amount" style="color: #8b5cf6;">
                        <a href="../wire-transfers/" style="color: inherit; text-decoration: none;">Go</a>
                    </div>
                    <div class="balance-subtitle">Send Money</div>
                </div>
            </div>
        </div>


        <!-- Beneficiaries List Section -->
        <div class="beneficiaries-section">
            <div class="beneficiaries-header">
                <h3><i class="fas fa-globe me-2"></i>Your Wire Transfer Beneficiaries</h3>
                <div class="beneficiaries-summary">
                    Showing <?php echo number_format(count($beneficiaries)); ?> wire beneficiaries
                </div>
            </div>

            <?php if (!empty($beneficiaries)): ?>
            <div class="beneficiaries-grid">
                <?php foreach ($beneficiaries as $beneficiary): ?>
                <div class="beneficiary-card">
                    <div class="beneficiary-header">
                        <div class="beneficiary-info">
                            <div class="beneficiary-name">
                                <?php echo htmlspecialchars($beneficiary['name']); ?>
                                <?php if ($beneficiary['is_favorite']): ?>
                                <i class="fas fa-star favorite-star" title="Favorite"></i>
                                <?php endif; ?>
                                <span class="international-badge" title="International Wire Transfer">
                                    <i class="fas fa-globe"></i> International
                                </span>
                            </div>
                            <div class="beneficiary-account">
                                <span class="account-number"><?php echo htmlspecialchars($beneficiary['beneficiary_account_number']); ?></span>
                            </div>
                            <div class="beneficiary-bank">
                                <?php echo htmlspecialchars($beneficiary['bank_name']); ?>
                                <span class="swift-code">(<?php echo htmlspecialchars($beneficiary['swift_code']); ?>)</span>
                            </div>
                        </div>
                        <div class="beneficiary-actions">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                        id="dropdownMenuButton<?php echo $beneficiary['id']; ?>"
                                        data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton<?php echo $beneficiary['id']; ?>">
                                    <li>
                                        <a class="dropdown-item" href="../wire-transfers/?beneficiary=<?php echo $beneficiary['id']; ?>">
                                            <i class="fas fa-paper-plane me-2"></i>Send Wire Transfer
                                        </a>
                                    </li>
                                    <li>
                                        <button class="dropdown-item" type="button" onclick="editBeneficiary(<?php echo $beneficiary['id']; ?>)">
                                            <i class="fas fa-edit me-2"></i>Edit Beneficiary
                                        </button>
                                    </li>
                                    <li>
                                        <button class="dropdown-item" type="button" onclick="toggleFavorite(<?php echo $beneficiary['id']; ?>, <?php echo $beneficiary['is_favorite'] ? 0 : 1; ?>)">
                                            <i class="fas fa-star me-2"></i>
                                            <?php echo $beneficiary['is_favorite'] ? 'Remove from Favorites' : 'Add to Favorites'; ?>
                                        </button>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <button class="dropdown-item text-danger" type="button" onclick="deleteBeneficiary(<?php echo $beneficiary['id']; ?>, '<?php echo htmlspecialchars($beneficiary['name'], ENT_QUOTES); ?>')">
                                            <i class="fas fa-trash me-2"></i>Delete
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="beneficiary-details">
                        <div class="detail-item">
                            <span class="detail-label">Country:</span>
                            <span class="detail-value"><?php echo htmlspecialchars($beneficiary['bank_country']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">SWIFT Code:</span>
                            <span class="detail-value"><?php echo htmlspecialchars($beneficiary['swift_code']); ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Added:</span>
                            <span class="detail-value"><?php echo formatDate($beneficiary['created_at'], 'M j, Y'); ?></span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <?php else: ?>
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No wire beneficiaries found</h5>
                <p class="text-muted">
                    Add your first wire transfer beneficiary to start making international transfers.
                    Wire transfers allow you to send money globally with enhanced security.
                </p>
                <button class="btn btn-primary" onclick="showAddBeneficiaryModal()">
                    <i class="fas fa-plus me-2"></i>Add Your First Wire Beneficiary
                </button>
            </div>
            <?php endif; ?>
        </div>
        </div>
    </div>

    <!-- User Footer Component -->
    <?php require_once '../shared/user_footer.php'; ?>
</div>

<!-- Add Beneficiary Modal -->
<div class="modal fade" id="addBeneficiaryModal" tabindex="-1" aria-labelledby="addBeneficiaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addBeneficiaryModalLabel">
                    <i class="fas fa-plus me-2"></i>Add Wire Transfer Beneficiary
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <!-- Beneficiary Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Beneficiary Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="beneficiary_country" class="form-label">Country *</label>
                            <select class="form-select" id="beneficiary_country" name="beneficiary_country" required>
                                <option value="">Select Country</option>
                                <option value="United Kingdom">United Kingdom</option>
                                <option value="United States">United States</option>
                                <option value="Canada">Canada</option>
                                <option value="Australia">Australia</option>
                                <option value="Germany">Germany</option>
                                <option value="France">France</option>
                                <option value="Japan">Japan</option>
                                <option value="Singapore">Singapore</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="beneficiary_account_number" class="form-label">Account Number *</label>
                            <input type="text" class="form-control" id="beneficiary_account_number" name="beneficiary_account_number" required>
                        </div>
                        <div class="col-md-6">
                            <label for="beneficiary_account_name" class="form-label">Account Name *</label>
                            <input type="text" class="form-control" id="beneficiary_account_name" name="beneficiary_account_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="beneficiary_address" class="form-label">Beneficiary Address *</label>
                        <textarea class="form-control" id="beneficiary_address" name="beneficiary_address" rows="2" required></textarea>
                    </div>

                    <!-- Bank Information -->
                    <h6 class="mb-3"><i class="fas fa-university me-2"></i>Bank Information</h6>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="bank_name" class="form-label">Bank Name *</label>
                            <input type="text" class="form-control" id="bank_name" name="bank_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="swift_code" class="form-label">SWIFT Code *</label>
                            <input type="text" class="form-control" id="swift_code" name="swift_code" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="bank_city" class="form-label">Bank City *</label>
                            <input type="text" class="form-control" id="bank_city" name="bank_city" required>
                        </div>
                        <div class="col-md-6">
                            <label for="bank_country" class="form-label">Bank Country *</label>
                            <input type="text" class="form-control" id="bank_country" name="bank_country" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="bank_address" class="form-label">Bank Address *</label>
                        <textarea class="form-control" id="bank_address" name="bank_address" rows="2" required></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="routing_code" class="form-label">Routing Code</label>
                            <input type="text" class="form-control" id="routing_code" name="routing_code">
                        </div>
                        <div class="col-md-6">
                            <label for="iban" class="form-label">IBAN</label>
                            <input type="text" class="form-control" id="iban" name="iban">
                        </div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_favorite" name="is_favorite" value="1">
                        <label class="form-check-label" for="is_favorite">
                            Mark as favorite
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Beneficiary
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Beneficiary Modal -->
<div class="modal fade" id="editBeneficiaryModal" tabindex="-1" aria-labelledby="editBeneficiaryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editBeneficiaryModalLabel">
                    <i class="fas fa-edit me-2"></i>Edit Wire Transfer Beneficiary
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="beneficiary_id" id="edit_beneficiary_id">

                    <!-- Beneficiary Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_name" class="form-label">Beneficiary Name *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_beneficiary_country" class="form-label">Country *</label>
                            <select class="form-select" id="edit_beneficiary_country" name="beneficiary_country" required>
                                <option value="">Select Country</option>
                                <option value="United Kingdom">United Kingdom</option>
                                <option value="United States">United States</option>
                                <option value="Canada">Canada</option>
                                <option value="Australia">Australia</option>
                                <option value="Germany">Germany</option>
                                <option value="France">France</option>
                                <option value="Japan">Japan</option>
                                <option value="Singapore">Singapore</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_beneficiary_account_number" class="form-label">Account Number *</label>
                            <input type="text" class="form-control" id="edit_beneficiary_account_number" name="beneficiary_account_number" required>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_beneficiary_account_name" class="form-label">Account Name *</label>
                            <input type="text" class="form-control" id="edit_beneficiary_account_name" name="beneficiary_account_name" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_beneficiary_address" class="form-label">Beneficiary Address *</label>
                        <textarea class="form-control" id="edit_beneficiary_address" name="beneficiary_address" rows="2" required></textarea>
                    </div>

                    <!-- Bank Information -->
                    <h6 class="mb-3"><i class="fas fa-university me-2"></i>Bank Information</h6>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_bank_name" class="form-label">Bank Name *</label>
                            <input type="text" class="form-control" id="edit_bank_name" name="bank_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_swift_code" class="form-label">SWIFT Code *</label>
                            <input type="text" class="form-control" id="edit_swift_code" name="swift_code" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_bank_city" class="form-label">Bank City *</label>
                            <input type="text" class="form-control" id="edit_bank_city" name="bank_city" required>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_bank_country" class="form-label">Bank Country *</label>
                            <input type="text" class="form-control" id="edit_bank_country" name="bank_country" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_bank_address" class="form-label">Bank Address *</label>
                        <textarea class="form-control" id="edit_bank_address" name="bank_address" rows="2" required></textarea>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_routing_code" class="form-label">Routing Code</label>
                            <input type="text" class="form-control" id="edit_routing_code" name="routing_code">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_iban" class="form-label">IBAN</label>
                            <input type="text" class="form-control" id="edit_iban" name="iban">
                        </div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit_is_favorite" name="is_favorite" value="1">
                        <label class="form-check-label" for="edit_is_favorite">
                            Mark as favorite
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Beneficiary
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom JavaScript -->
<script>
// Show Add Beneficiary Modal
function showAddBeneficiaryModal() {
    const modal = new bootstrap.Modal(document.getElementById('addBeneficiaryModal'));
    modal.show();
}

// Edit Beneficiary Function
function editBeneficiary(id) {
    fetch('get_beneficiary.php?id=' + id)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const beneficiary = data.beneficiary;

                // Populate edit modal fields
                document.getElementById('edit_beneficiary_id').value = beneficiary.id;
                document.getElementById('edit_name').value = beneficiary.name;
                document.getElementById('edit_beneficiary_country').value = beneficiary.beneficiary_country;
                document.getElementById('edit_beneficiary_account_number').value = beneficiary.beneficiary_account_number;
                document.getElementById('edit_beneficiary_account_name').value = beneficiary.beneficiary_account_name;
                document.getElementById('edit_beneficiary_address').value = beneficiary.beneficiary_address;
                document.getElementById('edit_bank_name').value = beneficiary.bank_name;
                document.getElementById('edit_bank_address').value = beneficiary.bank_address;
                document.getElementById('edit_bank_city').value = beneficiary.bank_city;
                document.getElementById('edit_bank_country').value = beneficiary.bank_country;
                document.getElementById('edit_swift_code').value = beneficiary.swift_code;
                document.getElementById('edit_routing_code').value = beneficiary.routing_code || '';
                document.getElementById('edit_iban').value = beneficiary.iban || '';
                document.getElementById('edit_is_favorite').checked = beneficiary.is_favorite == 1;

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('editBeneficiaryModal'));
                modal.show();
            } else {
                alert('Error loading beneficiary data: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading beneficiary data');
        });
}

// Toggle Favorite Function
function toggleFavorite(id, isFavorite) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="toggle_favorite">
        <input type="hidden" name="beneficiary_id" value="${id}">
        <input type="hidden" name="is_favorite" value="${isFavorite}">
    `;
    document.body.appendChild(form);
    form.submit();
}

// Delete Beneficiary Function
function deleteBeneficiary(id, name) {
    if (confirm('Are you sure you want to delete beneficiary "' + name + '"? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_beneficiary">
            <input type="hidden" name="beneficiary_id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});
</script>

</body>
</html>
